services:
  mysql:
    image: mysql:8
    command: --mysql-native-password=ON --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --binlog-expire-logs-seconds=25920
    restart: always
    ports:
      - 3306:3306
    volumes:
      - ./data/mysql/:/var/lib/mysql/
      - ./docker-entrypoint-initdb.d/:/docker-entrypoint-initdb.d/
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: "b_Mwjf!di!rh3wf" # 配置root用户密码
      MYSQL_DATABASE: "chatgpt" # 业务库名

  redis:
    image: redis
    restart: always
    ports:
      - 6379:6379
    environment:
      TZ: Asia/Shanghai # 指定时区
    volumes:
      - ./data/redis/:/data/
    command: redis-server --appendonly yes --requirepass "QW_379JNmENHaaa"


