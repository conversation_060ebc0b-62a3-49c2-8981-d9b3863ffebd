# LabIAI 项目架构与API文档分析

## 项目概述

LabIAI 是一个基于 Spring Boot 的 AI 工具订阅服务平台，提供用户注册、登录、套餐订阅、支付、设备管理等完整功能。

### 技术栈
- **后端框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.4.5
- **缓存**: Redis
- **认证框架**: Sa-Token 1.42.0
- **ORM**: MyBatis-Plus 3.5.7
- **支付**: 虎皮椒支付
- **邮件服务**: MailGun
- **设备指纹**: FingerprintJS
- **第三方集成**: 飞书机器人

## 项目架构

### 1. 分层架构
```
├── Controller 层 - 接口控制器
├── Service 层 - 业务逻辑
├── Mapper 层 - 数据访问
├── Entity 层 - 实体类
├── DTO 层 - 数据传输对象
├── Config 层 - 配置类
├── Util 层 - 工具类
├── Exception 层 - 异常处理
└── Interceptor 层 - 拦截器
```

### 2. 核心模块

#### 用户管理模块
- **用户注册**: 基于邮箱验证码的安全注册流程
- **用户登录**: 多维度安全验证（设备指纹、IP限流、失败计数）
- **设备管理**: 设备信任度评估、异常设备检测
- **会话管理**: Sa-Token 会话管理，支持多设备登录

#### 订阅管理模块
- **套餐管理**: 多种计费周期（日/月/季/年）
- **订单管理**: 订单创建、查询、取消、删除
- **优惠码系统**: 固定金额/百分比折扣，防超售机制
- **订阅历史**: 完整的订阅变更记录

#### 支付系统模块
- **支付方式**: 支持多种支付渠道（支付宝、微信等）
- **支付流程**: 创建支付 → 跳转支付 → 回调处理
- **退款管理**: 全额/部分退款支持

#### 安全监控模块
- **安全事件**: 异常登录、设备变更等安全事件记录
- **风险评估**: 基于多维度的风险评分系统
- **审计日志**: 完整的操作审计追踪

## 数据库设计

### 核心表结构

#### 用户相关表
- `users` - 用户基础信息
- `user_devices` - 用户设备管理
- `user_subscriptions` - 用户订阅信息
- `user_third_party_accounts` - 第三方账号绑定
- `login_logs` - 登录日志
- `security_events` - 安全事件

#### 订单相关表
- `orders` - 订单信息
- `packages` - 套餐信息
- `package_prices` - 套餐价格
- `discount_codes` - 优惠码
- `discount_code_usage` - 优惠码使用记录

#### 支付相关表
- `payments` - 支付记录
- `payment_methods` - 支付方式配置
- `refunds` - 退款记录

#### 系统相关表
- `satoken_sessions` - Sa-Token 会话管理
- `subscription_history` - 订阅历史记录

## API 接口文档

### 1. 认证相关接口

#### 用户登录
- **接口**: `POST /auth/login`
- **功能**: 用户邮箱密码登录
- **安全特性**: 
  - 设备指纹验证
  - 多维度登录频率限制
  - 登录失败计数机制
  - 用户状态检查

#### 用户注册
- **接口**: `POST /register/submit`
- **功能**: 基于邮箱验证码的用户注册
- **流程**: 
  1. `POST /register/init-session` - 初始化会话
  2. `POST /register/send-verification-code` - 发送验证码
  3. `POST /register/submit` - 完成注册

#### 会话管理
- **接口**: `POST /auth/logout` - 用户注销
- **接口**: `POST /auth/logout-others` - 强制下线其他会话

### 2. 套餐相关接口

#### 套餐查询
- **接口**: `GET /packages` - 获取所有可用套餐
- **接口**: `GET /packages/{id}` - 获取套餐详情
- **接口**: `GET /packages/{id}/prices` - 获取套餐价格选项

### 3. 订单相关接口

#### 订单管理
- **接口**: `POST /orders/create` - 创建订单
- **接口**: `GET /orders` - 获取用户订单列表
- **接口**: `GET /orders/{orderId}` - 根据订单ID查询详情
- **接口**: `GET /orders/order-no/{orderNo}` - 根据订单号查询详情
- **接口**: `PUT /orders/{orderId}/cancel` - 取消订单
- **接口**: `DELETE /orders/{orderId}` - 删除订单

#### 优惠码相关
- **接口**: `POST /orders/preview-discount` - 预验证优惠码
- **接口**: `GET /orders/{orderId}/discount-info` - 获取订单优惠码详情

### 4. 支付相关接口

#### 支付流程
- **接口**: `GET /payment/methods` - 获取可用支付方式
- **接口**: `POST /payment/create` - 创建支付订单
- **接口**: `GET /payment/success` - 支付成功回跳
- **接口**: `GET /payment/cancel` - 支付取消回跳
- **接口**: `POST /payment/notify/xunhupay` - 支付回调（内部）

### 5. 用户管理接口

#### 用户信息
- **接口**: `GET /user/me` - 获取当前用户信息
- **接口**: `POST /user/change-password` - 修改密码
- **接口**: `GET /user/subscription` - 获取用户订阅信息

#### 设备管理
- **接口**: `GET /devices` - 获取用户设备列表
- **接口**: `GET /devices/{deviceId}` - 获取设备详情
- **接口**: `PUT /devices/{deviceId}/trust` - 设置设备信任状态
- **接口**: `DELETE /devices/{deviceId}` - 删除设备
- **接口**: `GET /devices/fingerprint` - 获取设备指纹信息

## 安全机制

### 1. 设备指纹验证
- 基于 FingerprintJS 生成唯一设备标识
- 双重验证：visitorId + deviceHash
- 设备信任度评估和风险评分

### 2. 多维度限流
- 用户维度限流
- IP 维度限流  
- 设备维度限流
- 基于 Redis 的滑动窗口算法

### 3. 会话安全
- JWT + Nonce 防重放机制
- 设备绑定验证
- 会话超时管理
- 并发登录控制

### 4. 数据安全
- 密码 MD5 加密存储
- 敏感操作审计日志
- 完整的错误码体系
- 统一异常处理

## 业务特色功能

### 1. 优惠码防超售
- 基于 Redis 分布式锁
- 原子性操作保证
- 多种优惠类型支持（固定金额/百分比）

### 2. 订阅管理
- 灵活的计费周期
- 自动续费支持
- 套餐升级/降级
- 订阅历史追踪

### 3. 智能风险控制
- 异地登录检测
- 异常设备识别
- 登录行为分析
- 自动安全响应

### 4. 完整的支付生态
- 多支付渠道支持
- 支付状态实时同步
- 退款流程管理
- 支付安全保障

## 部署架构

### 生产环境配置
- **应用服务器**: Spring Boot 内嵌 Tomcat
- **数据库**: MySQL 主从架构
- **缓存**: Redis 集群
- **反向代理**: Nginx
- **容器化**: Docker + Docker Compose

### 监控与日志
- **链路追踪**: 自定义 TraceId 系统
- **业务监控**: 关键业务指标监控
- **日志管理**: 结构化日志输出
- **异常告警**: 飞书机器人集成

## 详细API接口规范

### 统一响应格式
```json
{
  "code": 20000,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

### 错误码体系

#### 成功状态码 (2xxxx)
- `20000` - 操作成功
- `20001` - 创建成功
- `20002` - 更新成功
- `20003` - 删除成功

#### 客户端错误 (4xxxx)
- `40000` - 请求参数错误
- `40001` - 参数校验失败
- `40100` - 未授权访问
- `40101` - Token已过期
- `40102` - Token无效
- `40103` - 缺少Token
- `40104` - 请先登录
- `40300` - 访问被禁止
- `40301` - 权限不足
- `40302` - 账号已被禁用
- `40303` - 账号已被锁定

#### 限流错误 (429xx)
- `42900` - 请求过于频繁
- `42901` - 登录尝试次数过多
- `42902` - 短信发送次数超限

#### 业务错误 (6xxxx)
- `60101` - 用户名或密码错误
- `60104` - 两次输入的密码不一致
- `60105` - 邮箱未验证
- `60202` - 验证码错误
- `60301` - 设备未受信任
- `60305` - 缺少设备指纹
- `60307` - 设备不匹配
- `60405` - nonce无效

#### 服务端错误 (5xxxx)
- `50000` - 服务器内部错误
- `50001` - 数据库错误
- `50002` - 缓存错误

### 认证机制详解

#### Sa-Token 认证
- **Token名称**: `access_token`
- **有效期**: 30天（2592000秒）
- **存储方式**: Cookie + Redis
- **并发控制**: 允许多设备同时登录

#### 设备指纹机制
```javascript
// 设备指纹生成示例
const deviceInfo = {
  visitorId: "32位十六进制字符串",
  deviceHash: "SHA-256哈希值",
  userAgent: "浏览器用户代理",
  screenResolution: "屏幕分辨率",
  timezone: "时区信息"
}
```

### 支付流程详解

#### 1. 支付创建流程
```
用户选择套餐 → 创建订单 → 选择支付方式 → 创建支付 → 跳转支付页面
```

#### 2. 支付回调处理
```
支付平台回调 → 验证签名 → 更新支付状态 → 更新订单状态 → 创建/更新订阅
```

#### 3. 支付状态流转
```
PENDING → SUCCESS/FAILED/CANCELLED/EXPIRED
```

### 订阅业务规则

#### 订阅状态管理
- `ACTIVE` - 活跃订阅
- `EXPIRED` - 已过期
- `CANCELLED` - 已取消

#### 套餐升级规则
- 支持套餐升级/降级
- 按比例计算剩余时间
- 自动处理价格差额

#### 自动续费机制
- 到期前7天、3天、1天提醒
- 宽限期3天
- 自动扣费失败处理

### 优惠码系统详解

#### 优惠码类型
- `FIXED` - 固定金额折扣
- `PERCENTAGE` - 百分比折扣

#### 使用规则
- `SINGLE` - 一码一用
- `MULTIPLE` - 一码多用
- 最小使用金额限制
- 最大优惠金额限制
- 适用套餐限制

#### 防超售机制
```java
// Redis分布式锁实现
String lockKey = "discount_code_lock:" + discountCodeId;
Boolean lockAcquired = redisTemplate.opsForValue()
    .setIfAbsent(lockKey, "1", Duration.ofSeconds(10));
```

### 安全事件监控

#### 事件级别
- `INFO` - 信息级别
- `WARNING` - 警告级别
- `DANGER` - 危险级别
- `CRITICAL` - 严重级别

#### 监控维度
- 异地登录检测
- 异常设备识别
- 登录失败统计
- 支付异常监控

### 第三方集成

#### 飞书机器人集成
- 用户注册通知
- 登录异常告警
- 支付成功通知
- 系统错误告警

#### MailGun邮件服务
- 注册验证码发送
- 密码重置邮件
- 订阅到期提醒
- 支付成功确认

### 性能优化策略

#### 缓存策略
- Redis缓存用户会话
- 套餐信息缓存
- 设备指纹缓存
- 限流计数器缓存

#### 数据库优化
- 合理的索引设计
- 分页查询优化
- 慢查询监控
- 读写分离支持

### 运维监控

#### 链路追踪
- 全链路TraceId追踪
- 关键业务节点监控
- 异常堆栈收集
- 性能指标统计

#### 日志管理
- 结构化日志输出
- 日志级别控制
- 日志文件轮转
- 敏感信息脱敏

这个项目展现了一个完整的 SaaS 订阅服务平台的架构设计，具备了企业级应用所需的安全性、可扩展性和可维护性特征。
