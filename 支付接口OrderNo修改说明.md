# 支付接口OrderNo修改说明

## 📋 修改概述

将支付相关接口从使用 `orderId` 改为使用 `orderNo`，提升系统安全性，避免暴露数据库自增主键。

## 🔄 具体修改内容

### 1. 创建支付记录接口

**接口**: `POST /payment/create/{orderNo}`

#### 修改前
```java
@PostMapping("/create/{orderId}")
public ApiResponse<PaymentCreateResponseDTO> createPayment(@PathVariable Long orderId, ...)
```

#### 修改后
```java
@PostMapping("/create/{orderNo}")
public ApiResponse<PaymentCreateResponseDTO> createPayment(@PathVariable String orderNo, ...)
```

#### 参数变化
- **路径参数**: `orderId` → `orderNo`
- **参数类型**: `Long` → `String`
- **示例**: `/payment/create/123` → `/payment/create/ORD20250617030822711E44`

#### 前端调用示例
```javascript
// 修改前
const createPayment = async (orderId, paymentMethod) => {
  const response = await fetch(`/payment/create/${orderId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `paymentMethodCode=${paymentMethod}&clientIp=127.0.0.1`,
    credentials: 'include'
  });
  return response.json();
};

// 修改后
const createPayment = async (orderNo, paymentMethod) => {
  const response = await fetch(`/payment/create/${orderNo}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `paymentMethodCode=${paymentMethod}&clientIp=127.0.0.1`,
    credentials: 'include'
  });
  return response.json();
};
```

### 2. 查询支付状态接口

#### 新增接口: 通过订单号查询
**接口**: `GET /payment/order-no/{orderNo}/status`

```java
@GetMapping("/order-no/{orderNo}/status")
@SaCheckLogin
public ApiResponse<PaymentStatusDTO> queryPaymentStatusByOrderNo(@PathVariable String orderNo)
```

#### 保留接口: 通过订单ID查询（兼容性）
**接口**: `GET /payment/order/{orderId}/status`

```java
@GetMapping("/order/{orderId}/status")
@SaCheckLogin
public ApiResponse<PaymentStatusDTO> queryPaymentStatusByOrderId(@PathVariable Long orderId)
```

#### 前端调用示例
```javascript
// 推荐使用：通过订单号查询
const queryPaymentStatus = async (orderNo) => {
  const response = await fetch(`/payment/order-no/${orderNo}/status`, {
    credentials: 'include'
  });
  return response.json();
};

// 兼容方式：通过订单ID查询（不推荐）
const queryPaymentStatusById = async (orderId) => {
  const response = await fetch(`/payment/order/${orderId}/status`, {
    credentials: 'include'
  });
  return response.json();
};
```

### 3. Service层修改

#### PaymentService.createPayment()
```java
// 修改前
public ApiResponse<PaymentCreateResponseDTO> createPayment(Long orderId, ...)

// 修改后
public ApiResponse<PaymentCreateResponseDTO> createPayment(String orderNo, ...)
```

#### 新增方法
```java
// 新增：通过订单号查询支付状态
public ApiResponse<PaymentStatusDTO> queryPaymentStatusByOrderNo(String orderNo)
```

## 📊 接口响应格式

### 创建支付记录响应
```json
{
  "success": true,
  "code": 200,
  "message": "支付订单创建成功",
  "data": {
    "paymentNo": "PAY20250617030822ABC123",
    "paymentUrl": "https://api.xunhupay.com/payment/do.html?id=xxx",
    "qrCodeUrl": "https://api.xunhupay.com/payment/qrcode.html?id=xxx",
    "amount": 89.00,
    "expiredAt": "2025-06-17 03:13:22"
  },
  "traceId": "trace_id_here"
}
```

### 查询支付状态响应
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "paymentNo": "PAY20250617030822ABC123",
    "status": "SUCCESS",
    "amount": 89.00,
    "paidAt": "2025-06-17 03:08:45",
    "createdAt": "2025-06-17 03:08:22"
  },
  "traceId": "trace_id_here"
}
```

## 🔒 安全性改进

### 1. 权限验证增强
```java
// 通过订单号查询时，内部验证订单所有权
ApiResponse<OrderDTO> orderResponse = orderService.getOrderByOrderNo(orderNo);
if (!orderResponse.getSuccess() || orderResponse.getData() == null) {
    throw new AuthorizationException("无权查看此订单的支付状态");
}
```

### 2. 参数验证
```java
// 订单号验证
if (StrUtil.isBlank(orderNo)) {
    throw ValidationException.invalidParameter("orderNo", orderNo);
}
```

### 3. 错误处理
```java
// 订单不存在
if (order == null) {
    throw OrderException.orderNotFound(orderNo);
}
```

## 🧪 测试用例

### 测试用例1: 创建支付记录
```bash
# 通过订单号创建支付
curl -X POST "http://localhost:8080/payment/create/ORD20250617030822711E44" \
  -H "Cookie: access_token=test_token" \
  -d "paymentMethodCode=xunhupay_wechat&clientIp=127.0.0.1"
```

**期望结果**: 返回支付创建成功响应

### 测试用例2: 查询支付状态
```bash
# 通过订单号查询支付状态
curl -X GET "http://localhost:8080/payment/order-no/ORD20250617030822711E44/status" \
  -H "Cookie: access_token=test_token"
```

**期望结果**: 返回支付状态信息

### 测试用例3: 错误处理
```bash
# 查询不存在的订单
curl -X GET "http://localhost:8080/payment/order-no/INVALID_ORDER_NO/status" \
  -H "Cookie: access_token=test_token"
```

**期望结果**: 返回订单不存在错误

## 🔄 前端适配指南

### 1. 支付流程适配
```javascript
// 完整的支付流程
const handlePayment = async (orderNo, paymentMethod) => {
  try {
    // 1. 创建支付
    const paymentResult = await createPayment(orderNo, paymentMethod);
    if (!paymentResult.success) {
      throw new Error(paymentResult.message);
    }

    // 2. 跳转支付页面
    window.location.href = paymentResult.data.paymentUrl;

    // 3. 支付完成后查询状态（轮询）
    const checkPaymentStatus = async () => {
      const statusResult = await queryPaymentStatus(orderNo);
      if (statusResult.success && statusResult.data.status === 'SUCCESS') {
        // 支付成功，跳转成功页面
        window.location.href = '/payment/success';
      } else if (statusResult.data.status === 'FAILED') {
        // 支付失败
        alert('支付失败，请重试');
      } else {
        // 继续轮询
        setTimeout(checkPaymentStatus, 2000);
      }
    };

    // 开始轮询（在支付页面返回后）
    setTimeout(checkPaymentStatus, 1000);

  } catch (error) {
    console.error('支付流程错误:', error);
    alert('支付失败: ' + error.message);
  }
};
```

### 2. 错误处理适配
```javascript
const handlePaymentError = (error) => {
  switch (error.code) {
    case 40004:
      alert('订单不存在或已过期');
      break;
    case 40005:
      alert('订单状态不允许支付');
      break;
    case 40003:
      alert('无权操作此订单');
      break;
    default:
      alert('支付失败: ' + error.message);
  }
};
```

## 📋 兼容性说明

### 保留的接口
- `GET /payment/order/{orderId}/status` - 通过订单ID查询支付状态

### 推荐使用的接口
- `POST /payment/create/{orderNo}` - 通过订单号创建支付
- `GET /payment/order-no/{orderNo}/status` - 通过订单号查询支付状态

### 迁移建议
1. **立即迁移**: 创建支付接口改为使用订单号
2. **逐步迁移**: 支付状态查询可以逐步从订单ID迁移到订单号
3. **最终目标**: 完全使用订单号，移除订单ID相关接口

## 🔍 日志记录

### 新增日志事件
- `ORDER_PAYMENT_STATUS_QUERY_BY_ORDER_NO`: 通过订单号查询支付状态
- `QUERY_ORDER_PAYMENT_STATUS_BY_ORDER_NO_FAILED`: 通过订单号查询失败

### 修改的日志事件
- `PAYMENT_CREATE_API_SUCCESS`: 记录订单号而不是订单ID
- `PAYMENT_CREATE_API_FAILED`: 记录订单号而不是订单ID

## ✅ 修改验证

### 功能验证
- [x] 通过订单号创建支付记录
- [x] 通过订单号查询支付状态
- [x] 权限验证正常工作
- [x] 错误处理正确

### 安全验证
- [x] 不暴露数据库自增ID
- [x] 订单所有权验证有效
- [x] 参数验证完整

### 兼容性验证
- [x] 原有接口仍然可用
- [x] 新接口正常工作
- [x] 前端可以平滑迁移

这些修改成功将支付接口从使用订单ID改为使用订单号，提升了系统安全性，同时保持了良好的兼容性和用户体验。
