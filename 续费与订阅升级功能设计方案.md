# 续费与订阅升级功能设计方案

## 方案概述

基于现有的订阅页面功能（拉取订阅、拉取套餐），设计并实现续费和订阅升级功能。本方案将详细说明前端交互流程、后端接口设计和业务逻辑实现。

**设计原则**：
- 🚫 不改动现有数据库结构
- 🚫 不实现自动续费功能
- ✅ 只考虑续费和升级功能（不考虑降级）
- ✅ 复用现有的控制器结构
- ✅ 基于现有的Service层逻辑

## 现状分析

### 前端现有功能
- ✅ 拉取用户当前订阅信息 (`GET /user/subscription`)
- ✅ 拉取可用套餐列表 (`GET /packages`)
- ❌ 续费功能（缺失）
- ❌ 订阅升级功能（缺失）

### 后端现有接口和Service
- ✅ `GET /user/subscription` - 获取用户订阅信息
- ✅ `GET /packages` - 获取套餐列表
- ✅ `POST /orders/create` - 创建订单（已支持order_type）
- ✅ `POST /payment/create` - 创建支付
- ✅ `UserSubscriptionService.processSubscription()` - 已有续费/升级逻辑
- ❌ 续费预览接口（缺失）
- ❌ 升级预览接口（缺失）

### 现有数据库结构（无需修改）
- ✅ `orders.order_type` 字段已存在（NEW/RENEWAL/UPGRADE）
- ✅ `subscription_history` 表已存在
- ✅ `user_subscriptions.auto_renewal` 字段已存在（但不使用）

## 功能需求分析

### 1. 续费功能需求

#### 1.1 业务场景
- 用户订阅即将到期（7天内）
- 用户订阅已过期但在宽限期内（3天）
- 用户主动续费

#### 1.2 前端交互需求
- 显示续费提醒和倒计时
- 提供续费按钮和续费选项
- 支持选择续费周期（月付/年付）
- 支持应用优惠码
- 显示续费价格计算

#### 1.3 业务规则
- 续费从当前订阅到期时间开始计算
- 如果订阅已过期，从续费时间开始计算
- 续费可以应用优惠码
- 续费成功后自动延长订阅时间

### 2. 订阅升级功能需求

#### 2.1 业务场景
- 用户希望升级到更高级的套餐
- 用户希望降级到更低级的套餐
- 用户希望更改计费周期

#### 2.2 前端交互需求
- 显示当前套餐和可升级套餐
- 计算升级费用和剩余时间价值
- 提供升级确认和支付流程
- 显示升级后的权益变化

#### 2.3 业务规则
- 升级立即生效
- 按比例计算当前套餐剩余价值
- 补差价或退差价
- 降级在下个计费周期生效

## 后端接口设计

### 1. 续费相关接口（基于现有控制器）

#### 1.1 获取续费选项
**接口**: `GET /packages/{packageId}/renewal-options`
**控制器**: `PackageController`

**描述**: 获取指定套餐的续费选项和价格（基于用户当前订阅）

**请求参数**:
- `packageId`: 当前订阅的套餐ID

**响应示例**:
```json
{
  "code": 20000,
  "message": "获取续费选项成功",
  "data": {
    "currentSubscription": {
      "subscriptionId": 123,
      "packageName": "基础版",
      "endTime": "2024-12-25T14:30:25",
      "status": "ACTIVE",
      "daysRemaining": 5,
      "isExpiringSoon": true
    },
    "renewalOptions": [
      {
        "packagePriceId": 1,
        "billingCycle": "MONTH",
        "cycleCount": 1,
        "originalPrice": 99.00,
        "salePrice": 89.00,
        "currency": "CNY",
        "displayText": "续费1个月",
        "newEndTime": "2025-01-25T14:30:25"
      },
      {
        "packagePriceId": 2,
        "billingCycle": "YEAR",
        "cycleCount": 1,
        "originalPrice": 1188.00,
        "salePrice": 899.00,
        "currency": "CNY",
        "displayText": "续费1年",
        "newEndTime": "2025-12-25T14:30:25"
      }
    ]
  }
}
```

#### 1.2 预览续费价格
**接口**: `POST /orders/preview-renewal`
**控制器**: `OrderController`

**描述**: 预览续费价格，支持优惠码（复用现有优惠码预览逻辑）

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
packagePriceId=1&discountCode=RENEW20
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "续费预览成功",
  "data": {
    "packagePriceId": 1,
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "billingInfo": "续费1个月",
    "currentEndTime": "2024-12-25T14:30:25",
    "newEndTime": "2025-01-25T14:30:25",
    "discountInfo": {
      "discountCode": "RENEW20",
      "discountType": "PERCENTAGE",
      "discountValue": 20.00
    }
  }
}
```

#### 1.3 创建续费订单
**接口**: `POST /orders/create-renewal`
**控制器**: `OrderController`

**描述**: 创建续费订单（基于现有订单创建逻辑，设置orderType为RENEWAL）

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
packagePriceId=1&discountCode=RENEW20
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "续费订单创建成功",
  "data": {
    "orderId": 12346,
    "orderNo": "RNW20241220143025ABC124",
    "packageName": "基础版",
    "billingInfo": "续费1个月",
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-20T15:00:25"
  }
}
```

### 2. 订阅升级相关接口（基于现有控制器）

#### 2.1 获取升级选项
**接口**: `GET /packages/upgrade-options`
**控制器**: `PackageController`

**描述**: 获取当前订阅可升级的套餐选项（只返回比当前套餐更高级的选项）

**响应示例**:
```json
{
  "code": 20000,
  "message": "获取升级选项成功",
  "data": {
    "currentSubscription": {
      "subscriptionId": 123,
      "packageId": 1,
      "packageName": "基础版",
      "endTime": "2025-01-25T14:30:25",
      "remainingDays": 36,
      "remainingValue": 107.40
    },
    "upgradeOptions": [
      {
        "packageId": 2,
        "packageName": "专业版",
        "description": "更多AI对话次数和高级功能",
        "features": [
          "每月500次AI对话",
          "高级模型访问",
          "优先客服支持"
        ],
        "prices": [
          {
            "packagePriceId": 3,
            "billingCycle": "MONTH",
            "originalPrice": 199.00,
            "salePrice": 179.00,
            "displayText": "专业版月付"
          }
        ],
        "upgradeType": "UPGRADE",
        "estimatedCost": 71.60
      }
    ]
  }
}
```

#### 2.2 预览升级价格
**接口**: `POST /subscription/preview-upgrade`

**描述**: 预览升级价格和时间计算

**请求参数**:
```json
{
  "targetPackageId": 2,
  "targetPackagePriceId": 3,
  "discountCode": "UPGRADE15"
}
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "升级预览成功",
  "data": {
    "upgradeType": "UPGRADE",
    "currentPackage": {
      "packageId": 1,
      "packageName": "基础版",
      "remainingDays": 36,
      "remainingValue": 107.40
    },
    "targetPackage": {
      "packageId": 2,
      "packageName": "专业版",
      "packagePriceId": 3,
      "monthlyPrice": 179.00
    },
    "calculation": {
      "remainingValue": 107.40,
      "targetMonthlyPrice": 179.00,
      "priceDifference": 71.60,
      "discountAmount": 10.74,
      "finalAmount": 60.86,
      "newEndTime": "2025-01-25T14:30:25"
    },
    "discountInfo": {
      "discountCode": "UPGRADE15",
      "discountType": "PERCENTAGE",
      "discountValue": 15.00
    }
  }
}
```

#### 2.3 创建升级订单
**接口**: `POST /subscription/create-upgrade-order`

**描述**: 创建升级订单

**请求参数**:
```json
{
  "targetPackageId": 2,
  "targetPackagePriceId": 3,
  "discountCode": "UPGRADE15"
}
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "升级订单创建成功",
  "data": {
    "orderId": 12347,
    "orderNo": "UPG20241220143025ABC125",
    "orderType": "UPGRADE",
    "fromPackageName": "基础版",
    "toPackageName": "专业版",
    "finalAmount": 60.86,
    "currency": "CNY",
    "status": "PENDING",
    "effectiveImmediately": true,
    "expiredAt": "2024-12-20T15:00:25"
  }
}
```

### 3. 订阅管理相关接口

#### 3.1 获取订阅详情（增强版）
**接口**: `GET /subscription/details`

**描述**: 获取详细的订阅信息，包含续费和升级提示

**响应示例**:
```json
{
  "code": 20000,
  "message": "获取订阅详情成功",
  "data": {
    "subscription": {
      "subscriptionId": 123,
      "packageId": 1,
      "packageName": "基础版",
      "status": "ACTIVE",
      "startTime": "2024-12-20T14:30:25",
      "endTime": "2025-01-25T14:30:25",
      "autoRenewal": false,
      "daysRemaining": 36,
      "isExpiringSoon": false,
      "isInGracePeriod": false
    },
    "usage": {
      "aiConversations": 75,
      "maxConversations": 100,
      "usagePercent": 75,
      "resetDate": "2025-01-01T00:00:00"
    },
    "notifications": {
      "renewalReminder": false,
      "upgradeRecommendation": true,
      "usageWarning": false
    },
    "quickActions": {
      "canRenew": true,
      "canUpgrade": true,
      "canDowngrade": true,
      "canCancel": true
    }
  }
}
```

#### 3.2 设置自动续费
**接口**: `PUT /subscription/auto-renewal`

**描述**: 开启或关闭自动续费

**请求参数**:
```json
{
  "autoRenewal": true,
  "renewalPackagePriceId": 1
}
```

#### 3.3 取消订阅
**接口**: `POST /subscription/cancel`

**描述**: 取消订阅（在下个计费周期生效）

**请求参数**:
```json
{
  "reason": "不再需要",
  "feedback": "功能不够用"
}
```

## 前端页面设计建议

### 1. 订阅管理页面布局

```
┌─────────────────────────────────────┐
│ 当前订阅状态                          │
│ ┌─────────────────────────────────┐ │
│ │ 基础版 | 剩余36天 | 到期提醒     │ │
│ │ [续费] [升级] [设置]            │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 使用情况                            │
│ ┌─────────────────────────────────┐ │
│ │ AI对话: 75/100 (75%)           │ │
│ │ ████████████░░░░                │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 推荐升级                            │
│ ┌─────────────────────────────────┐ │
│ │ 专业版 - 更多对话次数            │ │
│ │ [立即升级]                      │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 2. 续费弹窗设计

```
┌─────────────────────────────────────┐
│ 续费订阅                            │
│                                     │
│ 当前套餐: 基础版                    │
│ 到期时间: 2024-12-25               │
│                                     │
│ 续费选项:                           │
│ ○ 1个月 - ¥89                      │
│ ● 1年 - ¥899 (省¥179)             │
│                                     │
│ 优惠码: [输入优惠码] [验证]          │
│                                     │
│ 续费后到期: 2025-12-25              │
│ 支付金额: ¥899                      │
│                                     │
│ [取消] [确认续费]                   │
└─────────────────────────────────────┘
```

### 3. 升级弹窗设计

```
┌─────────────────────────────────────┐
│ 升级订阅                            │
│                                     │
│ 当前: 基础版 (剩余36天)             │
│ 升级到: 专业版                      │
│                                     │
│ 权益对比:                           │
│ • AI对话: 100次 → 500次            │
│ • 模型访问: 基础 → 高级             │
│ • 客服支持: 邮件 → 优先             │
│                                     │
│ 费用计算:                           │
│ 剩余价值: ¥107.40                  │
│ 升级差价: ¥71.60                   │
│ 优惠折扣: -¥10.74                  │
│ 实付金额: ¥60.86                   │
│                                     │
│ [取消] [确认升级]                   │
└─────────────────────────────────────┘
```

## 后端实现要点

### 1. 数据库设计调整

#### 1.1 订单表增加字段
```sql
ALTER TABLE orders ADD COLUMN order_type ENUM('NEW','RENEWAL','UPGRADE','DOWNGRADE') DEFAULT 'NEW';
ALTER TABLE orders ADD COLUMN from_subscription_id BIGINT NULL;
ALTER TABLE orders ADD COLUMN target_package_id BIGINT NULL;
```

#### 1.2 订阅历史表完善
```sql
-- 已存在，确保包含所有必要字段
-- subscription_history 表记录所有订阅变更
```

### 2. 核心业务逻辑

#### 2.1 续费逻辑
```java
public class SubscriptionRenewalService {
    
    // 计算续费价格
    public RenewalPreview calculateRenewalPrice(Long userId, Long packagePriceId, String discountCode) {
        // 1. 获取当前订阅
        // 2. 获取续费套餐价格
        // 3. 应用优惠码
        // 4. 计算新的到期时间
        // 5. 返回预览信息
    }
    
    // 执行续费
    public void processRenewal(Long orderId) {
        // 1. 验证订单状态
        // 2. 更新订阅到期时间
        // 3. 记录订阅历史
        // 4. 发送续费成功通知
    }
}
```

#### 2.2 升级逻辑
```java
public class SubscriptionUpgradeService {
    
    // 计算升级价格
    public UpgradePreview calculateUpgradePrice(Long userId, Long targetPackageId, String discountCode) {
        // 1. 获取当前订阅和剩余价值
        // 2. 获取目标套餐价格
        // 3. 计算差价
        // 4. 应用优惠码
        // 5. 返回预览信息
    }
    
    // 执行升级
    public void processUpgrade(Long orderId) {
        // 1. 验证订单状态
        // 2. 更新订阅套餐
        // 3. 调整到期时间
        // 4. 记录订阅历史
        // 5. 发送升级成功通知
    }
}
```

### 3. 定时任务

#### 3.1 到期提醒任务
```java
@Scheduled(cron = "0 0 9 * * ?") // 每天9点执行
public void sendExpirationReminders() {
    // 查询7天、3天、1天内到期的订阅
    // 发送邮件提醒
    // 推送站内消息
}
```

#### 3.2 自动续费任务
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天2点执行
public void processAutoRenewals() {
    // 查询开启自动续费且即将到期的订阅
    // 自动创建续费订单
    // 发起支付
}
```

## 开发优先级建议

### Phase 1: 基础续费功能
1. 续费选项接口
2. 续费预览接口
3. 续费订单创建
4. 前端续费弹窗

### Phase 2: 升级功能
1. 升级选项接口
2. 升级预览接口
3. 升级订单创建
4. 前端升级弹窗

### Phase 3: 增强功能
1. 自动续费设置
2. 订阅取消功能
3. 到期提醒
4. 使用情况统计

## 技术实现细节

### 1. Controller 层实现

#### 1.1 SubscriptionController 新增方法
```java
@RestController
@RequestMapping("/subscription")
@RequiredArgsConstructor
@Slf4j
public class SubscriptionController {

    private final ISubscriptionService subscriptionService;
    private final IOrderService orderService;

    /**
     * 获取续费选项
     */
    @GetMapping("/renewal-options")
    @SaCheckLogin
    public ApiResponse<RenewalOptionsDTO> getRenewalOptions() {
        Long userId = StpUtil.getLoginIdAsLong();
        RenewalOptionsDTO options = subscriptionService.getRenewalOptions(userId);
        return ApiResponse.success(options, "获取续费选项成功");
    }

    /**
     * 预览续费价格
     */
    @PostMapping("/preview-renewal")
    @SaCheckLogin
    public ApiResponse<RenewalPreviewDTO> previewRenewal(@Valid @RequestBody RenewalPreviewRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        RenewalPreviewDTO preview = subscriptionService.previewRenewal(userId, request);
        return ApiResponse.success(preview, "续费预览成功");
    }

    /**
     * 创建续费订单
     */
    @PostMapping("/create-renewal-order")
    @SaCheckLogin
    public ApiResponse<OrderDTO> createRenewalOrder(@Valid @RequestBody CreateRenewalOrderRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        OrderDTO order = subscriptionService.createRenewalOrder(userId, request);
        return ApiResponse.success(order, "续费订单创建成功");
    }

    /**
     * 获取升级选项
     */
    @GetMapping("/upgrade-options")
    @SaCheckLogin
    public ApiResponse<UpgradeOptionsDTO> getUpgradeOptions() {
        Long userId = StpUtil.getLoginIdAsLong();
        UpgradeOptionsDTO options = subscriptionService.getUpgradeOptions(userId);
        return ApiResponse.success(options, "获取升级选项成功");
    }

    /**
     * 预览升级价格
     */
    @PostMapping("/preview-upgrade")
    @SaCheckLogin
    public ApiResponse<UpgradePreviewDTO> previewUpgrade(@Valid @RequestBody UpgradePreviewRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        UpgradePreviewDTO preview = subscriptionService.previewUpgrade(userId, request);
        return ApiResponse.success(preview, "升级预览成功");
    }

    /**
     * 创建升级订单
     */
    @PostMapping("/create-upgrade-order")
    @SaCheckLogin
    public ApiResponse<OrderDTO> createUpgradeOrder(@Valid @RequestBody CreateUpgradeOrderRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        OrderDTO order = subscriptionService.createUpgradeOrder(userId, request);
        return ApiResponse.success(order, "升级订单创建成功");
    }

    /**
     * 设置自动续费
     */
    @PutMapping("/auto-renewal")
    @SaCheckLogin
    public ApiResponse<Void> setAutoRenewal(@Valid @RequestBody AutoRenewalRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        subscriptionService.setAutoRenewal(userId, request);
        return ApiResponse.success(null, "自动续费设置成功");
    }

    /**
     * 取消订阅
     */
    @PostMapping("/cancel")
    @SaCheckLogin
    public ApiResponse<Void> cancelSubscription(@Valid @RequestBody CancelSubscriptionRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        subscriptionService.cancelSubscription(userId, request);
        return ApiResponse.success(null, "订阅取消成功");
    }
}
```

### 2. Service 层核心逻辑

#### 2.1 续费服务实现
```java
@Service
@RequiredArgsConstructor
@Transactional
public class SubscriptionRenewalService {

    private final UserSubscriptionMapper subscriptionMapper;
    private final PackagePriceMapper packagePriceMapper;
    private final IDiscountCodeService discountCodeService;
    private final IOrderService orderService;

    /**
     * 计算续费价格
     */
    public RenewalPreviewDTO calculateRenewalPrice(Long userId, RenewalPreviewRequest request) {
        // 1. 获取当前订阅
        UserSubscription currentSubscription = getCurrentActiveSubscription(userId);
        if (currentSubscription == null) {
            throw new SubscriptionException("用户没有活跃订阅");
        }

        // 2. 获取续费套餐价格
        PackagePrice packagePrice = packagePriceMapper.selectById(request.getPackagePriceId());
        if (packagePrice == null || packagePrice.getStatus() != 1) {
            throw new PackageException("套餐价格不存在或已禁用");
        }

        // 3. 验证是否为同一套餐
        if (!packagePrice.getPackageId().equals(currentSubscription.getPackageId())) {
            throw new SubscriptionException("续费套餐必须与当前套餐一致");
        }

        // 4. 计算基础价格
        BigDecimal originalAmount = packagePrice.getSalePrice();
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal finalAmount = originalAmount;

        // 5. 应用优惠码
        if (StringUtils.hasText(request.getDiscountCode())) {
            DiscountCodeValidationDTO validation = discountCodeService.validateDiscountCode(
                request.getDiscountCode(), request.getPackagePriceId(), userId);

            if (validation.isValid()) {
                discountAmount = validation.getDiscountAmount();
                finalAmount = originalAmount.subtract(discountAmount);
            }
        }

        // 6. 计算新的到期时间
        LocalDateTime currentEndTime = currentSubscription.getEndTime();
        LocalDateTime newEndTime = calculateNewEndTime(currentEndTime, packagePrice);

        // 7. 构建预览结果
        return RenewalPreviewDTO.builder()
            .packagePriceId(request.getPackagePriceId())
            .originalAmount(originalAmount)
            .discountAmount(discountAmount)
            .finalAmount(finalAmount)
            .currency(packagePrice.getCurrency())
            .billingInfo(buildBillingInfo(packagePrice))
            .currentEndTime(currentEndTime)
            .newEndTime(newEndTime)
            .discountInfo(buildDiscountInfo(request.getDiscountCode()))
            .build();
    }

    /**
     * 计算新的到期时间
     */
    private LocalDateTime calculateNewEndTime(LocalDateTime currentEndTime, PackagePrice packagePrice) {
        LocalDateTime baseTime = currentEndTime.isAfter(LocalDateTime.now()) ?
            currentEndTime : LocalDateTime.now();

        return switch (packagePrice.getBillingCycle()) {
            case DAY -> baseTime.plusDays(packagePrice.getCycleCount());
            case MONTH -> baseTime.plusMonths(packagePrice.getCycleCount());
            case QUARTER -> baseTime.plusMonths(packagePrice.getCycleCount() * 3);
            case YEAR -> baseTime.plusYears(packagePrice.getCycleCount());
        };
    }

    /**
     * 执行续费
     */
    public void processRenewal(Long orderId) {
        Order order = orderService.getById(orderId);
        if (order == null || !OrderStatusEnum.PAID.equals(order.getStatus())) {
            throw new OrderException("订单不存在或状态异常");
        }

        if (!OrderType.RENEWAL.equals(order.getOrderType())) {
            throw new OrderException("订单类型不是续费订单");
        }

        // 1. 获取当前订阅
        UserSubscription subscription = subscriptionMapper.selectById(order.getFromSubscriptionId());

        // 2. 获取套餐价格信息
        PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());

        // 3. 计算新的到期时间
        LocalDateTime newEndTime = calculateNewEndTime(subscription.getEndTime(), packagePrice);

        // 4. 更新订阅
        subscription.setEndTime(newEndTime);
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        subscription.setUpdatedAt(LocalDateTime.now());
        subscriptionMapper.updateById(subscription);

        // 5. 记录订阅历史
        recordSubscriptionHistory(subscription, order, SubscriptionAction.RENEW);

        // 6. 发送续费成功通知
        sendRenewalSuccessNotification(subscription, order);
    }
}
```

#### 2.2 升级服务实现
```java
@Service
@RequiredArgsConstructor
@Transactional
public class SubscriptionUpgradeService {

    /**
     * 计算升级价格
     */
    public UpgradePreviewDTO calculateUpgradePrice(Long userId, UpgradePreviewRequest request) {
        // 1. 获取当前订阅
        UserSubscription currentSubscription = getCurrentActiveSubscription(userId);
        if (currentSubscription == null) {
            throw new SubscriptionException("用户没有活跃订阅");
        }

        // 2. 获取目标套餐价格
        PackagePrice targetPackagePrice = packagePriceMapper.selectById(request.getTargetPackagePriceId());
        if (targetPackagePrice == null) {
            throw new PackageException("目标套餐价格不存在");
        }

        // 3. 计算当前订阅剩余价值
        BigDecimal remainingValue = calculateRemainingValue(currentSubscription);

        // 4. 计算目标套餐月均价格
        BigDecimal targetMonthlyPrice = calculateMonthlyPrice(targetPackagePrice);

        // 5. 计算差价
        BigDecimal priceDifference = targetMonthlyPrice.subtract(
            calculateMonthlyPrice(getCurrentPackagePrice(currentSubscription)));

        // 6. 计算剩余天数对应的差价
        long remainingDays = ChronoUnit.DAYS.between(LocalDateTime.now(), currentSubscription.getEndTime());
        BigDecimal upgradeCost = priceDifference.multiply(BigDecimal.valueOf(remainingDays))
            .divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);

        // 7. 应用优惠码
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (StringUtils.hasText(request.getDiscountCode())) {
            // 优惠码逻辑
        }

        BigDecimal finalAmount = upgradeCost.subtract(discountAmount);

        return UpgradePreviewDTO.builder()
            .upgradeType(UpgradeType.UPGRADE)
            .currentPackage(buildCurrentPackageInfo(currentSubscription))
            .targetPackage(buildTargetPackageInfo(targetPackagePrice))
            .calculation(buildCalculationInfo(remainingValue, targetMonthlyPrice, priceDifference, finalAmount))
            .build();
    }

    /**
     * 计算剩余价值
     */
    private BigDecimal calculateRemainingValue(UserSubscription subscription) {
        PackagePrice currentPrice = getCurrentPackagePrice(subscription);
        BigDecimal monthlyPrice = calculateMonthlyPrice(currentPrice);

        long remainingDays = ChronoUnit.DAYS.between(LocalDateTime.now(), subscription.getEndTime());

        return monthlyPrice.multiply(BigDecimal.valueOf(remainingDays))
            .divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
    }

    /**
     * 执行升级
     */
    public void processUpgrade(Long orderId) {
        Order order = orderService.getById(orderId);
        if (order == null || !OrderStatusEnum.PAID.equals(order.getStatus())) {
            throw new OrderException("订单不存在或状态异常");
        }

        // 1. 获取当前订阅
        UserSubscription subscription = subscriptionMapper.selectById(order.getFromSubscriptionId());

        // 2. 更新订阅套餐
        subscription.setPackageId(order.getTargetPackageId());
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        subscription.setUpdatedAt(LocalDateTime.now());
        // 升级不改变到期时间，只改变套餐

        subscriptionMapper.updateById(subscription);

        // 3. 记录订阅历史
        recordSubscriptionHistory(subscription, order, SubscriptionAction.UPGRADE);

        // 4. 发送升级成功通知
        sendUpgradeSuccessNotification(subscription, order);
    }
}
```

### 3. DTO 类设计

#### 3.1 请求 DTO
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewalPreviewRequest {
    @NotNull(message = "套餐价格ID不能为空")
    private Long packagePriceId;

    @Size(max = 50, message = "优惠码长度不能超过50个字符")
    private String discountCode;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpgradePreviewRequest {
    @NotNull(message = "目标套餐ID不能为空")
    private Long targetPackageId;

    @NotNull(message = "目标套餐价格ID不能为空")
    private Long targetPackagePriceId;

    @Size(max = 50, message = "优惠码长度不能超过50个字符")
    private String discountCode;
}
```

#### 3.2 响应 DTO
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewalOptionsDTO {
    private CurrentSubscriptionInfo currentSubscription;
    private List<RenewalOption> renewalOptions;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeOptionsDTO {
    private CurrentSubscriptionInfo currentSubscription;
    private List<UpgradeOption> upgradeOptions;
    private List<DowngradeOption> downgradeOptions;
}
```

### 4. 定时任务实现

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class SubscriptionScheduledTasks {

    private final IUserSubscriptionService subscriptionService;
    private final MailGunService mailGunService;

    /**
     * 发送到期提醒
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendExpirationReminders() {
        log.info("开始执行订阅到期提醒任务");

        // 查询7天内到期的订阅
        List<UserSubscription> expiring7Days = subscriptionService.findExpiringSubscriptions(7);
        expiring7Days.forEach(subscription -> {
            sendExpirationEmail(subscription, 7);
        });

        // 查询3天内到期的订阅
        List<UserSubscription> expiring3Days = subscriptionService.findExpiringSubscriptions(3);
        expiring3Days.forEach(subscription -> {
            sendExpirationEmail(subscription, 3);
        });

        // 查询1天内到期的订阅
        List<UserSubscription> expiring1Day = subscriptionService.findExpiringSubscriptions(1);
        expiring1Day.forEach(subscription -> {
            sendExpirationEmail(subscription, 1);
        });

        log.info("订阅到期提醒任务执行完成");
    }

    /**
     * 自动续费处理
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void processAutoRenewals() {
        log.info("开始执行自动续费任务");

        List<UserSubscription> autoRenewalSubscriptions =
            subscriptionService.findAutoRenewalSubscriptions();

        for (UserSubscription subscription : autoRenewalSubscriptions) {
            try {
                processAutoRenewal(subscription);
            } catch (Exception e) {
                log.error("自动续费失败: subscriptionId={}", subscription.getId(), e);
            }
        }

        log.info("自动续费任务执行完成");
    }
}
```

这个方案提供了完整的续费和升级功能设计，包含了详细的技术实现方案，可以根据实际需求调整实现优先级。
