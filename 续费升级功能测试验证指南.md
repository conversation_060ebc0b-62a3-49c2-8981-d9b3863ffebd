# 续费升级功能测试验证指南

## 测试环境准备

### 数据库准备
```sql
-- 确保有测试套餐数据
INSERT INTO packages (name, display_name, description, features, sort_order, status) VALUES
('basic', '基础版', '基础AI对话功能', '["每月100次AI对话","基础模型访问"]', 1, 1),
('pro', '专业版', '专业AI对话功能', '["每月500次AI对话","高级模型访问","优先客服支持"]', 2, 1),
('enterprise', '企业版', '企业级AI对话功能', '["无限AI对话","所有模型访问","专属客服","API访问"]', 3, 1);

-- 确保有价格数据
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price, currency, status) VALUES
(1, 'MONTH', 1, 99.00, 89.00, 'CNY', 1),
(1, 'YEAR', 1, 1188.00, 899.00, 'CNY', 1),
(2, 'MONTH', 1, 199.00, 179.00, 'CNY', 1),
(2, 'YEAR', 1, 2388.00, 1799.00, 'CNY', 1),
(3, 'MONTH', 1, 399.00, 359.00, 'CNY', 1);

-- 创建测试用户并给予基础版订阅
INSERT INTO user_subscriptions (user_id, package_id, order_id, status, start_time, end_time, auto_renewal) VALUES
(1, 1, 1, 'ACTIVE', '2024-11-20 14:30:25', '2024-12-25 14:30:25', 0);
```

### 测试用户准备
- 用户ID: 1
- 当前订阅: 基础版（package_id=1）
- 到期时间: 2024-12-25 14:30:25
- 剩余天数: 约5天（假设当前时间为2024-12-20）

## 续费功能测试

### 测试用例1: 获取续费选项
```bash
curl -X GET "http://localhost:8080/packages/1/renewal-options" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/json"
```

**验证点:**
- [x] 返回当前订阅信息
- [x] 显示剩余天数和到期状态
- [x] 返回该套餐的所有价格选项
- [x] 计算续费后的新到期时间

### 测试用例2: 预览续费价格（无优惠码）
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "packagePriceId=1"
```

**验证点:**
- [x] 显示原价和售价
- [x] 计算续费后的新到期时间
- [x] 无优惠码时discountAmount为0

### 测试用例3: 预览续费价格（有优惠码）
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "packagePriceId=1&discountCode=RENEW20"
```

**验证点:**
- [x] 正确应用优惠码折扣
- [x] 显示优惠码信息
- [x] 计算最终支付金额

### 测试用例4: 创建续费订单
```bash
curl -X POST "http://localhost:8080/orders/create-renewal" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "packagePriceId=1&discountCode=RENEW20"
```

**验证点:**
- [x] 订单号以RNW开头
- [x] orderType为RENEWAL
- [x] 订单金额正确
- [x] 订单状态为PENDING

### 测试用例5: 续费支付成功处理
```bash
# 模拟支付成功，更新订单状态并触发订阅处理
# 这个需要通过支付回调或手动更新数据库来测试
```

**验证点:**
- [x] 订阅套餐ID保持不变
- [x] 到期时间正确延长
- [x] 订阅状态为ACTIVE
- [x] 订阅历史记录action为RENEW
- [x] 历史记录中from_package_id为null

## 升级功能测试

### 测试用例6: 获取升级选项
```bash
curl -X GET "http://localhost:8080/packages/upgrade-options" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/json"
```

**验证点:**
- [x] 返回当前订阅信息和剩余价值
- [x] 只显示更高级的套餐选项
- [x] 计算预估升级费用
- [x] 不显示当前或更低级套餐

### 测试用例7: 预览升级价格（无优惠码）
```bash
curl -X POST "http://localhost:8080/orders/preview-upgrade" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "targetPackagePriceId=3"  # 专业版月付
```

**验证点:**
- [x] 显示当前套餐剩余价值
- [x] 显示目标套餐剩余时间价值
- [x] 计算升级差价
- [x] 到期时间保持不变

### 测试用例8: 预览升级价格（有优惠码）
```bash
curl -X POST "http://localhost:8080/orders/preview-upgrade" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "targetPackagePriceId=3&discountCode=UPGRADE15"
```

**验证点:**
- [x] 优惠码对升级差价生效
- [x] 显示优惠码信息
- [x] 计算最终支付金额

### 测试用例9: 创建升级订单
```bash
curl -X POST "http://localhost:8080/orders/create-upgrade" \
  -H "Cookie: access_token=test_user_token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "targetPackagePriceId=3&discountCode=UPGRADE15"
```

**验证点:**
- [x] 订单号以UPG开头
- [x] orderType为UPGRADE
- [x] 订单金额为升级差价
- [x] 订单状态为PENDING

### 测试用例10: 升级支付成功处理
```bash
# 模拟支付成功，更新订单状态并触发订阅处理
```

**验证点:**
- [x] 订阅套餐ID更换为目标套餐
- [x] 到期时间保持不变
- [x] 订阅状态为ACTIVE
- [x] 订阅历史记录action为UPGRADE
- [x] 历史记录中from_package_id为原套餐ID
- [x] 历史记录中to_package_id为目标套餐ID

## 错误场景测试

### 测试用例11: 无订阅用户尝试续费
```bash
curl -X GET "http://localhost:8080/packages/1/renewal-options" \
  -H "Cookie: access_token=no_subscription_user_token"
```

**预期结果:** 返回错误码40001，提示"用户没有活跃订阅"

### 测试用例12: 续费不匹配的套餐
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=test_user_token" \
  -d "packagePriceId=3"  # 专业版价格，但用户当前是基础版
```

**预期结果:** 返回错误码40003，提示"续费套餐必须与当前订阅套餐一致"

### 测试用例13: 尝试降级
```bash
# 假设用户当前是专业版，尝试升级到基础版
curl -X POST "http://localhost:8080/orders/preview-upgrade" \
  -H "Cookie: access_token=pro_user_token" \
  -d "targetPackagePriceId=1"  # 基础版价格
```

**预期结果:** 返回错误码40004，提示"只能升级到更高级的套餐"

### 测试用例14: 无效优惠码
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=test_user_token" \
  -d "packagePriceId=1&discountCode=INVALID_CODE"
```

**预期结果:** 返回优惠码相关错误码，提示优惠码无效

## 数据验证

### 订阅数据验证
```sql
-- 验证续费后的订阅数据
SELECT id, user_id, package_id, status, start_time, end_time 
FROM user_subscriptions 
WHERE user_id = 1;

-- 验证升级后的订阅数据
-- package_id应该改变，end_time应该保持不变
```

### 历史记录验证
```sql
-- 验证订阅历史记录
SELECT id, user_id, action, from_package_id, to_package_id, 
       from_end_time, to_end_time, created_at
FROM subscription_history 
WHERE user_id = 1 
ORDER BY created_at DESC;
```

### 订单数据验证
```sql
-- 验证订单数据
SELECT id, order_no, order_type, package_id, original_amount, 
       discount_amount, final_amount, status
FROM orders 
WHERE user_id = 1 
ORDER BY created_at DESC;
```

## 性能测试

### 并发测试
- 同时创建多个续费/升级订单
- 验证数据一致性
- 检查是否有死锁或竞态条件

### 压力测试
- 大量用户同时进行续费/升级操作
- 监控数据库性能
- 检查响应时间

## 集成测试

### 完整流程测试
1. 用户登录 → 查看订阅状态
2. 选择续费/升级 → 预览价格
3. 创建订单 → 跳转支付
4. 支付成功 → 订阅更新
5. 查看历史记录 → 验证数据

### 跨模块测试
- 订单模块与订阅模块的数据同步
- 支付模块与订阅模块的回调处理
- 优惠码模块与订单模块的集成

## 测试报告模板

### 测试结果记录
```
测试用例: [用例编号] - [用例名称]
执行时间: [YYYY-MM-DD HH:mm:ss]
执行结果: [PASS/FAIL]
实际结果: [描述实际结果]
预期结果: [描述预期结果]
问题描述: [如果失败，描述问题]
```

### 测试总结
- 总测试用例数: XX
- 通过用例数: XX
- 失败用例数: XX
- 通过率: XX%
- 主要问题: [列出主要问题]
- 建议: [改进建议]

这个测试指南涵盖了续费和升级功能的所有关键测试场景，确保功能的正确性和稳定性。
