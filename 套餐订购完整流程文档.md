# LabIAI 套餐订购完整流程文档

## 流程概述

本文档详细描述了用户从浏览套餐到完成订阅的完整业务流程，包括套餐选择、订单创建、支付处理、订阅激活和历史记录的全链路流程。

## 业务流程图

```
用户浏览套餐 → 选择套餐价格 → 应用优惠码(可选) → 创建订单 → 选择支付方式 → 
发起支付 → 支付成功 → 订单状态更新 → 创建/更新订阅 → 记录订阅历史
```

## 详细流程步骤

### 第一步：套餐浏览与选择

#### 1.1 获取套餐列表
**接口**: `GET /packages`

用户访问套餐页面，系统返回所有可用套餐信息：

```json
{
  "code": 20000,
  "message": "获取套餐列表成功",
  "data": [
    {
      "id": 1,
      "name": "basic",
      "display_name": "基础版",
      "description": "适合个人用户使用",
      "features": [
        "每月100次AI对话",
        "基础模型访问",
        "邮件客服支持"
      ],
      "prices": [
        {
          "id": 1,
          "billing_cycle": "MONTH",
          "cycle_count": 1,
          "original_price": 99.00,
          "sale_price": 89.00,
          "currency": "CNY",
          "display_text": "月付 ¥89/月",
          "discount_percent": 10
        },
        {
          "id": 2,
          "billing_cycle": "YEAR",
          "cycle_count": 1,
          "original_price": 1188.00,
          "sale_price": 899.00,
          "currency": "CNY",
          "display_text": "年付 ¥899/年",
          "discount_percent": 24
        }
      ]
    }
  ]
}
```

#### 1.2 套餐详情查看
**接口**: `GET /packages/{id}`

用户点击查看套餐详情，获取更详细的套餐信息和功能说明。

### 第二步：优惠码验证（可选）

#### 2.1 预验证优惠码
**接口**: `POST /orders/preview-discount`

用户输入优惠码后，系统进行预验证：

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
discountCode=SAVE20&packagePriceId=1
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "优惠码验证成功",
  "data": {
    "discountCodeId": 5,
    "discountCode": "SAVE20",
    "valid": true,
    "discountType": "PERCENTAGE",
    "discountValue": 20.00,
    "discountAmount": 17.80,
    "originalAmount": 89.00,
    "finalAmount": 71.20,
    "packageName": "基础版",
    "billingInfo": "月付",
    "failureReason": null
  }
}
```

### 第三步：订单创建

#### 3.1 创建订单
**接口**: `POST /orders/create`

用户确认套餐和优惠码后，系统创建订单：

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
packagePriceId=1&discountCode=SAVE20
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "订单创建成功",
  "data": {
    "orderId": 12345,
    "orderNo": "ORD20241220143025ABC123",
    "packageName": "基础版",
    "packageFeatures": [
      "每月100次AI对话",
      "基础模型访问",
      "邮件客服支持"
    ],
    "billingInfo": "月付",
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-20T15:00:25",
    "createdAt": "2024-12-20T14:30:25"
  }
}
```

**业务规则**:
- 每个用户最多可有4个未付款订单
- 优惠码在创建订单时被消费（使用次数+1）
- 订单默认30分钟后过期

### 第四步：支付方式选择

#### 4.1 获取支付方式
**接口**: `GET /payment/methods?amount=71.20`

系统返回可用的支付方式：

```json
{
  "code": 20000,
  "message": "获取支付方式成功",
  "data": [
    {
      "method_code": "xunhupay_auto",
      "method_name": "支付宝/微信支付",
      "method_type": "GATEWAY",
      "provider": "xunhupay",
      "icon_url": "https://example.com/icons/xunhupay.png",
      "description": "支持支付宝和微信支付，自动适配PC和手机端",
      "min_amount": 0.01,
      "max_amount": 50000.00,
      "priority": 100,
      "recommended": true
    }
  ]
}
```

### 第五步：发起支付

#### 5.1 创建支付
**接口**: `POST /payment/create`

用户选择支付方式后，系统创建支付订单：

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
packagePriceId=1&methodCode=xunhupay_auto
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "支付创建成功",
  "data": {
    "payment_no": "P20241220143456789012",
    "order_no": "O20241220143456789012",
    "amount": 71.20,
    "payment_url": "https://api.xunhupay.com/pay/abc123",
    "qr_code_url": "https://api.xunhupay.com/qrcode/abc123",
    "expired_at": "2024-12-20T15:04:56",
    "package_name": "基础版",
    "billing_info": "MONTH x 1"
  }
}
```

#### 5.2 支付跳转
- **PC端**: 显示二维码，用户扫码支付
- **移动端**: 直接跳转到支付页面

### 第六步：支付处理

#### 6.1 支付回调处理
**接口**: `POST /payment/notify/xunhupay` (内部接口)

支付平台异步通知支付结果，系统处理流程：

1. **验证签名**: 验证支付平台回调签名
2. **更新支付状态**: 将支付记录状态更新为 `SUCCESS`
3. **更新订单状态**: 将订单状态更新为 `PAID`
4. **创建/更新订阅**: 根据订单信息创建或更新用户订阅

#### 6.2 支付成功回跳
**接口**: `GET /payment/success?trade_order_id=O20241220143456789012`

用户支付成功后跳转到成功页面。

### 第七步：订阅激活

#### 7.1 订阅创建/更新逻辑

支付成功后，系统自动处理订阅：

**新订阅创建**:
```sql
INSERT INTO user_subscriptions (
    user_id, package_id, order_id, status, 
    start_time, end_time, auto_renewal
) VALUES (
    123, 1, 12345, 'ACTIVE',
    '2024-12-20 14:30:25', '2025-01-20 14:30:25', 0
);
```

**订阅续费**:
```sql
UPDATE user_subscriptions 
SET end_time = DATE_ADD(end_time, INTERVAL 1 MONTH),
    status = 'ACTIVE',
    updated_at = NOW()
WHERE user_id = 123;
```

**订阅升级**:
```sql
-- 计算剩余时间价值，创建新订阅
-- 记录升级历史
```

### 第八步：订阅历史记录

#### 8.1 记录订阅历史
系统自动在 `subscription_history` 表中记录订阅变更：

```sql
INSERT INTO subscription_history (
    user_id, subscription_id, order_id, action,
    from_package_id, to_package_id, 
    from_end_time, to_end_time
) VALUES (
    123, 456, 12345, 'CREATE',
    NULL, 1,
    NULL, '2025-01-20 14:30:25'
);
```

**订阅操作类型**:
- `CREATE`: 新建订阅
- `RENEW`: 续费
- `UPGRADE`: 升级
- `DOWNGRADE`: 降级
- `CANCEL`: 取消

### 第九步：用户查看订阅信息

#### 9.1 查看当前订阅
**接口**: `GET /user/subscription`

```json
{
  "code": 20000,
  "message": "获取订阅信息成功",
  "data": {
    "subscriptionId": 456,
    "packageName": "基础版",
    "packageFeatures": [
      "每月100次AI对话",
      "基础模型访问",
      "邮件客服支持"
    ],
    "status": "ACTIVE",
    "startTime": "2024-12-20T14:30:25",
    "endTime": "2025-01-20T14:30:25",
    "remainingDays": 31,
    "autoRenewal": false,
    "nextBillDate": "2025-01-20",
    "currentUsage": {
      "aiConversations": 25,
      "maxConversations": 100,
      "usagePercent": 25
    }
  }
}
```

#### 9.2 查看订阅历史
**接口**: `GET /user/subscription/history`

```json
{
  "code": 20000,
  "message": "获取订阅历史成功",
  "data": [
    {
      "id": 789,
      "action": "CREATE",
      "fromPackageName": null,
      "toPackageName": "基础版",
      "fromEndTime": null,
      "toEndTime": "2025-01-20T14:30:25",
      "orderNo": "ORD20241220143025ABC123",
      "amount": 71.20,
      "createdAt": "2024-12-20T14:30:25"
    }
  ]
}
```

## 异常处理流程

### 支付失败处理
1. **支付超时**: 订单状态保持 `PENDING`，用户可重新支付
2. **支付失败**: 支付状态更新为 `FAILED`，用户可重新发起支付
3. **支付取消**: 支付状态更新为 `CANCELLED`，用户可重新支付

### 订单取消处理
**接口**: `PUT /orders/{orderId}/cancel`

1. **检查订单状态**: 只能取消 `PENDING` 状态的订单
2. **释放优惠码**: 自动恢复优惠码使用次数
3. **更新订单状态**: 将订单状态更新为 `CANCELLED`

### 退款处理
1. **申请退款**: 用户申请退款
2. **退款审核**: 系统或人工审核
3. **执行退款**: 调用支付平台退款接口
4. **更新状态**: 更新订单和订阅状态

## 业务规则总结

### 订单规则
- 每用户最多4个未付款订单
- 订单30分钟后自动过期
- 优惠码在订单创建时消费

### 支付规则
- 支付30分钟后自动过期
- 支持重复支付同一订单
- 回调幂等性处理

### 订阅规则
- 一个用户同时只能有一个活跃订阅
- 订阅到期前7/3/1天发送提醒
- 提供3天宽限期

### 优惠码规则
- 支持固定金额和百分比折扣
- 可设置最小使用金额和最大优惠金额
- 订单取消时自动释放使用次数

## 监控与告警

### 关键指标监控
- 订单创建成功率
- 支付成功率
- 订阅激活成功率
- 支付回调处理时长

### 异常告警
- 支付回调失败
- 订阅激活失败
- 优惠码异常使用
- 大额订单创建

这个完整的流程确保了用户从套餐选择到订阅激活的顺畅体验，同时保证了业务数据的一致性和系统的稳定性。
