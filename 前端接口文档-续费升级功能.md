# 续费与升级功能 - 前端接口文档

## 接口概述

本文档描述了续费和升级功能的所有前端接口，包括请求参数、响应格式和业务逻辑说明。

### 认证要求
所有接口都需要用户登录，通过Cookie中的`access_token`进行认证。

### 基础URL
```
https://www.labiai.com
```

### 通用响应格式
```json
{
  "code": 20000,
  "message": "操作成功",
  "data": {},
  "success": true,
  "timestamp": "2024-12-20T14:30:25Z",
  "traceId": "abc123def456"
}
```

## 续费功能接口

### 1. 获取续费选项

**接口**: `GET /packages/{packageId}/renewal-options`

**描述**: 获取指定套餐的续费选项，包括不同计费周期的价格

**路径参数**:
- `packageId` (Long): 当前订阅的套餐ID

**请求示例**:
```javascript
fetch('/packages/1/renewal-options', {
  method: 'GET',
  credentials: 'include'
})
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "获取续费选项成功",
  "data": {
    "currentSubscription": {
      "subscriptionId": 123,
      "packageName": "基础版",
      "endTime": "2024-12-25 14:30:25",
      "status": "ACTIVE",
      "daysRemaining": 5,
      "isExpiringSoon": true,
      "isInGracePeriod": false
    },
    "renewalOptions": [
      {
        "packagePriceId": 1,
        "billingCycle": "MONTH",
        "cycleCount": 1,
        "originalPrice": 99.00,
        "salePrice": 89.00,
        "currency": "CNY",
        "displayText": "续费1个月",
        "newEndTime": "2025-01-25 14:30:25",
        "discountPercent": 10
      },
      {
        "packagePriceId": 2,
        "billingCycle": "YEAR",
        "cycleCount": 1,
        "originalPrice": 1188.00,
        "salePrice": 899.00,
        "currency": "CNY",
        "displayText": "续费1年",
        "newEndTime": "2025-12-25 14:30:25",
        "discountPercent": 24
      }
    ]
  }
}
```

### 2. 预览续费价格

**接口**: `POST /orders/preview-renewal`

**描述**: 预览续费价格，支持优惠码验证

**请求参数**:
- `packagePriceId` (Long): 套餐价格ID
- `discountCode` (String, 可选): 优惠码

**请求示例**:
```javascript
const formData = new FormData();
formData.append('packagePriceId', '1');
formData.append('discountCode', 'RENEW20'); // 可选

fetch('/orders/preview-renewal', {
  method: 'POST',
  credentials: 'include',
  body: formData
})
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "续费预览成功",
  "data": {
    "packagePriceId": 1,
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "billingInfo": "续费1个月",
    "currentEndTime": "2024-12-25 14:30:25",
    "newEndTime": "2025-01-25 14:30:25",
    "discountInfo": {
      "discountCode": "RENEW20",
      "discountType": "PERCENTAGE",
      "discountValue": 20.00
    }
  }
}
```

### 3. 创建续费订单

**接口**: `POST /orders/create-renewal`

**描述**: 创建续费订单

**请求参数**:
- `packagePriceId` (Long): 套餐价格ID
- `discountCode` (String, 可选): 优惠码

**请求示例**:
```javascript
const formData = new FormData();
formData.append('packagePriceId', '1');
formData.append('discountCode', 'RENEW20'); // 可选

fetch('/orders/create-renewal', {
  method: 'POST',
  credentials: 'include',
  body: formData
})
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "续费订单创建成功",
  "data": {
    "orderId": 12346,
    "orderNo": "RNW20241220143025ABC124",
    "packageName": "基础版",
    "billingInfo": "续费1个月",
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-20 15:00:25"
  }
}
```

## 升级功能接口

### 1. 获取升级选项

**接口**: `GET /packages/upgrade-options`

**描述**: 获取当前订阅可升级的套餐选项

**请求示例**:
```javascript
fetch('/packages/upgrade-options', {
  method: 'GET',
  credentials: 'include'
})
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "获取升级选项成功",
  "data": {
    "currentSubscription": {
      "subscriptionId": 123,
      "packageId": 1,
      "packageName": "基础版",
      "endTime": "2025-01-25 14:30:25",
      "remainingDays": 36,
      "remainingValue": 107.40
    },
    "upgradeOptions": [
      {
        "packageId": 2,
        "packageName": "专业版",
        "description": "更多AI对话次数和高级功能",
        "features": [
          "每月500次AI对话",
          "高级模型访问",
          "优先客服支持"
        ],
        "prices": [
          {
            "packagePriceId": 3,
            "billingCycle": "MONTH",
            "originalPrice": 199.00,
            "salePrice": 179.00,
            "displayText": "专业版月付"
          }
        ],
        "upgradeType": "UPGRADE",
        "estimatedCost": 71.60
      }
    ]
  }
}
```

### 2. 预览升级价格

**接口**: `POST /orders/preview-upgrade`

**描述**: 预览升级价格，计算剩余时间的升级差价

**请求参数**:
- `targetPackagePriceId` (Long): 目标套餐价格ID
- `discountCode` (String, 可选): 优惠码

**请求示例**:
```javascript
const formData = new FormData();
formData.append('targetPackagePriceId', '3');
formData.append('discountCode', 'UPGRADE15'); // 可选

fetch('/orders/preview-upgrade', {
  method: 'POST',
  credentials: 'include',
  body: formData
})
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "升级预览成功",
  "data": {
    "upgradeType": "UPGRADE",
    "currentPackage": {
      "packageId": 1,
      "packageName": "基础版",
      "remainingDays": 36,
      "remainingValue": 107.40
    },
    "targetPackage": {
      "packageId": 2,
      "packageName": "专业版",
      "packagePriceId": 3,
      "monthlyPrice": 179.00
    },
    "calculation": {
      "currentRemainingValue": 107.40,
      "targetRemainingValue": 214.80,
      "upgradeDifference": 107.40,
      "discountAmount": 16.11,
      "finalAmount": 91.29,
      "endTime": "2025-01-25 14:30:25",
      "remainingDays": 36
    },
    "discountInfo": {
      "discountCode": "UPGRADE15",
      "discountType": "PERCENTAGE",
      "discountValue": 15.00
    }
  }
}
```

**升级价格计算说明**:
- `currentRemainingValue`: 当前套餐剩余价值 = 当前套餐月价 × 剩余天数 ÷ 30
- `targetRemainingValue`: 目标套餐剩余时间价值 = 目标套餐月价 × 剩余天数 ÷ 30
- `upgradeDifference`: 升级差价 = 目标套餐剩余时间价值 - 当前套餐剩余价值
- `finalAmount`: 最终支付金额 = 升级差价 - 优惠金额

### 3. 创建升级订单

**接口**: `POST /orders/create-upgrade`

**描述**: 创建升级订单，只升级剩余时间到更高等级

**请求参数**:
- `targetPackagePriceId` (Long): 目标套餐价格ID
- `discountCode` (String, 可选): 优惠码

**请求示例**:
```javascript
const formData = new FormData();
formData.append('targetPackagePriceId', '3');
formData.append('discountCode', 'UPGRADE15'); // 可选

fetch('/orders/create-upgrade', {
  method: 'POST',
  credentials: 'include',
  body: formData
})
```

**响应示例**:
```json
{
  "code": 20000,
  "message": "升级订单创建成功",
  "data": {
    "orderId": 12347,
    "orderNo": "UPG20241220143025ABC125",
    "packageName": "专业版",
    "originalAmount": 107.40,
    "discountAmount": 16.11,
    "finalAmount": 91.29,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-20 15:00:25"
  }
}
```

## 错误码说明

### 通用错误码
- `40001`: 用户没有活跃订阅
- `40002`: 套餐价格不存在或已禁用
- `40003`: 续费套餐必须与当前订阅套餐一致
- `40004`: 只能升级到更高级的套餐
- `50000`: 服务器内部错误

### 优惠码相关错误码
- `60202`: 优惠码不存在或已失效
- `60203`: 优惠码已达到使用上限
- `60204`: 优惠码不适用于当前套餐
- `60205`: 订单金额不满足优惠码使用条件

## 前端实现建议

### 续费流程
1. 调用获取续费选项接口，展示续费选项
2. 用户选择续费周期，可输入优惠码
3. 调用预览续费价格接口，显示价格明细
4. 用户确认后调用创建续费订单接口
5. 跳转到支付页面

### 升级流程
1. 调用获取升级选项接口，展示可升级套餐
2. 用户选择目标套餐，可输入优惠码
3. 调用预览升级价格接口，显示升级差价计算
4. 用户确认后调用创建升级订单接口
5. 跳转到支付页面

### 注意事项
1. 升级只会升级剩余时间到更高等级，不会延长到期时间
2. 升级费用是按剩余时间计算的差价，不是全额套餐费用
3. 所有金额字段都是BigDecimal类型，前端需要正确处理精度
4. 时间字段格式为 "yyyy-MM-dd HH:mm:ss"
5. 优惠码是可选参数，不传则不应用优惠

## 前端UI设计建议

### 续费页面设计
```html
<!-- 续费选项卡片 -->
<div class="renewal-options">
  <div class="current-subscription">
    <h3>当前订阅</h3>
    <p>套餐：基础版</p>
    <p>到期时间：2024-12-25 14:30:25</p>
    <p class="warning">剩余5天，即将到期</p>
  </div>

  <div class="renewal-plans">
    <div class="plan-card" data-price-id="1">
      <h4>续费1个月</h4>
      <div class="price">
        <span class="original">¥99</span>
        <span class="sale">¥89</span>
        <span class="discount">10%折扣</span>
      </div>
      <p>续费后到期：2025-01-25 14:30:25</p>
    </div>

    <div class="plan-card recommended" data-price-id="2">
      <h4>续费1年</h4>
      <div class="price">
        <span class="original">¥1188</span>
        <span class="sale">¥899</span>
        <span class="discount">24%折扣</span>
      </div>
      <p>续费后到期：2025-12-25 14:30:25</p>
      <span class="badge">推荐</span>
    </div>
  </div>

  <div class="discount-section">
    <input type="text" placeholder="输入优惠码" id="discountCode">
    <button onclick="previewRenewal()">预览价格</button>
  </div>
</div>
```

### 升级页面设计
```html
<!-- 升级选项卡片 -->
<div class="upgrade-options">
  <div class="current-info">
    <h3>当前订阅</h3>
    <p>套餐：基础版</p>
    <p>剩余：36天</p>
    <p>剩余价值：¥107.40</p>
  </div>

  <div class="upgrade-plans">
    <div class="upgrade-card" data-package-id="2">
      <h4>专业版</h4>
      <p class="description">更多AI对话次数和高级功能</p>
      <ul class="features">
        <li>每月500次AI对话</li>
        <li>高级模型访问</li>
        <li>优先客服支持</li>
      </ul>
      <div class="upgrade-cost">
        <p>升级差价：¥71.60</p>
        <p class="note">仅升级剩余36天到专业版</p>
      </div>
    </div>
  </div>

  <div class="discount-section">
    <input type="text" placeholder="输入优惠码" id="upgradeDiscountCode">
    <button onclick="previewUpgrade()">预览升级费用</button>
  </div>
</div>
```

### JavaScript示例代码

```javascript
// 续费相关函数
async function loadRenewalOptions(packageId) {
  try {
    const response = await fetch(`/packages/${packageId}/renewal-options`, {
      credentials: 'include'
    });
    const result = await response.json();

    if (result.success) {
      renderRenewalOptions(result.data);
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('加载续费选项失败');
  }
}

async function previewRenewal() {
  const packagePriceId = getSelectedPriceId();
  const discountCode = document.getElementById('discountCode').value;

  const formData = new FormData();
  formData.append('packagePriceId', packagePriceId);
  if (discountCode) {
    formData.append('discountCode', discountCode);
  }

  try {
    const response = await fetch('/orders/preview-renewal', {
      method: 'POST',
      credentials: 'include',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      showPricePreview(result.data);
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('预览价格失败');
  }
}

async function createRenewalOrder() {
  const packagePriceId = getSelectedPriceId();
  const discountCode = document.getElementById('discountCode').value;

  const formData = new FormData();
  formData.append('packagePriceId', packagePriceId);
  if (discountCode) {
    formData.append('discountCode', discountCode);
  }

  try {
    const response = await fetch('/orders/create-renewal', {
      method: 'POST',
      credentials: 'include',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      // 跳转到支付页面
      window.location.href = `/payment?orderId=${result.data.orderId}`;
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('创建订单失败');
  }
}

// 升级相关函数
async function loadUpgradeOptions() {
  try {
    const response = await fetch('/packages/upgrade-options', {
      credentials: 'include'
    });
    const result = await response.json();

    if (result.success) {
      renderUpgradeOptions(result.data);
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('加载升级选项失败');
  }
}

async function previewUpgrade() {
  const targetPackagePriceId = getSelectedUpgradePriceId();
  const discountCode = document.getElementById('upgradeDiscountCode').value;

  const formData = new FormData();
  formData.append('targetPackagePriceId', targetPackagePriceId);
  if (discountCode) {
    formData.append('discountCode', discountCode);
  }

  try {
    const response = await fetch('/orders/preview-upgrade', {
      method: 'POST',
      credentials: 'include',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      showUpgradePreview(result.data);
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('预览升级费用失败');
  }
}

async function createUpgradeOrder() {
  const targetPackagePriceId = getSelectedUpgradePriceId();
  const discountCode = document.getElementById('upgradeDiscountCode').value;

  const formData = new FormData();
  formData.append('targetPackagePriceId', targetPackagePriceId);
  if (discountCode) {
    formData.append('discountCode', discountCode);
  }

  try {
    const response = await fetch('/orders/create-upgrade', {
      method: 'POST',
      credentials: 'include',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      // 跳转到支付页面
      window.location.href = `/payment?orderId=${result.data.orderId}`;
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('创建升级订单失败');
  }
}

// 工具函数
function showError(message) {
  alert(message); // 实际项目中应该用更好的错误提示组件
}

function showPricePreview(data) {
  // 显示价格预览弹窗
  const modal = document.getElementById('pricePreviewModal');
  document.getElementById('originalAmount').textContent = `¥${data.originalAmount}`;
  document.getElementById('discountAmount').textContent = `¥${data.discountAmount}`;
  document.getElementById('finalAmount').textContent = `¥${data.finalAmount}`;
  modal.style.display = 'block';
}

function showUpgradePreview(data) {
  // 显示升级预览弹窗
  const modal = document.getElementById('upgradePreviewModal');
  document.getElementById('currentValue').textContent = `¥${data.calculation.currentRemainingValue}`;
  document.getElementById('targetValue').textContent = `¥${data.calculation.targetRemainingValue}`;
  document.getElementById('upgradeDiff').textContent = `¥${data.calculation.upgradeDifference}`;
  document.getElementById('upgradeDiscount').textContent = `¥${data.calculation.discountAmount}`;
  document.getElementById('upgradeFinal').textContent = `¥${data.calculation.finalAmount}`;
  modal.style.display = 'block';
}
```

## 测试用例

### 续费功能测试
1. **正常续费**: 选择续费周期，不使用优惠码
2. **优惠码续费**: 输入有效优惠码进行续费
3. **无效优惠码**: 输入无效优惠码，验证错误提示
4. **即将到期续费**: 测试订阅即将到期时的续费

### 升级功能测试
1. **正常升级**: 选择更高级套餐进行升级
2. **优惠码升级**: 使用优惠码进行升级
3. **降级限制**: 尝试选择更低级套餐，验证错误提示
4. **剩余时间计算**: 验证升级差价计算的准确性

## 支付流程说明

### 续费支付流程
1. 用户选择续费选项 → 调用预览接口确认价格
2. 创建续费订单 → 获得订单ID和订单号（RNW开头）
3. 跳转支付页面 → 用户完成支付
4. 支付成功回调 → 系统自动处理续费
   - 延长订阅到期时间
   - 套餐保持不变
   - 记录续费历史

### 升级支付流程
1. 用户选择升级选项 → 调用预览接口确认差价
2. 创建升级订单 → 获得订单ID和订单号（UPG开头）
3. 跳转支付页面 → 用户完成支付
4. 支付成功回调 → 系统自动处理升级
   - 更换订阅套餐
   - 到期时间保持不变
   - 记录升级历史

### 支付成功后的订阅变化

#### 续费示例
```
支付前：基础版，到期 2024-12-25 14:30:25
续费1个月后：基础版，到期 2025-01-25 14:30:25
```

#### 升级示例
```
支付前：基础版，到期 2025-01-25 14:30:25，剩余36天
升级到专业版后：专业版，到期 2025-01-25 14:30:25，剩余36天
```

### 前端轮询建议

支付成功后，建议前端轮询订阅状态：

```javascript
// 支付成功后轮询订阅状态
async function checkSubscriptionUpdate(expectedChange) {
  const maxAttempts = 10;
  const interval = 2000; // 2秒

  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch('/user/subscription-info', {
        credentials: 'include'
      });
      const result = await response.json();

      if (result.success && result.data.currentSubscription) {
        const subscription = result.data.currentSubscription;

        if (expectedChange.type === 'renewal') {
          // 检查到期时间是否延长
          if (new Date(subscription.endTime) > new Date(expectedChange.originalEndTime)) {
            showSuccessMessage('续费成功！订阅已延长');
            return true;
          }
        } else if (expectedChange.type === 'upgrade') {
          // 检查套餐是否更换
          if (subscription.packageId === expectedChange.targetPackageId) {
            showSuccessMessage('升级成功！已享受高级功能');
            return true;
          }
        }
      }

      await new Promise(resolve => setTimeout(resolve, interval));
    } catch (error) {
      console.error('检查订阅状态失败:', error);
    }
  }

  showWarningMessage('订阅更新可能需要更多时间，请稍后刷新页面查看');
  return false;
}
```

这份接口文档为前端开发者提供了完整的实现指南，包括接口调用、UI设计建议、JavaScript示例代码和支付流程说明。
