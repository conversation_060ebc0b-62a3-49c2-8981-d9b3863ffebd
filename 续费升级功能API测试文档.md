# 续费与升级功能 API 测试文档

## 测试环境准备

### 前置条件
1. 用户已登录并有活跃订阅
2. 数据库中有多个套餐和价格选项
3. 可选：准备一些优惠码用于测试

### 测试数据示例
```sql
-- 假设用户ID为1，当前订阅基础版套餐（package_id=1）
-- 套餐层级：基础版(sort_order=1) < 专业版(sort_order=2) < 企业版(sort_order=3)
```

## 续费功能测试

### 1. 获取续费选项
**接口**: `GET /packages/{packageId}/renewal-options`

**测试用例1**: 正常获取续费选项
```bash
curl -X GET "http://localhost:8080/packages/1/renewal-options" \
  -H "Cookie: access_token=your_token_here"
```

**预期响应**:
```json
{
  "code": 20000,
  "message": "获取续费选项成功",
  "data": {
    "currentSubscription": {
      "subscriptionId": 123,
      "packageName": "基础版",
      "endTime": "2024-12-25 14:30:25",
      "status": "ACTIVE",
      "daysRemaining": 5,
      "isExpiringSoon": true,
      "isInGracePeriod": false
    },
    "renewalOptions": [
      {
        "packagePriceId": 1,
        "billingCycle": "MONTH",
        "cycleCount": 1,
        "originalPrice": 99.00,
        "salePrice": 89.00,
        "currency": "CNY",
        "displayText": "续费1个月",
        "newEndTime": "2025-01-25 14:30:25",
        "discountPercent": 10
      },
      {
        "packagePriceId": 2,
        "billingCycle": "YEAR",
        "cycleCount": 1,
        "originalPrice": 1188.00,
        "salePrice": 899.00,
        "currency": "CNY",
        "displayText": "续费1年",
        "newEndTime": "2025-12-25 14:30:25",
        "discountPercent": 24
      }
    ]
  }
}
```

### 2. 预览续费价格
**接口**: `POST /orders/preview-renewal`

**测试用例1**: 不使用优惠码
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=your_token_here" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "packagePriceId=1"
```

**测试用例2**: 使用优惠码
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=your_token_here" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "packagePriceId=1&discountCode=RENEW20"
```

**预期响应**:
```json
{
  "code": 20000,
  "message": "续费预览成功",
  "data": {
    "packagePriceId": 1,
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "billingInfo": "续费1个月",
    "currentEndTime": "2024-12-25 14:30:25",
    "newEndTime": "2025-01-25 14:30:25",
    "discountInfo": {
      "discountCode": "RENEW20",
      "discountType": "PERCENTAGE",
      "discountValue": 20.00
    }
  }
}
```

### 3. 创建续费订单
**接口**: `POST /orders/create-renewal`

**测试用例1**: 创建续费订单
```bash
curl -X POST "http://localhost:8080/orders/create-renewal" \
  -H "Cookie: access_token=your_token_here" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "packagePriceId=1&discountCode=RENEW20"
```

**预期响应**:
```json
{
  "code": 20000,
  "message": "续费订单创建成功",
  "data": {
    "orderId": 12346,
    "orderNo": "RNW20241220143025ABC124",
    "packageName": "基础版",
    "billingInfo": "续费1个月",
    "originalAmount": 89.00,
    "discountAmount": 17.80,
    "finalAmount": 71.20,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-20 15:00:25"
  }
}
```

## 升级功能测试

### 1. 获取升级选项
**接口**: `GET /packages/upgrade-options`

**测试用例1**: 正常获取升级选项
```bash
curl -X GET "http://localhost:8080/packages/upgrade-options" \
  -H "Cookie: access_token=your_token_here"
```

**预期响应**:
```json
{
  "code": 20000,
  "message": "获取升级选项成功",
  "data": {
    "currentSubscription": {
      "subscriptionId": 123,
      "packageId": 1,
      "packageName": "基础版",
      "endTime": "2025-01-25 14:30:25",
      "remainingDays": 36,
      "remainingValue": 107.40
    },
    "upgradeOptions": [
      {
        "packageId": 2,
        "packageName": "专业版",
        "description": "更多AI对话次数和高级功能",
        "features": [
          "每月500次AI对话",
          "高级模型访问",
          "优先客服支持"
        ],
        "prices": [
          {
            "packagePriceId": 3,
            "billingCycle": "MONTH",
            "originalPrice": 199.00,
            "salePrice": 179.00,
            "displayText": "专业版月付"
          }
        ],
        "upgradeType": "UPGRADE",
        "estimatedCost": 71.60
      }
    ]
  }
}
```

### 2. 预览升级价格
**接口**: `POST /orders/preview-upgrade`

**测试用例1**: 预览升级价格
```bash
curl -X POST "http://localhost:8080/orders/preview-upgrade" \
  -H "Cookie: access_token=your_token_here" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "targetPackagePriceId=3&discountCode=UPGRADE15"
```

**预期响应**:
```json
{
  "code": 20000,
  "message": "升级预览成功",
  "data": {
    "upgradeType": "UPGRADE",
    "currentPackage": {
      "packageId": 1,
      "packageName": "基础版",
      "remainingDays": 36,
      "remainingValue": 107.40
    },
    "targetPackage": {
      "packageId": 2,
      "packageName": "专业版",
      "packagePriceId": 3,
      "monthlyPrice": 179.00
    },
    "calculation": {
      "remainingValue": 107.40,
      "targetMonthlyPrice": 179.00,
      "priceDifference": 71.60,
      "discountAmount": 10.74,
      "finalAmount": 60.86,
      "newEndTime": "2025-01-25 14:30:25"
    },
    "discountInfo": {
      "discountCode": "UPGRADE15",
      "discountType": "PERCENTAGE",
      "discountValue": 15.00
    }
  }
}
```

### 3. 创建升级订单
**接口**: `POST /orders/create-upgrade`

**测试用例1**: 创建升级订单
```bash
curl -X POST "http://localhost:8080/orders/create-upgrade" \
  -H "Cookie: access_token=your_token_here" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "targetPackagePriceId=3&discountCode=UPGRADE15"
```

**预期响应**:
```json
{
  "code": 20000,
  "message": "升级订单创建成功",
  "data": {
    "orderId": 12347,
    "orderNo": "UPG20241220143025ABC125",
    "packageName": "专业版",
    "originalAmount": 71.60,
    "discountAmount": 10.74,
    "finalAmount": 60.86,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-20 15:00:25"
  }
}
```

## 错误场景测试

### 1. 用户没有活跃订阅
```bash
# 使用没有订阅的用户token测试
curl -X GET "http://localhost:8080/packages/1/renewal-options" \
  -H "Cookie: access_token=no_subscription_user_token"
```

**预期响应**:
```json
{
  "code": 40001,
  "message": "用户没有活跃订阅"
}
```

### 2. 续费套餐不匹配
```bash
# 用户当前订阅基础版，但尝试续费专业版
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=your_token_here" \
  -d "packagePriceId=3"  # 专业版价格ID
```

**预期响应**:
```json
{
  "code": 40003,
  "message": "续费套餐必须与当前订阅套餐一致"
}
```

### 3. 尝试降级
```bash
# 用户当前订阅专业版，但尝试升级到基础版
curl -X POST "http://localhost:8080/orders/preview-upgrade" \
  -H "Cookie: access_token=pro_user_token" \
  -d "targetPackagePriceId=1"  # 基础版价格ID
```

**预期响应**:
```json
{
  "code": 40004,
  "message": "只能升级到更高级的套餐"
}
```

### 4. 优惠码无效
```bash
curl -X POST "http://localhost:8080/orders/preview-renewal" \
  -H "Cookie: access_token=your_token_here" \
  -d "packagePriceId=1&discountCode=INVALID_CODE"
```

**预期响应**:
```json
{
  "code": 60202,
  "message": "优惠码不存在或已失效"
}
```

## 支付流程测试

### 续费订单支付
1. 创建续费订单（获得orderId）
2. 调用支付接口：`POST /payment/create`
3. 支付成功后，验证订阅是否延长

### 升级订单支付
1. 创建升级订单（获得orderId）
2. 调用支付接口：`POST /payment/create`
3. 支付成功后，验证订阅套餐是否更换

## 验证点

### 续费成功后验证
- 订阅到期时间是否正确延长
- 订阅历史记录是否正确记录
- 优惠码使用次数是否正确扣减

### 升级成功后验证
- 订阅套餐是否更换为目标套餐
- 订阅到期时间是否保持不变
- 订阅历史记录是否正确记录升级信息
- 优惠码使用次数是否正确扣减

## 注意事项

1. **测试顺序**: 先测试预览接口，再测试订单创建
2. **数据清理**: 每次测试后清理测试数据，避免影响后续测试
3. **边界条件**: 测试订阅即将到期、已过期等边界情况
4. **并发测试**: 测试同时创建多个订单的情况
5. **优惠码测试**: 测试优惠码的各种状态（有效、无效、已用完等）

这个测试文档涵盖了续费和升级功能的主要测试场景，可以帮助验证实现的正确性。
