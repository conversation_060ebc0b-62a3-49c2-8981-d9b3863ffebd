# 支付回调处理完整流程说明

## 流程概述

支付回调处理是续费和升级功能的关键环节，确保用户支付成功后能够正确更新订阅状态。

## 完整流程图

```
用户支付成功
    ↓
第三方支付平台回调
    ↓
PaymentController.handleXunhupayNotify()
    ↓
验证回调签名和参数
    ↓
PaymentService.processPaymentSuccess()
    ↓
┌─────────────────────────────────────────┐
│ 1. 更新支付记录状态为SUCCESS             │
│ 2. 记录交易ID和平台订单ID               │
│ 3. 保存回调数据                        │
│ 4. 设置支付完成时间                     │
└─────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────┐
│ 更新订单状态为PAID                      │
└─────────────────────────────────────────┘
    ↓
PaymentService.processSubscription(order)
    ↓
UserSubscriptionService.processSubscription(order)
    ↓
根据order.orderType判断处理方式
    ↓
┌─────────────┬─────────────┬─────────────┐
│  RENEWAL    │   UPGRADE   │   普通订单   │
│  续费处理    │   升级处理   │  兼容处理    │
└─────────────┴─────────────┴─────────────┘
    ↓
更新用户订阅和记录历史
    ↓
发送支付成功通知
```

## 核心代码实现

### 1. PaymentService.processPaymentSuccess()

```java
@Transactional
public void processPaymentSuccess(Payment payment, Map<String, String> params) {
    try {
        // 记录处理开始
        TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PROCESS_START", processDetails);

        // 1. 更新支付记录
        payment.setStatus(PaymentStatusEnum.SUCCESS.getCode());
        payment.setTransactionId(params.get("transaction_id"));
        payment.setPlatformOrderId(params.get("open_order_id"));
        payment.setCallbackData(callbackData);
        payment.setPaidAt(LocalDateTime.now());
        updateById(payment);

        // 2. 更新订单状态
        Order order = orderMapper.selectById(payment.getOrderId());
        order.setStatus(OrderStatusEnum.PAID.getCode());
        updateOrder(order);

        // 3. 处理订阅（核心逻辑）
        processSubscription(order);

        // 4. 发送通知
        sendPaymentSuccessNotification(order);

        // 记录处理完成
        TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PROCESS_COMPLETE", successDetails);

    } catch (Exception e) {
        TraceUtils.recordError("PAYMENT_SUCCESS_PROCESS_FAILED", errorDetails);
        throw e; // 确保事务回滚
    }
}
```

### 2. UserSubscriptionService.processSubscription()

```java
@Override
@Transactional
public ApiResponse<UserSubscription> processSubscription(Order order) {
    String orderType = order.getOrderType(); // 关键：获取订单类型
    UserSubscription currentSubscription = getCurrentSubscription(userId);
    
    if (currentSubscription == null) {
        // 创建新订阅
        result = createNewSubscription(order);
    } else {
        // 根据订单类型处理
        if ("RENEWAL".equals(orderType)) {
            // 续费：延长到期时间，套餐不变
            result = renewSubscription(order, currentSubscription);
        } else if ("UPGRADE".equals(orderType)) {
            // 升级：更换套餐，到期时间不变
            result = upgradeSubscription(order, currentSubscription);
        } else {
            // 普通订单：兼容旧逻辑
            // 根据套餐是否相同判断续费还是升级
        }
    }
}
```

## 订单类型处理详解

### 续费订单处理 (orderType = "RENEWAL")

#### 处理逻辑
```java
@Override
@Transactional
public UserSubscription renewSubscription(Order order, UserSubscription currentSubscription) {
    // 1. 获取套餐价格信息
    PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());
    
    // 2. 计算新的到期时间（从当前到期时间开始延长）
    LocalDateTime newEndTime = calculateEndTime(packagePrice, currentSubscription.getEndTime());
    
    // 3. 更新订阅
    currentSubscription.setEndTime(newEndTime);
    currentSubscription.setStatus("ACTIVE");
    updateById(currentSubscription);
    
    // 4. 记录续费历史
    recordSubscriptionHistory(currentSubscription, order, "RENEW", 
                            originalEndTime, newEndTime);
}
```

#### 效果
- ✅ 套餐ID：保持不变
- ✅ 到期时间：延长（从当前到期时间开始计算）
- ✅ 历史记录：action = "RENEW"

#### 示例
```
续费前：基础版，到期 2024-12-25 14:30:25
续费1个月后：基础版，到期 2025-01-25 14:30:25
```

### 升级订单处理 (orderType = "UPGRADE")

#### 处理逻辑
```java
@Override
@Transactional
public UserSubscription upgradeSubscription(Order order, UserSubscription currentSubscription) {
    // 1. 获取套餐价格信息
    PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());
    
    // 2. 记录原来的信息
    Long originalPackageId = currentSubscription.getPackageId();
    LocalDateTime originalEndTime = currentSubscription.getEndTime();
    
    // 3. 升级逻辑：只更换套餐，保持到期时间不变
    currentSubscription.setPackageId(order.getPackageId());
    // 注意：不修改endTime，保持原来的到期时间
    currentSubscription.setStatus("ACTIVE");
    updateById(currentSubscription);
    
    // 4. 记录升级历史
    recordSubscriptionHistoryForUpgrade(currentSubscription, order, "UPGRADE", 
                                      originalPackageId, originalEndTime, originalEndTime);
}
```

#### 效果
- ✅ 套餐ID：更换为目标套餐
- ✅ 到期时间：保持不变
- ✅ 历史记录：action = "UPGRADE"，记录from_package_id和to_package_id

#### 示例
```
升级前：基础版，到期 2025-01-25 14:30:25，剩余36天
升级后：专业版，到期 2025-01-25 14:30:25，剩余36天
```

## 订阅历史记录

### subscription_history 表结构

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键 |
| user_id | bigint | 用户ID |
| subscription_id | bigint | 订阅ID |
| order_id | bigint | 订单ID |
| action | varchar | 操作类型：CREATE/RENEW/UPGRADE |
| from_package_id | bigint | 原套餐ID（升级时使用） |
| to_package_id | bigint | 目标套餐ID |
| from_end_time | datetime | 原到期时间 |
| to_end_time | datetime | 新到期时间 |
| created_at | datetime | 创建时间 |

### 续费历史记录示例
```json
{
  "action": "RENEW",
  "from_package_id": null,
  "to_package_id": 1,
  "from_end_time": "2024-12-25 14:30:25",
  "to_end_time": "2025-01-25 14:30:25"
}
```

### 升级历史记录示例
```json
{
  "action": "UPGRADE",
  "from_package_id": 1,
  "to_package_id": 2,
  "from_end_time": "2025-01-25 14:30:25",
  "to_end_time": "2025-01-25 14:30:25"
}
```

## 支付标题优化

### buildOrderTitle() 方法增强

```java
private String buildOrderTitle(Order order) {
    try {
        Package pkg = packageMapper.selectById(order.getPackageId());
        PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());
        
        String packageName = pkg.getDisplayName();
        String billingInfo = getBillingDisplayText(packagePrice.getBillingCycle(), packagePrice.getCycleCount());
        
        // 根据订单类型生成不同的标题
        String orderType = order.getOrderType();
        if ("RENEWAL".equals(orderType)) {
            return "续费 " + packageName + " - " + billingInfo;
        } else if ("UPGRADE".equals(orderType)) {
            return "升级到 " + packageName + " - 剩余时间";
        } else {
            return packageName + " - " + billingInfo;
        }
    } catch (Exception e) {
        return "LabIAI订阅服务";
    }
}
```

### 支付标题示例
- 续费订单：`续费 基础版 - 1个月`
- 升级订单：`升级到 专业版 - 剩余时间`
- 普通订单：`基础版 - 1个月`

## 错误处理和事务管理

### 事务边界
```java
@Transactional
public void processPaymentSuccess(Payment payment, Map<String, String> params) {
    try {
        // 所有操作在一个事务中
        updatePayment(payment);
        updateOrder(order);
        processSubscription(order);
        sendNotification(order);
    } catch (Exception e) {
        // 任何异常都会导致事务回滚
        log.error("处理支付成功失败", e);
        throw e;
    }
}
```

### 异常处理策略
1. **支付记录更新失败**：事务回滚，支付状态保持原样
2. **订单状态更新失败**：事务回滚，支付状态保持原样
3. **订阅处理失败**：事务回滚，支付和订单状态保持原样
4. **通知发送失败**：记录警告日志，不影响主流程

## 监控和日志

### 关键业务事件记录
```java
// 处理开始
TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PROCESS_START", processDetails);

// 订单状态更新
TraceUtils.recordBusinessEvent("ORDER_STATUS_UPDATED", orderDetails);

// 订阅处理
TraceUtils.recordBusinessEvent("SUBSCRIPTION_PROCESSED", subscriptionDetails);

// 处理完成
TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PROCESS_COMPLETE", successDetails);
```

### 错误事件记录
```java
// 处理失败
TraceUtils.recordError("PAYMENT_SUCCESS_PROCESS_FAILED", errorDetails);

// 订阅处理失败
TraceUtils.recordError("SUBSCRIPTION_PROCESS_FAILED", errorDetails);
```

## 测试验证

### 续费测试
1. 创建续费订单
2. 模拟支付成功回调
3. 验证：
   - 支付状态为SUCCESS
   - 订单状态为PAID
   - 订阅套餐ID未变
   - 到期时间正确延长
   - 历史记录正确

### 升级测试
1. 创建升级订单
2. 模拟支付成功回调
3. 验证：
   - 支付状态为SUCCESS
   - 订单状态为PAID
   - 订阅套餐ID已更换
   - 到期时间保持不变
   - 历史记录包含原套餐ID

## 总结

支付回调处理现在完全支持续费和升级功能：

1. ✅ **准确识别订单类型**：根据orderType字段区分处理方式
2. ✅ **正确处理续费**：延长到期时间，保持套餐不变
3. ✅ **正确处理升级**：更换套餐，保持到期时间不变
4. ✅ **完整记录历史**：详细记录所有变更信息
5. ✅ **事务安全性**：确保数据一致性
6. ✅ **详细监控**：完整的业务事件和错误记录
7. ✅ **向后兼容**：支持旧的普通订单处理逻辑

这个实现确保了续费和升级功能的可靠性和正确性。
