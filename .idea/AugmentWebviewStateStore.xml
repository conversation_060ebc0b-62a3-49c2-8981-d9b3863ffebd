<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;e2d745f2-434a-45b3-a47f-3945d61c8b7e&quot;,&quot;conversations&quot;:{&quot;063ce82f-805d-47d7-b71e-c046350a3857&quot;:{&quot;id&quot;:&quot;063ce82f-805d-47d7-b71e-c046350a3857&quot;,&quot;createdAtIso&quot;:&quot;2025-06-11T11:47:35.961Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-11T11:47:35.961Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;e2d745f2-434a-45b3-a47f-3945d61c8b7e&quot;:{&quot;id&quot;:&quot;e2d745f2-434a-45b3-a47f-3945d61c8b7e&quot;,&quot;createdAtIso&quot;:&quot;2025-06-11T11:47:36.031Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-11T11:47:36.031Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;1eeb279f-8706-4569-a028-f0963f4f529a&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;}" />
      </map>
    </option>
  </component>
</project>