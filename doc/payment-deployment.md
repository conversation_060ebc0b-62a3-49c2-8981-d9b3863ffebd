# 支付系统部署指南

基于现有LabIAI项目，集成虎皮椒支付的完整部署指南。

## 📋 部署前准备

### 1. 虎皮椒账号申请

1. 访问 [虎皮椒官网](https://www.xunhupay.com/) 注册账号
2. 完成实名认证（个人或企业）
3. 在商户后台创建应用，获取：
   - `app_id`：应用ID
   - `app_secret`：应用密钥
4. 配置回调地址：`http://yourdomain.com/api/payment/notify/xunhupay`

### 2. 环境要求

- Java 8+
- MySQL 5.7+
- Spring Boot 2.x
- Maven 3.6+

## 🚀 部署步骤

### 第一步：数据库初始化

```bash
# 1. 执行支付表结构创建
mysql -u root -p labiai < sql/payment_tables.sql

# 2. 执行初始化数据
mysql -u root -p labiai < sql/init_payment_data.sql

# 3. 验证表结构
mysql -u root -p -e "USE labiai; SHOW TABLES LIKE '%payment%';"
```

### 第二步：配置虎皮椒支付

```sql
-- 在数据库中更新虎皮椒配置
UPDATE payment_methods 
SET api_config = JSON_OBJECT(
    'app_id', 'YOUR_REAL_APP_ID',
    'app_secret', 'YOUR_REAL_APP_SECRET', 
    'api_url', 'https://api.xunhupay.com/payment/do.html'
)
WHERE method_code = 'xunhupay_auto';
```

### 第三步：配置应用参数

在 `application.yml` 中配置：

```yaml
# 支付系统配置
payment:
  callback-base-url: http://yourdomain.com  # 修改为你的域名
  default-expire-minutes: 30
  max-retry-count: 3

# 日志配置（可选）
logging:
  level:
    com.jiashu.labiai.service.PaymentService: INFO
    com.jiashu.labiai.util.XunhupayUtils: DEBUG
```

### 第四步：编译和部署

```bash
# 1. 编译项目
mvn clean package -DskipTests

# 2. 启动应用
java -jar target/labiai-*.jar

# 3. 验证启动
curl http://localhost:8080/api/packages
```

## 🧪 功能测试

### 1. 获取套餐列表

```bash
curl -X GET "http://localhost:8080/api/packages" \
     -H "Content-Type: application/json"
```

**预期响应：**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "basic",
      "display_name": "基础版",
      "description": "适合个人用户",
      "features": ["基础功能", "邮件支持", "5GB存储"],
      "prices": [
        {
          "id": 1,
          "billing_cycle": "MONTH",
          "cycle_count": 1,
          "original_price": 99.00,
          "sale_price": 89.00,
          "currency": "CNY",
          "display_text": "月付",
          "discount_percent": 10
        }
      ]
    }
  ]
}
```

### 2. 获取支付方式

```bash
curl -X GET "http://localhost:8080/api/packages/payment-methods" \
     -H "Content-Type: application/json"
```

**预期响应：**
```json
{
  "code": 200,
  "data": [
    {
      "method_code": "xunhupay_auto",
      "method_name": "支付宝/微信支付",
      "method_type": "GATEWAY",
      "provider": "xunhupay",
      "description": "支持支付宝和微信支付，自动适配PC和手机端",
      "min_amount": 0.01,
      "max_amount": 50000.00,
      "recommended": true
    }
  ]
}
```

### 3. 创建支付订单（需要登录）

```bash
# 先登录获取token
curl -X POST "http://localhost:8080/auth/login" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "<EMAIL>",
       "password": "password"
     }'

# 使用token创建支付订单
curl -X POST "http://localhost:8080/api/payment/create" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d "packagePriceId=1&methodCode=xunhupay_auto"
```

**预期响应：**
```json
{
  "code": 200,
  "data": {
    "payment_no": "P20241216123456123456",
    "order_no": "O20241216123456123456",
    "amount": 89.00,
    "payment_url": "https://api.xunhupay.com/pay/...",
    "qr_code_url": "https://api.xunhupay.com/qrcode/...",
    "expired_at": "2024-12-16T13:24:56",
    "package_name": "基础版",
    "billing_info": "MONTH x 1"
  }
}
```

## 🔍 监控和日志

### 关键日志位置

```bash
# 支付创建日志
grep "虎皮椒支付创建" logs/spring.log

# 支付回调日志
grep "虎皮椒支付回调" logs/spring.log

# 错误日志
grep "ERROR" logs/spring.log | grep -i payment
```

### 监控指标

```sql
-- 今日支付统计
SELECT 
    COUNT(*) as total_payments,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_payments,
    SUM(CASE WHEN status = 'SUCCESS' THEN amount ELSE 0 END) as success_amount
FROM payments 
WHERE DATE(created_at) = CURDATE();

-- 支付方式统计
SELECT 
    pm.method_name,
    COUNT(*) as payment_count,
    SUM(p.amount) as total_amount,
    ROUND(AVG(CASE WHEN p.status = 'SUCCESS' THEN 1.0 ELSE 0.0 END) * 100, 2) as success_rate
FROM payments p
JOIN payment_methods pm ON p.payment_method_id = pm.id
WHERE DATE(p.created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY pm.id, pm.method_name;
```

## ⚠️ 常见问题

### 1. 支付创建失败

**问题：** API返回错误 "虎皮椒支付创建失败"

**解决方案：**
1. 检查虎皮椒配置是否正确
2. 验证app_id和app_secret
3. 检查网络连接是否正常
4. 查看详细错误日志

```sql
-- 检查配置
SELECT api_config FROM payment_methods WHERE method_code = 'xunhupay_auto';
```

### 2. 回调验签失败

**问题：** 日志显示 "虎皮椒回调签名验证失败"

**解决方案：**
1. 确认app_secret配置正确
2. 检查回调URL是否可访问
3. 验证参数传递是否完整

```bash
# 测试回调URL可访问性
curl -X POST "http://yourdomain.com/api/payment/notify/xunhupay" \
     -d "test=1"
```

### 3. 支付状态不更新

**问题：** 用户支付成功但订单状态未更新

**解决方案：**
1. 检查虎皮椒后台回调配置
2. 验证回调URL设置
3. 查看回调日志

```sql
-- 查看回调数据
SELECT 
    payment_no,
    status,
    callback_data,
    paid_at
FROM payments 
WHERE status = 'PENDING' 
AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

## 🔐 安全建议

### 1. 敏感信息保护

```bash
# 使用环境变量存储敏感配置
export XUNHUPAY_APP_ID="your_app_id"
export XUNHUPAY_APP_SECRET="your_app_secret"
```

### 2. 回调安全

- 确保回调URL使用HTTPS
- 验证回调来源IP
- 记录所有回调请求

### 3. 数据库安全

```sql
-- 创建专用支付用户
CREATE USER 'payment_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE ON labiai.payments TO 'payment_user'@'localhost';
GRANT SELECT, INSERT, UPDATE ON labiai.orders TO 'payment_user'@'localhost';
```

## 📈 性能优化

### 1. 数据库索引优化

```sql
-- 添加支付查询索引
CREATE INDEX idx_payments_trade_order_id ON payments(trade_order_id);
CREATE INDEX idx_payments_user_status ON payments(user_id, status);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
```

### 2. 缓存策略

```java
// 缓存套餐信息（可选）
@Cacheable(value = "packages", key = "'all'")
public List<Package> getAllPackages() {
    // 实现细节
}
```

## 🚀 上线清单

- [ ] 虎皮椒账号已申请并认证
- [ ] 数据库表结构已创建
- [ ] 支付配置已更新为生产环境配置
- [ ] 回调URL已配置并可访问
- [ ] 支付流程已完整测试
- [ ] 监控和日志已配置
- [ ] 安全措施已实施
- [ ] 备份策略已制定

## 📞 技术支持

如遇到问题，请：
1. 查看详细错误日志
2. 检查数据库配置
3. 验证虎皮椒配置
4. 联系虎皮椒技术支持

**虎皮椒技术支持：**
- 官方文档：https://www.xunhupay.com/doc
- 技术QQ群：见官网
- 邮箱支持：见官网 