# LabIAI 支付订阅系统 API 接口文档

## 概述

本文档描述了 LabIAI 支付订阅系统的前端 API 接口，包含套餐管理、订单创建、支付处理等完整流程。

**基础信息：**

- 响应格式: JSON
- 字符编码: UTF-8

## 统一响应格式

所有接口都使用统一的响应格式：

```json
{
  "success": true,
  "code": 20000,
  "message": "操作成功",
  "data": {},
  "timestamp": 1703123456789,
  "traceId": "trace_123456789",
  "pagination": null,
  "extra": null
}
```

**响应字段说明：**
- `success`: 请求是否成功 (boolean)
- `code`: 响应状态码 (integer)
- `message`: 响应消息 (string)
- `data`: 响应数据 (object/array)
- `timestamp`: 时间戳 (long)
- `traceId`: 链路追踪ID (string)
- `pagination`: 分页信息 (object, 可选)
- `extra`: 扩展信息 (object, 可选)

## 1. 套餐管理接口

### 1.1 获取所有可用套餐

**接口地址：** `GET /packages`

**接口描述：** 获取所有启用的套餐及其价格信息

**请求参数：** 无

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "basic",
      "displayName": "基础版",
      "description": "适合个人用户",
      "features": ["基础功能", "邮件支持", "5GB存储"],
      "sortOrder": 1,
      "prices": [
        {
          "id": 1,
          "billingCycle": "MONTH",
          "cycleCount": 1,
          "originalPrice": 99.00,
          "salePrice": 89.00,
          "currency": "CNY",
          "discountPercent": 10,
          "displayText": "1个月"
        },
        {
          "id": 2,
          "billingCycle": "YEAR",
          "cycleCount": 1,
          "originalPrice": 1188.00,
          "salePrice": 899.00,
          "currency": "CNY",
          "discountPercent": 24,
          "displayText": "1年"
        }
      ]
    },
    {
      "id": 2,
      "name": "plus",
      "displayName": "进阶版",
      "description": "适合小团队",
      "features": ["进阶功能", "优先支持", "50GB存储", "API访问"],
      "sortOrder": 2,
      "prices": [
        {
          "id": 3,
          "billingCycle": "MONTH",
          "cycleCount": 1,
          "originalPrice": 199.00,
          "salePrice": 179.00,
          "currency": "CNY",
          "discountPercent": 10,
          "displayText": "1个月"
        }
      ]
    }
  ],
  "traceId": "trace_123456789"
}
```

**前端展示建议：**
- 使用卡片布局展示每个套餐
- `displayName` 作为套餐标题
- `description` 作为套餐描述
- `features` 数组渲染为功能列表（使用 ✓ 图标）
- `prices` 数组渲染为价格选项，支持切换周期
- 如果 `discountPercent > 0`，显示折扣标签
- 按 `sortOrder` 排序显示

### 1.2 获取套餐详情

**接口地址：** `GET /packages/{id}`

**接口描述：** 获取指定套餐的详细信息

**请求参数：**
- `id` (path): 套餐ID

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "获取套餐详情成功",
  "data": {
    "id": 1,
    "name": "basic",
    "displayName": "基础版",
    "description": "适合个人用户",
    "features": ["基础功能", "邮件支持", "5GB存储"],
    "sortOrder": 1,
    "prices": [
      {
        "id": 1,
        "billingCycle": "MONTH",
        "cycleCount": 1,
        "originalPrice": 99.00,
        "salePrice": 89.00,
        "currency": "CNY",
        "discountPercent": 10,
        "displayText": "1个月"
      }
    ]
  },
  "traceId": "trace_123456789"
}
```

### 1.3 获取套餐价格选项

**接口地址：** `GET /packages/{id}/prices`

**接口描述：** 获取指定套餐的所有价格选项

**请求参数：**
- `id` (path): 套餐ID

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "获取价格选项成功",
  "data": [
    {
      "id": 1,
      "billingCycle": "MONTH",
      "cycleCount": 1,
      "originalPrice": 99.00,
      "salePrice": 89.00,
      "currency": "CNY",
      "discountPercent": 10,
      "displayText": "1个月"
    },
    {
      "id": 2,
      "billingCycle": "QUARTER",
      "cycleCount": 3,
      "originalPrice": 297.00,
      "salePrice": 249.00,
      "currency": "CNY",
      "discountPercent": 16,
      "displayText": "3个月"
    },
    {
      "id": 3,
      "billingCycle": "YEAR",
      "cycleCount": 1,
      "originalPrice": 1188.00,
      "salePrice": 899.00,
      "currency": "CNY",
      "discountPercent": 24,
      "displayText": "1年"
    }
  ],
  "traceId": "trace_123456789"
}
```

**前端展示建议：**
- 使用选项卡或按钮组展示不同周期
- 突出显示折扣百分比（如 "省24%"）
- 年付等长周期可以显示 "最划算" 标签
- 显示原价和现价的对比

### 1.4 获取套餐价格详情

**接口地址：** `GET /packages/prices/{priceId}`

**接口描述：** 获取指定价格的详细信息

**请求参数：**
- `priceId` (path): 价格ID

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "获取价格详情成功",
  "data": {
    "id": 1,
    "billingCycle": "MONTH",
    "cycleCount": 1,
    "originalPrice": 99.00,
    "salePrice": 89.00,
    "currency": "CNY",
    "discountPercent": 10,
    "displayText": "1个月",
    "packageName": "基础版",
    "packageDescription": "适合个人用户",
    "packageFeatures": ["基础功能", "邮件支持", "5GB存储"]
  },
  "traceId": "trace_123456789"
}
```

## 2. 订单管理接口

### 2.1 创建订单

**接口地址：** `POST /packages/orders`

**接口描述：** 创建订阅订单

**认证要求：** 需要登录

**请求参数：**
- `packagePriceId` (form): 套餐价格ID (必填)
- `discountCode` (form): 优惠码 (可选)

**请求示例：**
```
POST /packages/orders
Content-Type: application/x-www-form-urlencoded

packagePriceId=1&discountCode=SAVE2024
```

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "订单创建成功",
  "data": {
    "orderId": 12345,
    "orderNo": "ORD20241216123456ABC123",
    "packageName": "基础版",
    "packageFeatures": ["基础功能", "邮件支持", "5GB存储"],
    "billingInfo": "1个月",
    "originalAmount": 99.00,
    "discountAmount": 10.00,
    "finalAmount": 89.00,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-16T12:30:00",
    "createdAt": "2024-12-16T12:00:00",
    "traceId": "trace_123456789"
  },
  "traceId": "trace_123456789"
}
```

**前端处理建议：**
- 创建成功后立即跳转到支付页面
- 显示订单摘要信息（套餐名称、价格、优惠等）
- 显示订单过期时间倒计时
- 保存 `orderId` 用于后续支付

### 2.2 验证优惠码

**接口地址：** `POST /packages/discount-codes/validate`

**接口描述：** 验证优惠码是否可用

**认证要求：** 需要登录

**请求参数：**
- `discountCode` (form): 优惠码 (必填)
- `packageId` (form): 套餐ID (必填)
- `amount` (form): 订单金额 (必填)

**请求示例：**
```
POST /packages/discount-codes/validate
Content-Type: application/x-www-form-urlencoded

discountCode=SAVE2024&packageId=1&amount=99.00
```

**响应示例：**
```json
{
  "success": false,
  "code": 61050,
  "message": "优惠码不存在",
  "data": null,
  "traceId": "trace_123456789"
}
```

**前端处理建议：**
- 在用户输入优惠码后实时验证
- 显示验证结果（成功/失败原因）
- 成功时显示优惠金额
- 失败时显示错误提示

## 3. 支付接口

### 3.1 获取可用支付方式

**接口地址：** `GET /payment/methods`

**接口描述：** 获取当前可用的支付方式列表

**请求参数：**
- `amount` (query): 支付金额，默认 0.01

**请求示例：**
```
GET /payment/methods?amount=89.00
```

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "操作成功",
  "data": [
    {
      "methodCode": "xunhupay_auto",
      "methodName": "支付宝/微信支付",
      "methodType": "GATEWAY",
      "provider": "xunhupay",
      "iconUrl": "https://example.com/icons/xunhupay.png",
      "description": "支持支付宝和微信支付，自动适配PC和手机端",
      "minAmount": 0.01,
      "maxAmount": 50000.00,
      "priority": 100,
      "recommended": true
    }
  ],
  "traceId": "trace_123456789"
}
```

**前端展示建议：**
- 使用图标 + 名称的方式展示支付方式
- 推荐的支付方式显示 "推荐" 标签
- 按 `priority` 排序显示
- 显示支付方式描述信息

### 3.2 创建支付订单

**接口地址：** `POST /payment/create`

**接口描述：** 创建支付订单，获取支付链接

**认证要求：** 需要登录

**请求参数：**
- `orderId` (form): 订单ID (必填)
- `paymentMethodCode` (form): 支付方式代码 (必填)
- `returnUrl` (form): 支付成功跳转URL (可选)
- `cancelUrl` (form): 支付取消跳转URL (可选)

**请求示例：**
```
POST /payment/create
Content-Type: application/x-www-form-urlencoded

orderId=12345&paymentMethodCode=xunhupay_auto&returnUrl=https://example.com/success&cancelUrl=https://example.com/cancel
```

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "支付订单创建成功",
  "data": {
    "paymentNo": "PAY20241216123456ABC123",
    "orderNo": "ORD20241216123456ABC123",
    "amount": 89.00,
    "paymentUrl": "https://api.xunhupay.com/pay/...",
    "qrCodeUrl": "https://api.xunhupay.com/qrcode/...",
    "expiredAt": "2024-12-16T12:30:00",
    "traceId": "trace_123456789"
  },
  "traceId": "trace_123456789"
}
```

**前端处理建议：**
- **移动端**：直接跳转到 `paymentUrl`
- **PC端**：显示 `qrCodeUrl` 二维码供用户扫码
- 显示支付金额和过期时间
- 开始轮询支付状态
- 保存 `paymentNo` 用于状态查询

### 3.3 查询支付状态

**接口地址：** `GET /payment/status/{paymentNo}`

**接口描述：** 查询支付订单状态

**认证要求：** 需要登录

**请求参数：**
- `paymentNo` (path): 支付单号

**请求示例：**
```
GET /payment/status/PAY20241216123456ABC123
```

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "操作成功",
  "data": {
    "paymentNo": "PAY20241216123456ABC123",
    "status": "SUCCESS",
    "amount": 89.00,
    "paidAt": "2024-12-16T12:15:00",
    "createdAt": "2024-12-16T12:00:00"
  },
  "traceId": "trace_123456789"
}
```

**支付状态说明：**
- `PENDING`: 待支付
- `SUCCESS`: 支付成功
- `FAILED`: 支付失败
- `CANCELLED`: 支付取消
- `EXPIRED`: 支付过期

**前端处理建议：**
- 每3-5秒轮询一次支付状态
- 支付成功时停止轮询，显示成功页面
- 支付失败时显示失败原因
- 支付过期时提示重新创建订单

### 3.4 支付成功页面

**接口地址：** `GET /payment/success`

**接口描述：** 支付成功后的跳转页面

**请求参数：**
- `trade_order_id` (query): 商户订单号 (可选)
- `payment_no` (query): 支付单号 (可选)

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "支付成功",
  "data": {
    "status": "success",
    "message": "支付成功",
    "tradeOrderId": "TXN1703123456ABC123",
    "paymentNo": "PAY20241216123456ABC123",
    "timestamp": 1703123456789
  },
  "traceId": "trace_123456789"
}
```

### 3.5 支付取消页面

**接口地址：** `GET /payment/cancel`

**接口描述：** 支付取消后的跳转页面

**请求参数：**
- `trade_order_id` (query): 商户订单号 (可选)
- `payment_no` (query): 支付单号 (可选)

**响应示例：**
```json
{
  "success": true,
  "code": 20000,
  "message": "支付已取消",
  "data": {
    "status": "cancel",
    "message": "支付已取消",
    "tradeOrderId": "TXN1703123456ABC123",
    "paymentNo": "PAY20241216123456ABC123",
    "timestamp": 1703123456789
  },
  "traceId": "trace_123456789"
}
```

## 4. 完整支付流程

### 4.1 前端支付流程图

```
1. 用户选择套餐
   ↓
2. 获取所有套餐和价格详情
   ↓
3. 用户选择价格周期
   ↓
4. [可选] 输入优惠码并验证 (POST /packages/discount-codes/validate)
   ↓
5. 创建订单 (POST /packages/orders)
   ↓
6. 获取支付方式 (GET /payment/methods)
   ↓
7. 用户选择支付方式
   ↓
8. 创建支付订单 (POST /payment/create)
   ↓
9. 跳转支付页面或显示二维码
   ↓
10. 轮询支付状态 (GET /payment/status/{paymentNo})
    ↓
11. 支付完成，显示结果页面
```

已经有的mock数据.按照这个设计进行前端展示设计

```sql
-- ==========================================
-- AI应用套餐Mock数据 (纯文本数组Features)
-- ==========================================

-- 插入套餐数据
INSERT INTO `packages` (`name`, `display_name`, `description`, `features`, `status`, `sort_order`) VALUES
('free', 'Free 免费版', '注册即可使用，每天5次对话额度，体验所有AI模型功能', 
 JSON_ARRAY(
    '每天5次对话',
    '所有AI模型可用 (GPT-3.5, GPT-4, GPT-4o, Claude)',
    '文档上传分析',
    '图像生成功能',
    '语音对话',
    '历史记录保留7天',
    '社区支持'
 ), 1, 1),

('plus', 'Plus 进阶版', '3小时内50次提问，取消每日限制，适合个人用户和轻度商用', 
 JSON_ARRAY(
    '*3小时内50次提问',
    '所有AI模型可用 (GPT-3.5, GPT-4, GPT-4o, Claude)',
    '文档上传分析',
    '图像生成功能',
    '语音对话',
    '永久历史记录',
    '优先响应 (比免费版快2倍)',
    '邮件客服支持'
 ), 1, 2),

('pro', 'Pro 专业版', '3小时内100次提问，适合重度使用者和企业用户，专属客服支持', 
 JSON_ARRAY(
    '*3小时内100次提问',
    '所有AI模型可用 (GPT-3.5, GPT-4, GPT-4o, Claude)',
    'API接口访问',
    '文档上传分析',
    '图像生成功能',
    '语音对话',
    '永久历史记录',
    '最高优先级响应 (VIP通道)',
    '专属客服支持 (1对1服务)'
 ), 1, 3);

-- 插入套餐价格数据
-- Free套餐
INSERT INTO `package_prices` (`package_id`, `billing_cycle`, `cycle_count`, `original_price`, `sale_price`, `currency`, `status`) VALUES
((SELECT id FROM packages WHERE name = 'free'), 'MONTH', 1, 0.00, 0.00, 'CNY', 1);

-- Plus套餐价格
INSERT INTO `package_prices` (`package_id`, `billing_cycle`, `cycle_count`, `original_price`, `sale_price`, `currency`, `status`) VALUES
-- Plus 月付
((SELECT id FROM packages WHERE name = 'plus'), 'MONTH', 1, 60.00, 60.00, 'CNY', 1),
-- Plus 季付 (9折优惠)
((SELECT id FROM packages WHERE name = 'plus'), 'QUARTER', 3, 180.00, 162.00, 'CNY', 1),
-- Plus 年付 (8折优惠)
((SELECT id FROM packages WHERE name = 'plus'), 'YEAR', 12, 720.00, 576.00, 'CNY', 1);

-- Pro套餐价格
INSERT INTO `package_prices` (`package_id`, `billing_cycle`, `cycle_count`, `original_price`, `sale_price`, `currency`, `status`) VALUES
-- Pro 月付
((SELECT id FROM packages WHERE name = 'pro'), 'MONTH', 1, 100.00, 100.00, 'CNY', 1),
-- Pro 季付 (9折优惠)
((SELECT id FROM packages WHERE name = 'pro'), 'QUARTER', 3, 300.00, 270.00, 'CNY', 1),
-- Pro 年付 (8折优惠)
((SELECT id FROM packages WHERE name = 'pro'), 'YEAR', 12, 1200.00, 960.00, 'CNY', 1);

-- ==========================================
-- 查询示例
-- ==========================================

-- 查看所有套餐的功能特性
SELECT 
    p.name,
    p.display_name,
    p.features
FROM packages p 
WHERE p.status = 1
ORDER BY p.sort_order;

-- 获取前端完整数据
SELECT 
    p.id,
    p.name,
    p.display_name,
    p.description,
    p.features,
    -- 关联价格信息
    (SELECT JSON_ARRAYAGG(
        JSON_OBJECT(
            'billing_cycle', pp.billing_cycle,
            'cycle_count', pp.cycle_count,
            'original_price', pp.original_price,
            'sale_price', pp.sale_price,
            'currency', pp.currency
        )
    ) FROM package_prices pp WHERE pp.package_id = p.id AND pp.status = 1) as prices
FROM packages p 
WHERE p.status = 1
ORDER BY p.sort_order;

-- 搜索包含特定功能的套餐
SELECT 
    p.name,
    p.display_name
FROM packages p 
WHERE JSON_CONTAINS(p.features, '"API接口访问"')
ORDER BY p.sort_order;

-- 管理操作示例
-- 1. 更新套餐功能列表
UPDATE packages 
SET features = JSON_ARRAY(
    '*3小时内60次提问',
    '所有AI模型可用 (GPT-3.5, GPT-4, GPT-4o, Claude)',
    '文档上传分析',
    '图像生成功能',
    '语音对话',
    '永久历史记录',
    '优先响应',
    '邮件客服支持',
    '新功能测试'
)
WHERE name = 'plus';

-- 2. 给套餐添加新功能
UPDATE packages 
SET features = JSON_ARRAY_APPEND(features, '$', '团队协作功能')
WHERE name = 'pro';

-- 3. 删除套餐中的某个功能 (需要重建数组)
-- 先查询当前功能，然后重新设置去掉不要的功能
```



## 5. 错误处理

### 5.1 常见错误码

| 错误码 | 错误信息 | 处理建议 |
|--------|----------|----------|
| 40100 | 未授权访问 | 跳转到登录页面 |
| 40403 | 订单不存在 | 提示用户重新创建订单 |
| 61010 | 套餐不存在 | 刷新套餐列表 |
| 61012 | 套餐价格不存在 | 重新选择价格选项 |
| 61032 | 支付方式不存在 | 重新获取支付方式列表 |
| 61050 | 优惠码不存在 | 提示用户检查优惠码 |



## 6. 注意事项

1. **认证处理**：需要登录的接口必须在请求头中携带 `satoken`
2. **轮询频率**：支付状态轮询建议间隔3-5秒，避免过于频繁
3. **超时处理**：支付订单有30分钟有效期，需要显示倒计时
4. **移动端适配**：根据设备类型选择跳转支付页面或显示二维码
5. **错误重试**：网络错误时可以自动重试，业务错误需要用户手动处理
6. **链路追踪**：所有响应都包含 `traceId`，可用于问题排查

## 7. 测试数据

### 7.1 测试套餐数据
- 基础版 (basic): 月付 ¥89, 年付 ¥899
- 进阶版 (plus): 月付 ¥179, 年付 ¥1799  
- 专业版 (pro): 月付 ¥359, 年付 ¥3599

### 7.2 测试支付方式
- 虎皮椒支付 (xunhupay_auto): 支持支付宝/微信

### 7.3 测试优惠码
- 当前系统中优惠码功能返回"不存在"，可用于测试错误处理流程

这个文档涵盖了完整的支付订阅流程，前端可以根据这个文档进行开发和集成。 