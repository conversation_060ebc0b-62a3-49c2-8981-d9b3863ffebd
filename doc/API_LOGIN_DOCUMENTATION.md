# 登录接口文档

## 接口概述

本文档描述了基于Sa-Token框架的用户登录认证系统，集成了设备指纹验证、登录失败计数、风险评估等安全机制。

## 基础信息

- **Base URL**: `http://your-domain.com/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **认证框架**: Sa-Token
- **会话管理**: Cookie + Redis

## 1. 用户登录

### 接口信息
- **URL**: `/auth/login`
- **Method**: `POST`
- **描述**: 用户邮箱密码登录，集成设备指纹验证、登录失败计数、多维度限流等安全机制
- **实现类**: `AuthController.login()` -> `EnhancedAuthService.login()`

### 请求参数

#### Headers
```
Content-Type: application/json
User-Agent: 浏览器用户代理 (必需，用于设备识别)
X-Visitor-Id: 访客ID (自动生成，用于设备追踪)
X-Device-Hash: 设备指纹哈希 (自动生成)
```

#### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "userPassword123",
  "rememberMe": false
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址，用于身份验证 |
| password | string | 是 | 用户密码，使用MD5加密验证 |
| rememberMe | boolean | 否 | 是否记住登录状态，false时Token有效期2小时，true时使用默认有效期 |

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 20000,
  "message": "登录成功",
  "data": {
    "email": "<EMAIL>"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

#### 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| code | integer | 响应状态码，20000表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.email | string | 登录成功的用户邮箱 |
| timestamp | string | 响应时间戳 |
| traceId | string | 请求追踪ID，用于日志追踪 |

**注意**: 
- 登录成功后，Sa-Token会自动在Cookie中设置会话信息，前端无需手动处理Token
- 系统会自动记录登录日志到`login_logs`表
- 会重置该用户、IP、设备的登录失败计数

### 错误响应

#### 1. 参数校验失败 (400)
```json
{
  "code": 40001,
  "message": "参数校验失败",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 请求参数格式错误、缺少必填字段等

#### 2. 邮箱或密码错误 (400)
```json
{
  "code": 60101,
  "message": "邮箱或密码错误",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 
- 邮箱不存在
- 密码错误（MD5验证失败）
- 系统会自动增加失败计数（用户、IP、设备三个维度）

#### 3. 账号已被锁定 (403)
```json
{
  "code": 40303,
  "message": "账号已被锁定",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 
- 用户状态为LOCKED
- 用户的`locked_until`字段未过期
- 返回消息会包含剩余锁定时间

#### 4. 账号已被禁用 (403)
```json
{
  "code": 40302,
  "message": "账号已被禁用",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 用户状态为DISABLED

#### 5. 账号已注销 (403)
```json
{
  "code": 40302,
  "message": "账号已注销",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 用户状态为DELETED

#### 6. 登录过于频繁 (429)
```json
{
  "code": 42900,
  "message": "登录过于频繁，请稍后重试",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 
- 用户维度：超过用户登录频率限制
- 消息：`"登录过于频繁，请稍后重试"`

#### 7. IP登录过于频繁 (429)
```json
{
  "code": 42900,
  "message": "该IP登录过于频繁，请稍后重试",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: IP维度超过登录频率限制

#### 8. 设备登录过于频繁 (429)
```json
{
  "code": 42900,
  "message": "该设备登录过于频繁，请稍后重试",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 设备维度超过登录频率限制

#### 9. 系统异常 (500)
```json
{
  "code": 50000,
  "message": "登录失败，请稍后重试",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 系统内部异常，会记录失败日志

## 2. 用户注销

### 接口信息
- **URL**: `/auth/logout`
- **Method**: `POST`
- **描述**: 用户注销登录，清除Sa-Token会话
- **实现类**: `AuthController.logout()`

### 请求参数

#### Headers
```
Content-Type: application/json
Cookie: satoken={token_value} (Sa-Token自动管理)
```

#### Request Body
```json
{}
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 20000,
  "message": "注销成功",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

**注意**: 
- 注销操作总是返回成功，即使Token已过期或无效
- Sa-Token会自动清除Cookie中的会话信息
- 系统会记录注销日志

## 3. 强制下线其他会话

### 接口信息
- **URL**: `/auth/logout-others`
- **Method**: `POST`
- **描述**: 强制下线除当前会话外的所有其他会话，用于账户安全管理
- **实现类**: `AuthController.logoutOtherSessions()`

### 请求参数

#### Headers
```
Content-Type: application/json
Cookie: satoken={token_value} (Sa-Token自动管理)
```

#### Request Body
```json
{}
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 20000,
  "message": "已强制下线3个其他会话",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

**说明**: 
- 消息中的数字表示实际下线的会话数量
- 当前会话不会被下线
- 系统最大允许8个并发会话

#### 错误响应

#### 1. 未登录 (401)
```json
{
  "code": 40104,
  "message": "请先登录",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```
**触发条件**: 用户未登录或会话已过期

## 响应码说明

### 成功状态码 (200xx)
| 状态码 | 说明 |
|--------|------|
| 20000 | 操作成功 |
| 20001 | 创建成功 |
| 20002 | 更新成功 |
| 20003 | 删除成功 |

### 客户端错误 (400xx)
| 状态码 | 说明 |
|--------|------|
| 40000 | 请求参数错误 |
| 40001 | 参数校验失败 |
| 40002 | 缺少必要参数 |
| 40003 | 参数格式不正确 |

### 认证错误 (401xx)
| 状态码 | 说明 |
|--------|------|
| 40100 | 未授权访问 |
| 40101 | Token已过期 |
| 40102 | Token无效 |
| 40103 | 缺少Token |
| 40104 | 请先登录 |

### 授权错误 (403xx)
| 状态码 | 说明 |
|--------|------|
| 40300 | 访问被禁止 |
| 40301 | 权限不足 |
| 40302 | 账号已被禁用 |
| 40303 | 账号已被锁定 |

### 限流错误 (429xx)
| 状态码 | 说明 |
|--------|------|
| 42900 | 请求过于频繁 |
| 42901 | 登录尝试次数过多 |
| 42902 | 短信发送次数超限 |
| 42903 | API调用频率超限 |

### 业务错误 (600xx)

#### 用户相关 (601xx)
| 状态码 | 说明 |
|--------|------|
| 60101 | 用户名或密码错误 |
| 60102 | 密码强度不足 |
| 60103 | 原密码不正确 |
| 60104 | 两次输入的密码不一致 |
| 60105 | 邮箱未验证 |
| 60106 | 手机号未验证 |

#### 设备相关 (603xx)
| 状态码 | 说明 |
|--------|------|
| 60301 | 设备未受信任 |
| 60302 | 设备数量超限 |
| 60303 | 设备已受信任 |
| 60304 | 设备指纹不匹配 |
| 60305 | 缺少设备指纹 |
| 60306 | 设备已被拉黑 |
| 60307 | 设备不匹配 |

#### 会话相关 (604xx)
| 状态码 | 说明 |
|--------|------|
| 60401 | 会话已过期 |
| 60402 | 会话无效 |
| 60403 | 并发登录数量超限 |
| 60404 | 会话被强制下线 |
| 60405 | nonce无效 |
| 60406 | 请求频率超限 |

### 安全错误 (700xx)
| 状态码 | 说明 |
|--------|------|
| 70000 | 安全违规 |
| 70101 | 检测到可疑活动 |
| 70102 | 检测到异地登录 |
| 70103 | 异常登录时间 |
| 70104 | 高风险登录 |
| 70105 | IP地址已被黑名单 |
| 70106 | 检测到暴力破解攻击 |

### 服务端错误 (500xx)
| 状态码 | 说明 |
|--------|------|
| 50000 | 服务器内部错误 |
| 50001 | 数据库错误 |
| 50002 | 缓存错误 |
| 50003 | 消息队列错误 |

## 安全特性

### 1. 设备指纹验证
- 系统自动收集设备指纹信息（通过TraceContext）
- 基于User-Agent、IP地址等生成设备标识
- 设备信息用于登录日志记录和安全追踪

### 2. 多维度登录频率限制
基于Redis实现的滑动窗口限流机制：
- **用户维度**: 通过`IRateLimitService.checkUserLoginRate()`检查
- **IP维度**: 通过`IRateLimitService.checkIpLoginRate()`检查  
- **设备维度**: 通过`IRateLimitService.checkDeviceLoginRate()`检查
- 具体限制阈值在`RateLimitServiceImpl`中配置

### 3. 登录失败计数机制
- **失败计数**: 密码错误时自动增加失败次数（用户、IP、设备三个维度）
- **自动重置**: 登录成功时自动重置所有失败计数
- **账户解锁**: 如果用户被锁定，登录成功时自动解锁并记录安全事件
- **实现方法**: `EnhancedAuthService.incrementFailedAttempts()` 和 `resetFailedAttempts()`

### 4. 用户状态检查
系统会检查用户状态（UserStatus枚举）：
- **NORMAL**: 正常状态，允许登录
- **LOCKED**: 锁定状态，拒绝登录并显示剩余锁定时间
- **DISABLED**: 禁用状态，拒绝登录
- **DELETED**: 注销状态，拒绝登录

### 5. 会话管理
- **并发限制**: 最多允许8个并发会话，超出时自动踢掉最早的会话
- **会话时间**: rememberMe=false时Token有效期2小时，true时使用默认有效期
- **自动管理**: Sa-Token框架自动管理Cookie和Redis中的会话信息

### 6. 安全事件记录
- 登录成功/失败都会记录到`login_logs`表
- 账户解锁时会记录安全事件到`security_events`表
- 所有操作都有完整的审计日志

## 前端集成建议

### 1. 错误处理
```javascript
// 登录请求示例
async function login(email, password, rememberMe = false) {
  try {
    const response = await fetch('/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // 重要：包含Cookie以支持Sa-Token
      body: JSON.stringify({
        email,
        password,
        rememberMe
      })
    });
    
    const result = await response.json();
    
    if (result.code === 20000) {
      // 登录成功，Sa-Token会自动设置Cookie
      console.log('登录成功:', result.data);
      return result.data;
    } else {
      // 登录失败，根据错误码处理
      handleLoginError(result.code, result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('登录请求失败:', error);
    throw error;
  }
}

function handleLoginError(code, message) {
  switch (code) {
    case 60101:
      // 邮箱或密码错误，会增加失败计数
      showError('邮箱或密码错误，请重新输入');
      break;
    case 40303:
      // 账号被锁定，消息中包含剩余时间
      showError(message); // 直接显示服务端消息
      break;
    case 40302:
      // 账号被禁用或注销
      showError('账号异常，请联系客服');
      break;
    case 42900:
      // 频率限制，根据消息区分是用户、IP还是设备限制
      showError(message);
      break;
    case 50000:
      // 系统异常
      showError('系统繁忙，请稍后重试');
      break;
    default:
      showError(message || '登录失败，请重试');
  }
}

function showError(message) {
  // 这里可以替换为你的UI组件
  alert(message);
}
```

### 2. 会话管理
```javascript
// Sa-Token会话管理
class SessionManager {
  // 检查登录状态
  static async checkLoginStatus() {
    try {
      const response = await fetch('/auth/check', {
        method: 'GET',
        credentials: 'include'
      });
      const result = await response.json();
      return result.code === 20000;
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  }
  
  // 注销登录
  static async logout() {
    try {
      const response = await fetch('/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: '{}'
      });
      const result = await response.json();
      return result.code === 20000;
    } catch (error) {
      console.error('注销失败:', error);
      return false;
    }
  }
  
  // 强制下线其他会话
  static async logoutOthers() {
    try {
      const response = await fetch('/auth/logout-others', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: '{}'
      });
      const result = await response.json();
      if (result.code === 20000) {
        alert(result.message); // 显示下线的会话数量
        return true;
      }
      return false;
    } catch (error) {
      console.error('强制下线失败:', error);
      return false;
    }
  }
}
```

### 3. 请求拦截器配置
```javascript
// 配置全局请求拦截器（以axios为例）
axios.defaults.withCredentials = true; // 自动包含Cookie

// 响应拦截器处理认证错误
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response) {
      const { code } = error.response.data;
      if (code === 40104) {
        // 未登录，跳转到登录页
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);
```

## 测试用例

### 1. 正常登录
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }'
```

### 2. 错误密码
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword",
    "rememberMe": false
  }'
```

### 3. 注销登录
```bash
curl -X POST http://localhost:8080/auth/logout \
  -H "Authorization: Bearer {your-token}" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 常见问题

### Q1: 为什么登录后没有返回Token？
A1: 系统使用Sa-Token框架管理会话，Token信息自动存储在Cookie中，前端只需设置`credentials: 'include'`即可自动携带。

### Q2: 如何处理并发登录限制？
A2: 系统默认允许最多8个并发会话，超出限制时会自动踢掉最早的会话。可以使用`/auth/logout-others`接口主动管理会话。

### Q3: 登录失败计数是如何工作的？
A3: 
- 密码错误时会增加用户、IP、设备三个维度的失败计数
- 登录成功时会自动重置所有失败计数
- 如果用户被锁定，登录成功时会自动解锁并记录安全事件

### Q4: 频率限制的具体规则是什么？
A4: 系统实现了多维度频率限制，具体阈值在`RateLimitServiceImpl`中配置。触发限制时会返回不同的错误消息以区分是用户、IP还是设备维度的限制。

### Q5: 如何处理用户状态异常？
A5: 系统会检查用户状态：
- LOCKED: 显示剩余锁定时间
- DISABLED: 提示账号被禁用
- DELETED: 提示账号已注销
- 前端应根据不同状态给出相应的用户提示

### Q6: 设备信息是如何收集的？
A6: 系统通过`TraceContext`自动收集设备信息，包括：
- 访客ID（X-Visitor-Id）
- 设备指纹哈希（X-Device-Hash）
- User-Agent信息
- IP地址
- 这些信息用于安全追踪和日志记录

## 数据库表结构

### login_logs 登录日志表
记录所有登录尝试的详细信息：
```sql
- user_id: 用户ID
- email: 登录邮箱
- device_id: 设备ID
- device_hash: 设备指纹哈希
- ip_address: 登录IP地址
- user_agent: 用户代理信息
- login_type: 登录类型（LoginType枚举）
- login_method: 登录方法（LoginMethod枚举）
- login_result: 登录结果（LoginResult枚举）
- satoken_value: Sa-Token值
- session_timeout: 会话超时时间
- failure_reason: 失败原因
- created_at: 创建时间
```

### security_events 安全事件表
记录重要的安全事件：
```sql
- event_type_name: 事件类型名称
- event_level: 事件级别（INFO/WARNING/DANGER/CRITICAL）
- status: 处理状态（PENDING/HANDLED/IGNORED/AUTO_RESOLVED）
- user_id: 相关用户ID
- device_id: 相关设备ID
- ip_address: 相关IP地址
- event_data: 事件详细数据（JSON格式）
```

---

**注意**: 
- 本文档基于当前API版本编写，如有更新请及时同步文档内容
- 所有接口都支持完整的请求追踪，可通过traceId查询相关日志
- 建议在生产环境中配置适当的日志级别和监控告警 