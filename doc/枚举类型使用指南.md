# 枚举类型使用指南

## 1. 枚举类型设计原则

### UserType枚举定义
```java
@Getter
public enum UserType {
    NORMAL(1, "普通注册"),
    THIRD_PARTY(2, "第三方登录"),
    HYBRID(3, "混合账号");
    
    private final int code;
    private final String description;
    
    UserType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserType fromCode(int code) {
        for (UserType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown user type code: " + code);
    }
}
```

### 数据库字段设计
```sql
-- users表中的user_type字段
`user_type` tinyint DEFAULT '1' COMMENT '1:普通注册 2:第三方登录 3:混合账号'
```

## 2. 枚举类型使用方法

### ✅ 正确的使用方式

#### 2.1 存储到数据库 - 使用code值
```java
// 创建用户时设置用户类型
Users user = new Users();
user.setUserType(UserType.NORMAL.getCode());  // 存储1到数据库

// 其他枚举使用示例
user.setUserType(UserType.THIRD_PARTY.getCode());  // 存储2
user.setUserType(UserType.HYBRID.getCode());       // 存储3
```

#### 2.2 从数据库读取 - 将code转换为枚举
```java
// 从数据库查询用户
Users user = usersService.getById(userId);
int userTypeCode = user.getUserType();

// 将code转换为枚举对象
UserType userType = UserType.fromCode(userTypeCode);

// 使用枚举对象
switch (userType) {
    case NORMAL:
        // 处理普通注册用户逻辑
        break;
    case THIRD_PARTY:
        // 处理第三方登录用户逻辑
        break;
    case HYBRID:
        // 处理混合账号用户逻辑
        break;
}
```

#### 2.3 业务逻辑判断
```java
// 检查用户类型
public boolean isNormalUser(Users user) {
    UserType userType = UserType.fromCode(user.getUserType());
    return userType == UserType.NORMAL;
}

// 根据用户类型执行不同逻辑
public void processUser(Users user) {
    UserType userType = UserType.fromCode(user.getUserType());
    
    if (userType == UserType.NORMAL) {
        // 普通用户可以修改密码
        log.info("普通用户: {}", userType.getDescription());
    } else if (userType == UserType.THIRD_PARTY) {
        // 第三方用户不能修改密码
        log.info("第三方用户: {}", userType.getDescription());
    }
}
```

#### 2.4 条件查询
```java
// 查询特定类型的用户
public List<Users> findUsersByType(UserType userType) {
    LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Users::getUserType, userType.getCode());  // 使用code值查询
    return usersService.list(wrapper);
}

// 查询多种类型的用户
public List<Users> findNormalAndHybridUsers() {
    LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
    wrapper.in(Users::getUserType, 
               UserType.NORMAL.getCode(), 
               UserType.HYBRID.getCode());
    return usersService.list(wrapper);
}
```

### ❌ 错误的使用方式

#### 2.1 直接存储枚举对象（错误）
```java
// ❌ 错误：不能直接存储枚举对象
Users user = new Users();
user.setUserType(UserType.NORMAL);  // 编译错误：类型不匹配
```

#### 2.2 存储枚举名称（不推荐）
```java
// ❌ 不推荐：存储枚举名称字符串
user.setUserType(UserType.NORMAL.name());  // 存储"NORMAL"字符串
```

#### 2.3 硬编码magic number（不推荐）
```java
// ❌ 不推荐：直接使用魔法数字
user.setUserType(1);  // 难以维护，不知道1代表什么
```

## 3. 完整使用示例

### 3.1 注册服务中的使用
```java
@Service
public class UserRegisterServiceImpl implements UserRegisterService {
    
    private Users buildNewUser(UserRegisterRequest request) {
        Users user = new Users();
        
        // 基本信息
        user.setEmail(request.getEmail());
        user.setNickname(generateDefaultNickname(request.getEmail()));
        
        // 密码哈希
        String passwordHash = DigestUtil.md5Hex(request.getPassword());
        user.setPasswordHash(passwordHash);
        
        // ✅ 枚举类型正确使用 - 存储code值
        user.setUserType(UserType.NORMAL.getCode());
        
        // 其他默认值
        user.setPrimaryLoginType("password");
        user.setAccountSource("registration");
        user.setStatus(1);
        user.setEmailVerified(true);
        
        return user;
    }
}
```

### 3.2 用户查询服务中的使用
```java
@Service
public class UserQueryService {
    
    public UserDetailVO getUserDetail(Long userId) {
        Users user = usersService.getById(userId);
        if (user == null) {
            return null;
        }
        
        // ✅ 从数据库code值转换为枚举
        UserType userType = UserType.fromCode(user.getUserType());
        
        return UserDetailVO.builder()
                .userId(user.getId())
                .email(user.getEmail())
                .nickname(user.getNickname())
                .userType(userType.name())              // 返回枚举名称
                .userTypeDesc(userType.getDescription()) // 返回描述
                .build();
    }
}
```

### 3.3 权限检查中的使用
```java
@Component
public class UserPermissionChecker {
    
    public boolean canChangePassword(Users user) {
        UserType userType = UserType.fromCode(user.getUserType());
        
        // 只有普通用户和混合账号用户可以修改密码
        return userType == UserType.NORMAL || userType == UserType.HYBRID;
    }
    
    public boolean canBindThirdParty(Users user) {
        UserType userType = UserType.fromCode(user.getUserType());
        
        // 普通用户可以绑定第三方账号变成混合账号
        return userType == UserType.NORMAL;
    }
}
```

## 4. 最佳实践总结

### 4.1 设计原则
1. **枚举包含code和description**: 便于数据库存储和前端显示
2. **提供fromCode方法**: 便于从数据库值转换为枚举
3. **数据库存储数字**: 节省存储空间，查询效率高
4. **业务逻辑使用枚举**: 类型安全，代码可读性好

### 4.2 使用规范
1. **存储时使用code**: `user.setUserType(UserType.NORMAL.getCode())`
2. **读取时转换枚举**: `UserType userType = UserType.fromCode(user.getUserType())`
3. **业务判断使用枚举**: `if (userType == UserType.NORMAL)`
4. **前端显示使用description**: `userType.getDescription()`

### 4.3 异常处理
```java
public UserType getUserType(Users user) {
    try {
        return UserType.fromCode(user.getUserType());
    } catch (IllegalArgumentException e) {
        log.error("无效的用户类型code: {}, userId: {}", user.getUserType(), user.getId());
        // 返回默认类型或抛出业务异常
        return UserType.NORMAL;
    }
}
```

这样的设计既保证了数据库的高效存储，又保证了代码的类型安全和可维护性。 