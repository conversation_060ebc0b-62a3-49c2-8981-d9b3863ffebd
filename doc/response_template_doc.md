# 企业级响应模板设计文档

## 概述

本文档详细介绍了企业级应用的响应模板设计，包含统一响应格式、错误码体系、异常处理机制等核心组件。该响应模板旨在提供一致的API响应格式，便于前后端对接和系统维护。

## 设计原则

### 1. 一致性原则
- 所有API接口使用统一的响应格式
- 错误码规范化，便于前端统一处理
- 异常信息标准化，提升用户体验

### 2. 可扩展性原则
- 支持业务数据的灵活扩展
- 错误码体系支持按业务模块扩展
- 响应格式支持未来功能迭代

### 3. 类型安全原则
- 使用泛型确保编译期类型检查
- 避免运行时类型转换错误
- 提供强类型的业务数据返回

### 4. 可观测性原则
- 内置链路追踪ID支持
- 统一异常日志记录
- 便于问题排查和系统监控

## 核心组件

### 1. 统一响应DTO (ApiResponse)

#### 基本结构
```java
public class ApiResponse<T> {
    private Boolean success;     // 响应状态
    private Integer code;        // 响应码
    private String message;      // 响应消息
    private T data;             // 响应数据
    private Long timestamp;      // 时间戳
    private String traceId;      // 链路追踪ID
    private PageInfo pagination; // 分页信息（可选）
    private Map<String, Object> extra; // 扩展信息（可选）
}
```

#### 响应示例
```json
{
  "success": true,
  "code": 20000,
  "message": "操作成功",
  "data": {
    "userId": 123,
    "username": "john_doe",
    "email": "<EMAIL>"
  },
  "timestamp": 1640995200000,
  "traceId": "abc123def456789",
  "pagination": null,
  "extra": {
    "executionTime": 150,
    "version": "1.0.0"
  }
}
```

#### 核心方法

**成功响应方法**：
```java
// 简单成功响应
ApiResponse.success()

// 带数据的成功响应
ApiResponse.success(data)

// 带数据和自定义消息
ApiResponse.success(data, "自定义成功消息")

// 分页数据响应
ApiResponse.success(data, pagination)
```

**失败响应方法**：
```java
// 使用预定义错误码
ApiResponse.error(ResponseCode.INVALID_CREDENTIALS)

// 自定义错误码和消息
ApiResponse.error(40001, "参数校验失败")

// 带错误数据的响应
ApiResponse.error(ResponseCode.VALIDATION_ERROR, validationErrors)
```

**链式调用方法**：
```java
ApiResponse.success(data)
    .withTraceId("trace-123")
    .withExtra("executionTime", 150)
    .withExtra("version", "1.0.0")
```

### 2. 分页信息DTO (PageInfo)

#### 结构设计
```java
public class PageInfo {
    private Integer page;        // 当前页码（从1开始）
    private Integer size;        // 每页大小
    private Long total;          // 总记录数
    private Integer pages;       // 总页数
    private Boolean hasPrevious; // 是否有上一页
    private Boolean hasNext;     // 是否有下一页
    private Boolean isFirst;     // 是否为第一页
    private Boolean isLast;      // 是否为最后一页
}
```

#### 创建方法
```java
// 手动创建分页信息
PageInfo pageInfo = PageInfo.of(page, size, total);

// 从Spring Data Page对象创建
PageInfo pageInfo = PageInfo.fromPage(springDataPage);
```

#### 分页响应示例
```json
{
  "success": true,
  "code": 20000,
  "message": "查询成功",
  "data": [
    {"id": 1, "name": "用户1"},
    {"id": 2, "name": "用户2"}
  ],
  "pagination": {
    "page": 1,
    "size": 10,
    "total": 25,
    "pages": 3,
    "hasPrevious": false,
    "hasNext": true,
    "isFirst": true,
    "isLast": false
  }
}
```

## 错误码体系设计

### 编码规则

采用**5位数字编码**系统，便于分类管理和扩展：

| 范围 | 分类 | 说明 | 示例 |
|------|------|------|------|
| 200xx | 成功状态 | 各种操作成功的状态码 | 20000：操作成功 |
| 400xx | 客户端错误 | 请求参数、格式等客户端问题 | 40001：参数校验失败 |
| 401xx | 认证错误 | 身份认证相关错误 | 40101：Token已过期 |
| 403xx | 授权错误 | 权限和访问控制错误 | 40301：权限不足 |
| 404xx | 资源错误 | 资源不存在相关错误 | 40401：用户不存在 |
| 409xx | 冲突错误 | 资源冲突相关错误 | 40901：邮箱已存在 |
| 429xx | 限流错误 | 频率限制相关错误 | 42901：登录尝试过多 |
| 500xx | 服务端错误 | 服务器内部错误 | 50001：数据库错误 |
| 600xx | 业务错误 | 业务逻辑相关错误 | 60101：密码错误 |
| 700xx | 安全错误 | 安全策略相关错误 | 70101：可疑活动 |
| 800xx | 第三方错误 | 外部服务相关错误 | 80001：OAuth服务错误 |
| 900xx | 维护错误 | 系统维护相关错误 | 90000：系统维护中 |

### 具体错误码定义

#### 成功状态码 (200xx)
```java
SUCCESS(20000, "操作成功"),
CREATED(20001, "创建成功"),
UPDATED(20002, "更新成功"),
DELETED(20003, "删除成功")
```

#### 客户端错误 (400xx)
```java
BAD_REQUEST(40000, "请求参数错误"),
VALIDATION_ERROR(40001, "参数校验失败"),
MISSING_PARAMETER(40002, "缺少必要参数"),
INVALID_PARAMETER(40003, "参数格式不正确"),
REQUEST_TOO_LARGE(40004, "请求体过大"),
UNSUPPORTED_MEDIA_TYPE(40005, "不支持的媒体类型")
```

#### 认证错误 (401xx)
```java
UNAUTHORIZED(40100, "未授权访问"),
TOKEN_EXPIRED(40101, "Token已过期"),
TOKEN_INVALID(40102, "Token无效"),
TOKEN_MISSING(40103, "缺少Token"),
LOGIN_REQUIRED(40104, "请先登录")
```

#### 授权错误 (403xx)
```java
FORBIDDEN(40300, "访问被禁止"),
INSUFFICIENT_PERMISSIONS(40301, "权限不足"),
ACCOUNT_DISABLED(40302, "账号已被禁用"),
ACCOUNT_LOCKED(40303, "账号已被锁定")
```

#### 业务错误 (600xx)

**用户相关 (601xx)**：
```java
INVALID_CREDENTIALS(60101, "用户名或密码错误"),
PASSWORD_TOO_WEAK(60102, "密码强度不足"),
OLD_PASSWORD_INCORRECT(60103, "原密码不正确"),
EMAIL_NOT_VERIFIED(60105, "邮箱未验证")
```

**认证相关 (602xx)**：
```java
VERIFICATION_CODE_EXPIRED(60201, "验证码已过期"),
VERIFICATION_CODE_INVALID(60202, "验证码错误"),
TWO_FACTOR_REQUIRED(60204, "需要双因子验证")
```

**设备相关 (603xx)**：
```java
DEVICE_NOT_TRUSTED(60301, "设备未受信任"),
DEVICE_LIMIT_EXCEEDED(60302, "设备数量超限"),
DEVICE_FINGERPRINT_MISMATCH(60304, "设备指纹不匹配")
```

**会话相关 (604xx)**：
```java
SESSION_EXPIRED(60401, "会话已过期"),
SESSION_INVALID(60402, "会话无效"),
CONCURRENT_LOGIN_LIMIT_EXCEEDED(60403, "并发登录数量超限")
```

#### 安全错误 (700xx)
```java
SUSPICIOUS_ACTIVITY(70101, "检测到可疑活动"),
LOCATION_CHANGE_DETECTED(70102, "检测到异地登录"),
HIGH_RISK_LOGIN(70104, "高风险登录"),
IP_BLACKLISTED(70105, "IP地址已被黑名单")
```

## 异常体系设计

### 异常层次结构

```
BusinessException                    // 业务异常基类
├── AuthenticationException          // 认证异常
├── AuthorizationException           // 授权异常
├── ValidationException              // 验证异常
├── ResourceNotFoundException        // 资源不存在异常
├── SecurityException               // 安全异常
├── RateLimitException              // 限流异常
└── ThirdPartyServiceException      // 第三方服务异常
```

### 异常使用示例

#### 1. 认证异常
```java
// 预定义静态方法
throw AuthenticationException.invalidCredentials();
throw AuthenticationException.tokenExpired();
throw AuthenticationException.loginRequired();

// 自定义消息
throw new AuthenticationException("登录凭据已过期");
```

#### 2. 验证异常
```java
// 缺少参数
throw ValidationException.missingParameter("email");

// 参数格式错误
throw ValidationException.invalidParameter("email", "invalid-email");

// 带验证错误详情
Map<String, String> errors = new HashMap<>();
errors.put("email", "邮箱格式不正确");
errors.put("password", "密码长度不足");
throw new ValidationException("参数校验失败", errors);
```

#### 3. 资源不存在异常
```java
// 用户不存在
throw ResourceNotFoundException.user(userId);

// 设备不存在  
throw ResourceNotFoundException.device(deviceId);

// 自定义资源
throw new ResourceNotFoundException("订单ID: " + orderId);
```

#### 4. 安全异常
```java
// 可疑活动
throw SecurityException.suspiciousActivity("异地登录");

// 设备指纹不匹配
throw SecurityException.deviceFingerprintMismatch();

// 高风险登录
throw SecurityException.highRiskLogin("IP地址异常");
```

## 全局异常处理

### 异常处理器配置

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    // 业务异常处理
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException e) {
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData());
        return ResponseEntity.ok(response);
    }
    
    // 参数校验异常处理
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationException(
        MethodArgumentNotValidException e) {
        
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getFieldErrors().forEach(error -> 
            errors.put(error.getField(), error.getDefaultMessage())
        );
        
        ApiResponse<Map<String, String>> response = ApiResponse.error(
            ResponseCode.VALIDATION_ERROR, errors);
        return ResponseEntity.badRequest().body(response);
    }
    
    // Sa-Token异常处理
    @ExceptionHandler(NotLoginException.class)
    public ResponseEntity<ApiResponse<Void>> handleNotLoginException(NotLoginException e) {
        ResponseCode responseCode = mapSaTokenException(e);
        ApiResponse<Void> response = ApiResponse.error(responseCode);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
}
```

### 异常映射关系

| Sa-Token异常类型 | 响应码 | HTTP状态码 | 说明 |
|-----------------|--------|------------|------|
| NOT_TOKEN | TOKEN_MISSING | 401 | 缺少Token |
| INVALID_TOKEN | TOKEN_INVALID | 401 | Token无效 |
| TOKEN_TIMEOUT | TOKEN_EXPIRED | 401 | Token过期 |
| BE_REPLACED | SESSION_KICKED_OUT | 401 | 会话被顶替 |
| KICK_OUT | SESSION_KICKED_OUT | 401 | 被强制下线 |

## 控制器使用示例

### 1. 登录接口
```java
@PostMapping("/login")
public ResponseEntity<ApiResponse<LoginResponse>> login(
    @Valid @RequestBody LoginRequest request) {
    
    try {
        // 业务逻辑
        LoginResponse response = authService.login(request);
        
        // 成功响应
        return ResponseEntity.ok(
            ApiResponse.success(response, "登录成功")
                .withExtra("loginTime", System.currentTimeMillis())
        );
        
    } catch (AuthenticationException e) {
        // 异常会被全局异常处理器捕获，无需手动处理
        throw e;
    }
}
```

### 2. 分页查询接口
```java
@GetMapping("/users")
public ResponseEntity<ApiResponse<List<UserVO>>> getUsers(
    @RequestParam(defaultValue = "1") int page,
    @RequestParam(defaultValue = "10") int size) {
    
    // 业务逻辑
    Page<UserVO> userPage = userService.getUsers(page - 1, size);
    
    // 构建分页信息
    PageInfo pagination = PageInfo.fromPage(userPage);
    
    // 返回分页响应
    return ResponseEntity.ok(
        ApiResponse.success(userPage.getContent(), pagination)
    );
}
```

### 3. 参数校验接口
```java
@PostMapping("/users")
public ResponseEntity<ApiResponse<UserVO>> createUser(
    @Valid @RequestBody CreateUserRequest request) {
    
    // 业务校验
    if (userService.emailExists(request.getEmail())) {
        throw new BusinessException(ResponseCode.EMAIL_ALREADY_EXISTS);
    }
    
    // 业务逻辑
    UserVO user = userService.createUser(request);
    
    return ResponseEntity.ok(
        ApiResponse.success(user, "用户创建成功")
    );
}
```

### 4. 设备管理接口
```java
@PostMapping("/devices/{deviceId}/trust")
public ResponseEntity<ApiResponse<Void>> trustDevice(
    @PathVariable String deviceId,
    @AuthenticationPrincipal UserPrincipal user) {
    
    // 资源存在性校验
    if (!deviceService.deviceExists(user.getUserId(), deviceId)) {
        throw ResourceNotFoundException.device(deviceId);
    }
    
    // 业务逻辑
    deviceService.trustDevice(user.getUserId(), deviceId);
    
    return ResponseEntity.ok(
        ApiResponse.success(null, "设备已设为受信任")
    );
}
```

## 前端对接指南

### 1. 响应数据结构
前端接收到的响应数据统一为以下结构：
```typescript
interface ApiResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: number;
  traceId?: string;
  pagination?: PageInfo;
  extra?: Record<string, any>;
}

interface PageInfo {
  page: number;
  size: number;
  total: number;
  pages: number;
  hasPrevious: boolean;
  hasNext: boolean;
  isFirst: boolean;
  isLast: boolean;
}
```

### 2. 错误处理建议
```javascript
// Axios响应拦截器
axios.interceptors.response.use(
  response => {
    const { data } = response;
    
    if (data.success) {
      return data;
    } else {
      // 统一错误处理
      handleApiError(data);
      return Promise.reject(data);
    }
  },
  error => {
    console.error('请求失败:', error);
    return Promise.reject(error);
  }
);

// 错误处理函数
function handleApiError(apiResponse) {
  const { code, message } = apiResponse;
  
  switch (Math.floor(code / 100)) {
    case 401: // 认证错误
      // 跳转登录页
      router.push('/login');
      break;
    case 403: // 授权错误
      ElMessage.error('权限不足');
      break;
    case 429: // 限流错误
      ElMessage.warning('操作过于频繁，请稍后再试');
      break;
    case 600: // 业务错误
      ElMessage.error(message);
      break;
    case 700: // 安全错误
      ElMessage.error('安全验证失败：' + message);
      break;
    default:
      ElMessage.error('操作失败：' + message);
  }
}
```

### 3. 分页组件对接
```vue
<template>
  <div>
    <!-- 数据列表 -->
    <el-table :data="userList" v-loading="loading">
      <!-- 表格列定义 -->
    </el-table>
    
    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const userList = ref([]);
const loading = ref(false);
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  pages: 0
});

async function fetchUsers() {
  loading.value = true;
  try {
    const response = await api.get('/users', {
      params: {
        page: pagination.value.page,
        size: pagination.value.size
      }
    });
    
    userList.value = response.data;
    pagination.value = response.pagination;
  } catch (error) {
    console.error('获取用户列表失败:', error);
  } finally {
    loading.value = false;
  }
}

function handleSizeChange(newSize) {
  pagination.value.size = newSize;
  pagination.value.page = 1;
  fetchUsers();
}

function handleCurrentChange(newPage) {
  pagination.value.page = newPage;
  fetchUsers();
}

onMounted(() => {
  fetchUsers();
});
</script>
```

## 最佳实践

### 1. 错误码使用规范
- **一致性**：同类型错误使用统一错误码
- **语义化**：错误码编号要有明确的业务含义
- **文档化**：所有错误码都要有详细的说明文档
- **版本控制**：错误码变更要有版本管理

### 2. 异常处理规范
- **就近处理**：在最合适的层次处理异常
- **转换规范**：将底层异常转换为业务异常
- **日志记录**：重要异常要记录详细日志
- **用户友好**：错误信息要对用户友好

### 3. 响应数据规范
- **数据完整性**：确保响应数据的完整性和一致性
- **性能考虑**：避免返回过大的数据对象
- **安全考虑**：敏感信息不要在响应中暴露
- **向前兼容**：响应格式变更要保持向前兼容

### 4. 分页查询规范
- **默认值**：设置合理的分页默认值
- **最大限制**：设置每页最大记录数限制
- **性能优化**：大数据量查询要考虑性能优化
- **缓存策略**：适当使用缓存提升查询性能

### 5. 链路追踪规范
- **全链路**：确保整个请求链路都有traceId
- **传递机制**：在微服务间正确传递traceId
- **日志关联**：所有日志都要包含traceId
- **监控集成**：与APM系统集成进行链路监控

## 扩展指南

### 1. 新增错误码
当需要新增错误码时，按照以下步骤：

1. **确定分类**：根据错误类型确定错误码范围
2. **选择编号**：在对应范围内选择未使用的编号
3. **添加枚举**：在ResponseCode枚举中添加新错误码
4. **更新文档**：更新错误码文档说明
5. **测试验证**：编写测试用例验证新错误码

### 2. 新增异常类型
当需要新增异常类型时：

1. **继承基类**：继承BusinessException基类
2. **定义构造器**：提供便捷的构造器方法
3. **静态方法**：提供常用的静态创建方法
4. **异常处理**：在全局异常处理器中添加处理逻辑
5. **使用示例**：编写使用示例和文档

### 3. 响应格式扩展
当需要扩展响应格式时：

1. **向前兼容**：确保不破坏现有API兼容性
2. **可选字段**：新增字段设置为可选
3. **默认值**：为新字段提供合理默认值
4. **文档更新**：更新API文档和前端对接指南
5. **版本管理**：通过API版本管理变更

## 总结

本响应模板设计提供了：

1. **统一的响应格式**：确保所有API接口响应格式一致
2. **完善的错误码体系**：5位数字编码，分类清晰，易于扩展
3. **分层异常处理**：从业务异常到全局异常处理的完整体系
4. **类型安全保证**：泛型设计确保编译期类型检查
5. **可观测性支持**：内置链路追踪和异常日志记录
6. **前端友好**：标准化的数据格式便于前端统一处理

该设计既满足了当前业务需求，又为未来扩展提供了良好的基础架构。通过合理使用这套响应模板，可以显著提升API接口的一致性、可维护性和用户体验。