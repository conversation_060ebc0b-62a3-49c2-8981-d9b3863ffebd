# RegisterController 接口文档

## 概述

用户注册相关接口，包含会话初始化、发送验证码、用户注册三个步骤。采用基于JWT的会话管理和nonce防重放机制，确保接口安全性。

**基础URL**: `/register`

**技术栈**: Spring Boot + JWT + Redis + 设备指纹

---

## 安全机制说明

### 会话管理
- **JWT令牌**: 10分钟有效期，包含设备指纹信息
- **Nonce机制**: 一次性使用的随机字符串，防止重放攻击
- **设备绑定**: 基于FingerprintJS的设备指纹验证

### 设备指纹机制
系统使用双重设备指纹来确保安全性：
- **visitorId**: FingerprintJS生成的32位十六进制字符串
- **deviceHash**: 多维度设备信息的SHA-256哈希值

### 调用流程
1. **初始化会话** → 获取sessionToken和初始nonce
2. **发送验证码** → 消费nonce，获取新nonce
3. **完成注册** → 消费nonce，完成注册

---

## 必需的HTTP头信息

**所有接口都需要传递以下设备信息头**：
```http
X-Visitor-ID: <32位十六进制设备指纹ID>
X-Device-Hash: <SHA-256设备哈希>
```

**有会话要求的接口还需要**：
```http
Authorization: Bearer <sessionToken>
X-Session-Nonce: <当前nonce>
```

---

## 接口列表

### 1. 初始化会话

**接口信息**
- **路径**: `POST /register/init-session`
- **描述**: 初始化注册会话，获取会话令牌和初始nonce
- **认证**: 无需认证

**请求头**
```http
Content-Type: application/json
X-Visitor-ID: <32位十六进制设备指纹ID>
X-Device-Hash: <SHA-256设备哈希>
```

**请求参数**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| 无请求体参数，设备信息通过HTTP头传递 | | | |

**成功响应** (200)
```json
{
  "success": true,
  "code": 20000,
  "message": "会话初始化成功",
  "data": {
    "sessionToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "nonce": "abc123def456",
    "expiresIn": 600,
    "expiresAt": 1703123456
  },
  "timestamp": 1703123456789,
  "traceId": "trace_abc123"
}
```

**失败响应示例**
```json
{
  "success": false,
  "code": 60305,
  "message": "缺少设备指纹",
  "data": null,
  "timestamp": 1703123456789,
  "traceId": "trace_abc123"
}
```

---

### 2. 发送验证码

**接口信息**
- **路径**: `POST /register/send-verification-code`
- **描述**: 向指定邮箱发送注册验证码
- **认证**: 需要会话令牌

**请求头**
```http
Content-Type: application/json
Authorization: Bearer <sessionToken>
X-Session-Nonce: <当前nonce>
X-Visitor-ID: <32位十六进制设备指纹ID>
X-Device-Hash: <SHA-256设备哈希>
```

**请求参数**
```json
{
  "email": "<EMAIL>"
}
```

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| email | String | 是 | 邮箱地址 | 标准邮箱格式，最大255字符 |

**成功响应** (200)
```json
{
  "success": true,
  "code": 20000,
  "message": "验证码发送成功",
  "data": {
    "message": "验证码发送成功",
    "nonce": "def456ghi789",
    "nextAllowedAt": null,
    "remainingAttempts": null
  },
  "timestamp": 1703123456789,
  "traceId": "trace_def456"
}
```

**失败响应示例**

*邮箱已存在*
```json
{
  "success": false,
  "code": 60105,
  "message": "该邮箱已被注册",
  "data": null,
  "timestamp": 1703123456789
}
```

*会话令牌无效*
```json
{
  "success": false,
  "code": 40101,
  "message": "缺少Token",
  "data": null,
  "timestamp": 1703123456789
}
```

*设备指纹不匹配*
```json
{
  "success": false,
  "code": 60307,
  "message": "设备不匹配",
  "data": null,
  "timestamp": 1703123456789
}
```

*频率限制*
```json
{
  "success": false,
  "code": 42900,
  "message": "请求过于频繁",
  "data": {
    "nextAllowedAt": 1703123516
  },
  "timestamp": 1703123456789
}
```

---

### 3. 用户注册

**接口信息**
- **路径**: `POST /register/submit`
- **描述**: 完成用户注册，验证验证码并创建用户账户
- **认证**: 需要会话令牌

**请求头**
```http
Content-Type: application/json
Authorization: Bearer <sessionToken>
X-Session-Nonce: <当前nonce>
X-Visitor-ID: <32位十六进制设备指纹ID>
X-Device-Hash: <SHA-256设备哈希>
```

**请求参数**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "code": "123456"
}
```

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| email | String | 是 | 邮箱地址 | 标准邮箱格式，最大255字符 |
| password | String | 是 | 密码 | 6-20位字符 |
| confirmPassword | String | 是 | 确认密码 | 必须与password一致 |
| code | String | 是 | 验证码 | 6位数字 |

**成功响应** (200)
```json
{
  "success": true,
  "code": 20000,
  "message": "注册成功",
  "data": {
    "message": "注册成功",
    "userId": "user_abc123def456",
    "email": "<EMAIL>",
    "registeredAt": "2024-01-01T12:00:00"
  },
  "timestamp": 1703123456789,
  "traceId": "trace_ghi789"
}
```

**失败响应示例**

*验证码错误（前3次）*
```json
{
  "success": false,
  "code": 60202,
  "message": "验证码错误",
  "data": null,
  "timestamp": 1703123456789
}
```

*验证码错误次数过多*
```json
{
  "success": false,
  "code": 60202,
  "message": "验证码错误次数过多，请重新获取验证码",
  "data": null,
  "timestamp": 1703123456789
}
```

*密码不一致*
```json
{
  "success": false,
  "code": 60104,
  "message": "两次输入的密码不一致",
  "data": null,
  "timestamp": 1703123456789
}
```

*邮箱已存在*
```json
{
  "success": false,
  "code": 60105,
  "message": "该邮箱已被注册",
  "data": null,
  "timestamp": 1703123456789
}
```

---

## 错误码说明

### 成功状态码
| 状态码 | 说明 |
|--------|------|
| 20000 | 操作成功 |

### 客户端错误 (4xxxx)
| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 40000 | 请求参数错误 | 检查参数格式和必填项 |
| 40001 | 缺少参数 | 补充必要参数 |
| 40002 | 参数格式不正确 | 修正参数格式 |
| 40101 | 缺少Token | 重新初始化会话 |
| 40102 | Token无效 | 重新初始化会话 |
| 40103 | Token已过期 | 重新初始化会话 |
| 42900 | 请求过于频繁 | 等待冷却时间 |

### 业务错误 (6xxxx)
| 状态码 | 说明 | nonce消费 | 处理建议 |
|--------|------|-----------|----------|
| 60104 | 两次输入的密码不一致 | ❌ 不消费 | 可直接重试 |
| 60105 | 该邮箱已被注册 | ✅ 消费 | 重新初始化会话 |
| 60201 | 验证码已过期 | ❌ 不消费 | 重新发送验证码 |
| 60202 | 验证码错误 | 视情况 | 前3次不消费，第4次消费 |
| 60305 | 缺少设备指纹 | - | 重新收集设备信息 |
| 60307 | 设备不匹配 | - | 重新初始化会话 |
| 60405 | nonce无效 | - | 重新初始化会话 |

---

## 设备指纹生成

### JavaScript 实现（基于FingerprintJS）

```javascript
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import sha256 from 'crypto-js/sha256'

class DeviceFingerprint {
    constructor() {
        this.visitorId = null
        this.deviceHash = null
        this.initialized = false
    }

    /**
     * 初始化设备指纹
     */
    async initialize() {
        if (this.initialized) {
            return { visitorId: this.visitorId, deviceHash: this.deviceHash }
        }

        try {
            // 1. 获取FingerprintJS实例
            const fp = await FingerprintJS.load()
            const result = await fp.get()
            
            // 2. 获取visitorId
            this.visitorId = result.visitorId
            
            // 3. 收集多维度设备信息
            const components = result.components
            const deviceInfo = {
                // 基础信息
                userAgent: components.userAgent.value,
                language: components.language.value,
                languages: components.languages.value,
                
                // 屏幕信息
                screen: `${window.screen.width}x${window.screen.height}`,
                colorDepth: window.screen.colorDepth,
                pixelRatio: window.devicePixelRatio,
                
                // 时区和地理
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timezoneOffset: new Date().getTimezoneOffset(),
                
                // 浏览器特征
                canvas: components.canvas?.value,
                webgl: components.webgl?.value,
                audio: components.audio?.value,
                fonts: components.fonts?.value,
                
                // 硬件信息
                hardwareConcurrency: navigator.hardwareConcurrency,
                deviceMemory: navigator.deviceMemory,
                
                // 平台信息
                platform: components.platform?.value,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack,
                
                // 其他特征
                touchSupport: components.touchSupport?.value,
                vendor: navigator.vendor,
                vendorSub: navigator.vendorSub
            }

            // 4. 生成设备哈希
            const deviceStr = JSON.stringify(deviceInfo, Object.keys(deviceInfo).sort())
            this.deviceHash = sha256(deviceStr).toString()
            
            this.initialized = true
            
            console.log('设备指纹初始化完成:', {
                visitorId: this.visitorId,
                deviceHash: this.deviceHash
            })
            
            return {
                visitorId: this.visitorId,
                deviceHash: this.deviceHash
            }
            
        } catch (error) {
            console.error('设备指纹初始化失败:', error)
            throw new Error('无法生成设备指纹')
        }
    }

    /**
     * 获取设备信息（确保已初始化）
     */
    getDeviceInfo() {
        if (!this.initialized) {
            throw new Error('设备指纹未初始化，请先调用 initialize()')
        }
        return {
            visitorId: this.visitorId,
            deviceHash: this.deviceHash
        }
    }

    /**
     * 验证设备指纹格式
     */
    static validateFingerprint(visitorId, deviceHash) {
        const visitorIdPattern = /^[0-9a-f]{32}$/
        const deviceHashPattern = /^[0-9a-f]{64}$/
        
        return visitorIdPattern.test(visitorId) && deviceHashPattern.test(deviceHash)
    }
}
```

---

## 前端集成示例

### JavaScript 完整实现

```javascript
class RegistrationAPI {
    constructor() {
        this.baseURL = '/register'
        this.sessionToken = null
        this.nonce = null
        this.deviceFingerprint = new DeviceFingerprint()
    }

    /**
     * 初始化设备指纹（必须最先调用）
     */
    async initializeDevice() {
        return await this.deviceFingerprint.initialize()
    }

    /**
     * 获取通用请求头
     */
    getCommonHeaders() {
        const { visitorId, deviceHash } = this.deviceFingerprint.getDeviceInfo()
        
        return {
            'Content-Type': 'application/json',
            'X-Visitor-ID': visitorId,
            'X-Device-Hash': deviceHash
        }
    }

    /**
     * 获取认证请求头
     */
    getAuthHeaders() {
        if (!this.sessionToken || !this.nonce) {
            throw new Error('会话未初始化')
        }
        
        return {
            ...this.getCommonHeaders(),
            'Authorization': `Bearer ${this.sessionToken}`,
            'X-Session-Nonce': this.nonce
        }
    }

    /**
     * 1. 初始化会话
     */
    async initSession() {
        // 确保设备指纹已初始化
        if (!this.deviceFingerprint.initialized) {
            await this.initializeDevice()
        }

        const response = await fetch(`${this.baseURL}/init-session`, {
            method: 'POST',
            headers: this.getCommonHeaders()
        })

        const data = await response.json()
        
        if (data.success) {
            this.sessionToken = data.data.sessionToken
            this.nonce = data.data.nonce
            
            // 可选：存储到sessionStorage
            sessionStorage.setItem('sessionToken', this.sessionToken)
            sessionStorage.setItem('nonce', this.nonce)
            
            return data.data
        } else {
            throw new Error(data.message)
        }
    }

    /**
     * 2. 发送验证码
     */
    async sendVerificationCode(email) {
        const response = await fetch(`${this.baseURL}/send-verification-code`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify({ email })
        })

        const data = await response.json()
        
        if (data.success) {
            // 更新nonce
            if (data.data.nonce) {
                this.nonce = data.data.nonce
                sessionStorage.setItem('nonce', this.nonce)
            }
            return data.data
        } else {
            // 处理特定错误
            await this.handleError(data)
            throw new Error(data.message)
        }
    }

    /**
     * 3. 完成注册
     */
    async register(email, password, confirmPassword, code) {
        const response = await fetch(`${this.baseURL}/submit`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify({
                email,
                password,
                confirmPassword,
                code
            })
        })

        const data = await response.json()
        
        if (data.success) {
            // 注册成功，清理会话
            this.cleanup()
            return data.data
        } else {
            // 处理特定错误
            await this.handleError(data)
            throw new Error(data.message)
        }
    }

    /**
     * 错误处理
     */
    async handleError(errorData) {
        const { code, message } = errorData

        switch (code) {
            case 40101: // 缺少Token
            case 40102: // Token无效
            case 40103: // Token已过期
            case 60405: // nonce无效
            case 60307: // 设备不匹配
                console.warn('会话已失效，重新初始化会话')
                await this.initSession()
                break
                
            case 60105: // 邮箱已存在 (nonce已消费)
                console.warn('邮箱已存在，nonce已消费，需重新初始化')
                await this.initSession()
                break
                
            case 60202: // 验证码错误
                if (message.includes('次数过多')) {
                    console.warn('验证码错误次数过多，nonce已消费')
                    await this.initSession()
                }
                // 否则可以直接重试
                break
                
            case 60305: // 缺少设备指纹
                console.warn('设备指纹缺失，重新初始化设备指纹')
                await this.initializeDevice()
                await this.initSession()
                break
                
            case 42900: // 频率限制
                const retryAfter = errorData.data?.nextAllowedAt
                if (retryAfter) {
                    const waitTime = retryAfter - Math.floor(Date.now() / 1000)
                    console.warn(`请等待 ${waitTime} 秒后重试`)
                }
                break
                
            default:
                console.error('未处理的错误:', errorData)
        }
    }

    /**
     * 清理会话数据
     */
    cleanup() {
        this.sessionToken = null
        this.nonce = null
        sessionStorage.removeItem('sessionToken')
        sessionStorage.removeItem('nonce')
    }

    /**
     * 从存储中恢复会话
     */
    restoreSession() {
        this.sessionToken = sessionStorage.getItem('sessionToken')
        this.nonce = sessionStorage.getItem('nonce')
        return this.sessionToken && this.nonce
    }
}
```

### 使用示例

```javascript
// 初始化API客户端
const api = new RegistrationAPI()

// 完整注册流程
async function performRegistration() {
    try {
        // 0. 初始化设备指纹（必须最先执行）
        console.log('初始化设备指纹...')
        await api.initializeDevice()
        console.log('设备指纹初始化成功')

        // 1. 初始化会话（或恢复已有会话）
        if (!api.restoreSession()) {
            console.log('初始化会话...')
            await api.initSession()
            console.log('会话初始化成功')
        }

        // 2. 发送验证码
        console.log('发送验证码...')
        await api.sendVerificationCode('<EMAIL>')
        console.log('验证码发送成功')

        // 3. 用户输入验证码后，完成注册
        console.log('完成注册...')
        const result = await api.register(
            '<EMAIL>',
            'password123',
            'password123',
            '123456' // 用户输入的验证码
        )
        
        console.log('注册成功:', result)
        return result

    } catch (error) {
        console.error('注册失败:', error.message)
        
        // 根据错误类型进行相应处理
        if (error.message.includes('验证码错误次数过多')) {
            alert('验证码错误次数过多，请重新获取验证码')
            // 重新发送验证码
            return performRegistration()
        } else if (error.message.includes('该邮箱已被注册')) {
            alert('该邮箱已被注册，请使用其他邮箱或前往登录')
        } else if (error.message.includes('设备指纹')) {
            alert('设备验证失败，请刷新页面重试')
        } else {
            alert('注册失败：' + error.message)
        }
    }
}

// 调用注册流程
performRegistration()
```

### React Hook 示例

```javascript
import { useState, useCallback, useEffect } from 'react'

export function useRegistration() {
    const [api] = useState(() => new RegistrationAPI())
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)
    const [deviceReady, setDeviceReady] = useState(false)

    // 初始化设备指纹
    useEffect(() => {
        const initDevice = async () => {
            try {
                await api.initializeDevice()
                setDeviceReady(true)
            } catch (err) {
                setError('设备指纹初始化失败：' + err.message)
            }
        }
        
        initDevice()
    }, [api])

    const initSession = useCallback(async () => {
        if (!deviceReady) {
            throw new Error('设备指纹未准备就绪')
        }
        
        if (api.restoreSession()) {
            return true
        }
        
        setLoading(true)
        setError(null)
        
        try {
            await api.initSession()
            return true
        } catch (err) {
            setError(err.message)
            return false
        } finally {
            setLoading(false)
        }
    }, [api, deviceReady])

    const sendCode = useCallback(async (email) => {
        setLoading(true)
        setError(null)
        
        try {
            await api.sendVerificationCode(email)
            return true
        } catch (err) {
            setError(err.message)
            return false
        } finally {
            setLoading(false)
        }
    }, [api])

    const register = useCallback(async (email, password, confirmPassword, code) => {
        setLoading(true)
        setError(null)
        
        try {
            const result = await api.register(email, password, confirmPassword, code)
            return result
        } catch (err) {
            setError(err.message)
            return null
        } finally {
            setLoading(false)
        }
    }, [api])

    return {
        deviceReady,
        initSession,
        sendCode,
        register,
        loading,
        error,
        clearError: () => setError(null)
    }
}
```

---

## 注意事项

### 1. 安全要求
- **HTTPS必需**: 生产环境必须使用HTTPS
- **设备指纹**: 需要集成FingerprintJS库
- **Token存储**: 建议使用sessionStorage而非localStorage
- **敏感信息**: 不要在控制台或日志中输出密码等敏感信息
- **设备验证**: 所有请求必须携带正确的设备指纹信息

### 2. 错误处理
- **网络错误**: 实现重试机制和离线处理
- **会话过期**: 自动重新初始化会话
- **频率限制**: 显示等待时间，避免无效重试
- **用户体验**: 提供清晰的错误提示和操作建议
- **设备指纹失效**: 自动重新生成设备指纹

### 3. 性能优化
- **会话复用**: 在页面刷新时尝试恢复会话
- **请求缓存**: 避免重复的初始化请求
- **加载状态**: 提供合适的加载和禁用状态
- **设备指纹缓存**: 避免重复计算设备指纹

### 4. 调试建议
- **日志追踪**: 每个响应都包含traceId，便于问题排查
- **开发工具**: 使用浏览器开发者工具查看网络请求
- **错误码**: 根据错误码快速定位问题类型
- **设备指纹验证**: 确保visitorId为32位十六进制，deviceHash为64位十六进制

### 5. 浏览器兼容性
- **FingerprintJS支持**: 现代浏览器（IE11+）
- **crypto-js库**: 用于SHA-256哈希计算
- **ES6+特性**: 需要Babel转译支持老旧浏览器

---

## 链路追踪信息

系统会自动在响应头中返回链路追踪信息：
```http
X-Trace-Id: abc123def456789
X-Span-Id: def456789
```

这些信息用于：
- 问题排查和日志关联
- 性能监控和分析
- 分布式服务调用追踪

---

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.1.0 | 2024-01-01 | 增加设备指纹机制和HTTP头说明 |
| 1.0.0 | 2024-01-01 | 初始版本，包含基础注册流程 |

---

## 技术支持

如有问题请联系后端开发团队，提供traceId和设备指纹信息以便快速定位问题。 