# 用户订阅API文档

## 接口概述

用户订阅相关API，用于获取用户当前订阅状态和订阅历史记录，主要用于前端订阅页面展示。

## 接口详情

### 1. 获取用户订阅信息

#### 基本信息
- **接口地址**: `GET /user/subscription`
- **接口描述**: 获取当前登录用户的订阅信息，包含当前有效订阅和最新10条订阅历史记录
- **权限要求**: 需要登录

#### 请求参数
无需请求参数，通过Cookie中的登录信息获取用户ID。

#### 响应格式

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取订阅信息成功",
  "data": {
    "userId": 12345,
    "hasActiveSubscription": true,
    "currentSubscription": {
      "id": 1001,
      "packageId": 2,
      "packageName": "Plus套餐",
      "packageType": "plus",
      "status": "ACTIVE",
      "startTime": "2024-01-15 10:30:00",
      "endTime": "2024-02-15 10:30:00",
      "autoRenewal": 0,
      "remainingDays": 15,
      "expiringSoon": false,
      "limits": null
    },
    "historyList": [
      {
        "id": 5001,
        "action": "UPGRADE",
        "actionDescription": "升级套餐",
        "orderId": 8001,
        "fromPackageId": 1,
        "fromPackageName": "Basic套餐",
        "toPackageId": 2,
        "toPackageName": "Plus套餐",
        "fromEndTime": "2024-01-10 10:30:00",
        "toEndTime": "2024-02-15 10:30:00",
        "paymentAmount": null,
        "createdAt": "2024-01-15 10:30:00"
      },
      {
        "id": 5000,
        "action": "CREATE",
        "actionDescription": "开通订阅",
        "orderId": 8000,
        "fromPackageId": null,
        "fromPackageName": null,
        "toPackageId": 1,
        "toPackageName": "Basic套餐",
        "fromEndTime": null,
        "toEndTime": "2024-01-10 10:30:00",
        "paymentAmount": null,
        "createdAt": "2024-01-01 10:30:00"
      }
    ],
    "totalHistoryCount": 2
  },
  "timestamp": "2024-01-30 15:30:00",
  "traceId": "abc123def456"
}
```

**无订阅响应 (200)**:
```json
{
  "code": 200,
  "message": "获取订阅信息成功",
  "data": {
    "userId": 12345,
    "hasActiveSubscription": false,
    "currentSubscription": null,
    "historyList": [],
    "totalHistoryCount": 0
  },
  "timestamp": "2024-01-30 15:30:00",
  "traceId": "abc123def456"
}
```

**错误响应**:
```json
{
  "code": 401,
  "message": "请先登录",
  "data": null,
  "timestamp": "2024-01-30 15:30:00",
  "traceId": "abc123def456"
}
```

## 数据字段说明

### CurrentSubscriptionDTO (当前订阅信息)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | Long | 订阅ID | 1001 |
| packageId | Long | 套餐ID | 2 |
| packageName | String | 套餐名称 | "Plus套餐" |
| packageType | String | 套餐类型 | "plus" |
| status | String | 订阅状态 | "ACTIVE", "EXPIRED", "CANCELLED" |
| startTime | String | 订阅开始时间 | "2024-01-15 10:30:00" |
| endTime | String | 订阅结束时间 | "2024-02-15 10:30:00" |
| autoRenewal | Integer | 是否自动续费 | 0=否, 1=是 |
| remainingDays | Long | 剩余天数 | 15 |
| expiringSoon | Boolean | 是否即将过期(7天内) | false |
| limits | String | 套餐权限配置(JSON) | null |

### SubscriptionHistoryDTO (订阅历史)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | Long | 历史记录ID | 5001 |
| action | String | 操作类型 | "CREATE", "RENEW", "UPGRADE", "CANCEL" |
| actionDescription | String | 操作描述 | "升级套餐" |
| orderId | Long | 订单ID | 8001 |
| fromPackageId | Long | 原套餐ID | 1 |
| fromPackageName | String | 原套餐名称 | "Basic套餐" |
| toPackageId | Long | 目标套餐ID | 2 |
| toPackageName | String | 目标套餐名称 | "Plus套餐" |
| fromEndTime | String | 原结束时间 | "2024-01-10 10:30:00" |
| toEndTime | String | 新结束时间 | "2024-02-15 10:30:00" |
| paymentAmount | BigDecimal | 支付金额 | null (暂未实现) |
| createdAt | String | 操作时间 | "2024-01-15 10:30:00" |

## 订阅状态说明

### 订阅状态 (status)
- **ACTIVE**: 有效订阅
- **EXPIRED**: 已过期
- **CANCELLED**: 已取消

### 操作类型 (action)
- **CREATE**: 开通订阅
- **RENEW**: 续费订阅（同套餐）
- **UPGRADE**: 升级套餐（不同套餐）
- **CANCEL**: 取消订阅

## 前端展示建议

### 当前订阅区域
1. **有订阅时**:
   - 显示套餐名称和类型
   - 显示订阅状态（用颜色区分：绿色=有效，红色=过期，灰色=取消）
   - 显示剩余天数（如果即将过期用红色提醒）
   - 显示结束时间
   - 显示是否自动续费

2. **无订阅时**:
   - 显示"暂无有效订阅"
   - 提供"立即订阅"按钮

### 订阅历史表格
建议表格列：
- 操作时间 (createdAt)
- 操作类型 (actionDescription)
- 套餐变更 (fromPackageName → toPackageName)
- 时间变更 (fromEndTime → toEndTime)
- 订单号 (orderId)

## 错误处理

常见错误码：
- **401**: 未登录，需要跳转到登录页面
- **500**: 服务器内部错误，显示通用错误提示



## 注意事项

1. **认证**: 该接口需要用户登录，前端需要处理401错误并跳转到登录页面
2. **时间格式**: 所有时间字段均为 `yyyy-MM-dd HH:mm:ss` 格式
3. **历史记录**: 目前只返回最新10条历史记录，如需更多记录可考虑增加分页参数
4. **支付金额**: `paymentAmount` 字段目前返回null，后续版本会完善
5. **套餐权限**: `limits` 字段目前返回null，后续会包含JSON格式的权限配置
6. **缓存**: 建议前端适当缓存数据，避免频繁请求 