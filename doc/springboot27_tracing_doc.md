# Spring Boot 2.7企业级链路追踪实现方案

## 技术架构概述

基于您现有的技术栈：
- **Spring Boot**: 2.7.18
- **Java**: JDK 8
- **数据库**: MySQL 8.0.28 + MyBatis Plus 3.5.7
- **缓存**: Redis
- **认证**: Sa-Token 1.42.0 + JWT
- **工具库**: Hutool 5.8.25
- **AOP**: Spring Boot AOP

## 依赖配置

### 在现有pom.xml中补充链路追踪依赖

```xml
<!-- 监控和链路追踪相关依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>

<!-- Micrometer核心库 -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-core</artifactId>
</dependency>

<!-- Micrometer Prometheus集成 -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
</dependency>

<!-- Logback JSON输出 (用于ELK集成) -->
<dependency>
    <groupId>net.logstash.logback</groupId>
    <artifactId>logstash-logback-encoder</artifactId>
    <version>7.2</version>
</dependency>

```

## 核心组件实现

### 1. TraceId生成与管理

基于Hutool工具类的TraceId管理器：

```java
@Component
@Slf4j
public class TraceContext {
    
    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String SPAN_ID_HEADER = "X-Span-Id";
    private static final String TRACE_ID_MDC_KEY = "traceId";
    private static final String SPAN_ID_MDC_KEY = "spanId";
    
    /**
     * 生成TraceId (使用Hutool的IdUtil)
     */
    public static String generateTraceId() {
        return IdUtil.fastSimpleUUID();
    }
    
    /**
     * 生成SpanId
     */
    public static String generateSpanId() {
        return IdUtil.fastSimpleUUID().substring(0, 16);
    }
    
    /**
     * 设置TraceId到MDC
     */
    public static void setTraceId(String traceId) {
        if (StrUtil.isNotBlank(traceId)) {
            MDC.put(TRACE_ID_MDC_KEY, traceId);
        }
    }
    
    /**
     * 获取当前TraceId
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_MDC_KEY);
    }
    
    /**
     * 从请求头获取TraceId
     */
    public static String extractTraceId(HttpServletRequest request) {
        String traceId = request.getHeader(TRACE_ID_HEADER);
        if (StrUtil.isBlank(traceId)) {
            traceId = request.getHeader("traceid");
        }
        return traceId;
    }
    
    /**
     * 获取客户端真实IP (使用Hutool)
     */
    public static String getClientIP(HttpServletRequest request) {
        return ServletUtil.getClientIP(request);
    }
    
    /**
     * 清理MDC
     */
    public static void clear() {
        MDC.remove(TRACE_ID_MDC_KEY);
        MDC.remove(SPAN_ID_MDC_KEY);
    }
}
```

### 2. Web请求链路追踪拦截器

集成Spring Boot 2.7的WebMvcConfigurer：

```java
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class TraceInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 1. 从请求头获取或生成TraceId
            String traceId = TraceContext.extractTraceId(request);
            if (StrUtil.isBlank(traceId)) {
                traceId = TraceContext.generateTraceId();
            }
            
            // 2. 生成SpanId
            String spanId = TraceContext.generateSpanId();
            
            // 3. 设置到MDC
            TraceContext.setTraceId(traceId);
            TraceContext.setSpanId(spanId);
            
            // 4. 设置到响应头
            response.setHeader("X-Trace-Id", traceId);
            response.setHeader("X-Span-Id", spanId);
            
            // 5. 记录请求开始日志
            String clientIP = TraceContext.getClientIP(request);
            String userAgent = request.getHeader("User-Agent");
            
            log.info("Request started - Method: {}, URI: {}, ClientIP: {}, UserAgent: {}", 
                request.getMethod(), request.getRequestURI(), clientIP, userAgent);
            
            // 6. 设置请求开始时间
            request.setAttribute("requestStartTime", System.currentTimeMillis());
            
            return true;
            
        } catch (Exception e) {
            log.error("链路追踪拦截器异常", e);
            return true; // 不影响正常请求
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        try {
            // 计算请求耗时
            Long startTime = (Long) request.getAttribute("requestStartTime");
            long duration = startTime != null ? System.currentTimeMillis() - startTime : 0;
            
            // 记录请求完成日志
            log.info("Request completed - Status: {}, Duration: {}ms, Exception: {}", 
                response.getStatus(), duration, ex != null ? ex.getClass().getSimpleName() : "None");
            
        } finally {
            // 清理MDC
            TraceContext.clear();
        }
    }
}

/**
 * Web配置类 - 注册拦截器
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private TraceInterceptor traceInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceInterceptor)
                .addPathPatterns("/**")
                .order(Ordered.HIGHEST_PRECEDENCE);
    }
}
```

### 3. MyBatis Plus链路追踪

基于MyBatis Plus的SQL执行拦截器：

```java
/**
 * MyBatis Plus拦截器 - SQL执行链路追踪
 */
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", 
               args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
@Component
@Slf4j
public class MyBatisPlusTraceInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sqlId = mappedStatement.getId();
        String sqlCommandType = mappedStatement.getSqlCommandType().toString();
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = invocation.proceed();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录SQL执行日志（TraceId会自动包含）
            log.info("SQL executed - Type: {}, SqlId: {}, Duration: {}ms", 
                sqlCommandType, sqlId, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("SQL execution failed - Type: {}, SqlId: {}, Duration: {}ms, Error: {}", 
                sqlCommandType, sqlId, duration, e.getMessage());
            throw e;
        }
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
}

/**
 * MyBatis Plus配置类
 */
@Configuration
public class MyBatisPlusConfig {
    
    @Autowired
    private MyBatisPlusTraceInterceptor traceInterceptor;
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        return interceptor;
    }
    
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            // 添加自定义SQL拦截器
            configuration.addInterceptor(traceInterceptor);
        };
    }
}
```

### 4. Redis操作链路追踪

基于Spring AOP的Redis追踪：

```java
/**
 * Redis操作链路追踪切面
 */
@Aspect
@Component
@Slf4j
public class RedisTraceAspect {
    
    @Around("execution(* org.springframework.data.redis.core.RedisTemplate.*(..))")
    public Object traceRedisOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 获取Redis key (如果是字符串参数)
        String redisKey = extractRedisKey(args);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录Redis操作日志
            log.debug("Redis operation - Method: {}, Key: {}, Duration: {}ms", 
                methodName, redisKey, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Redis operation failed - Method: {}, Key: {}, Duration: {}ms, Error: {}", 
                methodName, redisKey, duration, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 提取Redis Key
     */
    private String extractRedisKey(Object[] args) {
        if (args != null && args.length > 0 && args[0] instanceof String) {
            return (String) args[0];
        }
        return "unknown";
    }
}
```

### 5. Sa-Token链路追踪集成

增强Sa-Token的登录和注销事件：

```java
/**
 * Sa-Token事件监听器 - 集成链路追踪
 */
@Component
@Slf4j
public class SaTokenTraceListener implements StpListener {
    
    @Override
    public void doLogin(String loginType, Object loginId, String tokenValue, SaLoginModel loginModel) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token login success - LoginId: {}, TokenValue: {}, Device: {}, TraceId: {}", 
            loginId, tokenValue, loginModel.getDevice(), traceId);
        
        // 可以将TraceId存储到Sa-Token的扩展信息中
        StpUtil.getSession().set("currentTraceId", traceId);
    }
    
    @Override
    public void doLogout(String loginType, Object loginId, String tokenValue) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token logout - LoginId: {}, TokenValue: {}, TraceId: {}", 
            loginId, tokenValue, traceId);
    }
    
    @Override
    public void doKickout(String loginType, Object loginId, String tokenValue) {
        String traceId = TraceContext.getTraceId();
        
        log.warn("Sa-Token kickout - LoginId: {}, TokenValue: {}, TraceId: {}", 
            loginId, tokenValue, traceId);
    }
    
    @Override
    public void doReplaced(String loginType, Object loginId, String tokenValue) {
        log.info("Sa-Token replaced - LoginId: {}, TokenValue: {}", loginId, tokenValue);
    }
    
    @Override
    public void doDisable(String loginType, Object loginId, String service, int level, long disableTime) {
        log.warn("Sa-Token disable - LoginId: {}, Service: {}, Level: {}", loginId, service, level);
    }
    
    @Override
    public void doUntieDisable(String loginType, Object loginId, String service) {
        log.info("Sa-Token untie disable - LoginId: {}, Service: {}", loginId, service);
    }
    
    @Override
    public void doOpenSafe(String loginType, String tokenValue, String service, long safeTime) {
        log.info("Sa-Token open safe - TokenValue: {}, Service: {}", tokenValue, service);
    }
    
    @Override
    public void doCloseSafe(String loginType, String tokenValue, String service) {
        log.info("Sa-Token close safe - TokenValue: {}, Service: {}", tokenValue, service);
    }
    
    @Override
    public void doCreateSession(String id) {
        log.debug("Sa-Token create session - Id: {}", id);
    }
    
    @Override
    public void doLogoutSession(String id) {
        log.debug("Sa-Token logout session - Id: {}", id);
    }
    
    @Override
    public void doRenewTimeout(String tokenValue, Object loginId, long timeout) {
        log.debug("Sa-Token renew timeout - TokenValue: {}, LoginId: {}, Timeout: {}", 
            tokenValue, loginId, timeout);
    }
}
```

### 6. 异步任务链路追踪

基于Spring Boot 2.7的异步配置：

```java
/**
 * 异步任务配置 - 支持TraceId传递
 */
@Configuration
@EnableAsync
public class AsyncTraceConfig {
    
    /**
     * 自定义异步执行器
     */
    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("async-trace-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 设置任务装饰器，传递TraceId
        executor.setTaskDecorator(new TraceableTaskDecorator());
        
        executor.initialize();
        return executor;
    }
    
    /**
     * 支持TraceId传递的任务装饰器
     */
    public static class TraceableTaskDecorator implements TaskDecorator {
        
        @Override
        public Runnable decorate(Runnable runnable) {
            // 获取当前线程的TraceId
            String traceId = TraceContext.getTraceId();
            String spanId = TraceContext.getSpanId();
            
            return () -> {
                try {
                    // 在新线程中设置TraceId
                    TraceContext.setTraceId(traceId);
                    TraceContext.setSpanId(TraceContext.generateSpanId()); // 生成新的SpanId
                    
                    // 执行原任务
                    runnable.run();
                    
                } finally {
                    // 清理MDC
                    TraceContext.clear();
                }
            };
        }
    }
}

/**
 * 异步服务示例
 */
@Service
@Slf4j
public class AsyncTraceService {
    
    /**
     * 异步发送邮件通知（保留TraceId）
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> sendEmailAsync(String email, String subject, String content) {
        log.info("开始异步发送邮件: email={}, subject={}", email, subject);
        
        try {
            // 模拟邮件发送
            Thread.sleep(1000);
            
            log.info("异步邮件发送成功: email={}", email);
            
        } catch (Exception e) {
            log.error("异步邮件发送失败: email={}", email, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
}
```

## 日志配置

### logback-spring.xml配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件路径 -->
    <property name="LOG_HOME" value="logs" />
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- JSON格式日志（用于ELK采集） -->
    <appender name="JSON_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/application.json</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/application.json.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "app": "enterprise-auth-system",
                            "env": "${SPRING_PROFILES_ACTIVE:-dev}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>
    
    <!-- 异步日志 -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="FILE"/>
    </appender>
    
    <appender name="ASYNC_JSON" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="JSON_FILE"/>
    </appender>
    
    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_JSON"/>
    </root>
    
    <!-- 特定包的日志级别 -->
    <logger name="com.jiashu" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_JSON"/>
    </logger>
    
    <!-- MyBatis SQL日志 -->
    <logger name="com.jiashu.mapper" level="DEBUG"/>
    
    <!-- Sa-Token日志 -->
    <logger name="cn.dev33.satoken" level="INFO"/>
    
    <!-- Redis日志 -->
    <logger name="org.springframework.data.redis" level="DEBUG"/>
</configuration>
```

## 应用配置

### application.yml

```yaml
spring:
  application:
    name: enterprise-auth-system
    
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    
  # 数据源配置
  datasource:
    url: *********************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      connection-timeout: 20000
      
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 5
        max-wait: 5000ms

# Sa-Token配置
sa-token:
  token-name: satoken
  timeout: 604800                # 7天
  activity-timeout: -1           # 不使用临时过期机制
  is-concurrent: true            # 允许同一账号多地同时登录
  is-share: false               # 不共享token
  max-login-count: 5            # 同一账号最大登录数量
  is-log: true                  # 开启操作日志
  jwt-secret-key: ${SA_TOKEN_JWT_SECRET:your-very-secure-jwt-secret-key-here-min-256-bits}
  
# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:mapper/*.xml

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      env: ${spring.profiles.active}

# 日志配置
logging:
  level:
    root: INFO
    com.jiashu: DEBUG
    cn.dev33.satoken: INFO
    org.springframework.data.redis: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n'

# 自定义配置
app:
  trace:
    enabled: true
    sample-rate: 1.0              # 采样率 100%
    async-enabled: true           # 异步日志
  security:
    device:
      max-devices-per-user: 10
      trust-threshold: 70
    login:
      max-failed-attempts: 5
      lockout-duration: 15m
```

## 链路追踪工具类

### TraceUtils工具类

```java
/**
 * 链路追踪工具类 - 基于Hutool增强
 */
@Component
@Slf4j
public class TraceUtils {
    
    /**
     * 执行带TraceId的操作
     */
    public static <T> T executeWithTrace(String operation, cn.hutool.core.lang.func.Func0<T> supplier) {
        String traceId = TraceContext.getTraceId();
        String spanId = TraceContext.generateSpanId();
        
        log.info("Operation started - Name: {}, TraceId: {}, SpanId: {}", operation, traceId, spanId);
        
        long startTime = System.currentTimeMillis();
        
        try {
            TraceContext.setSpanId(spanId);
            T result = supplier.call();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("Operation completed - Name: {}, Duration: {}ms", operation, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Operation failed - Name: {}, Duration: {}ms, Error: {}", 
                operation, duration, e.getMessage());
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 记录业务事件
     */
    public static void recordBusinessEvent(String eventType, Object eventData) {
        String traceId = TraceContext.getTraceId();
        
        // 使用Hutool的JSON工具
        String jsonData = cn.hutool.json.JSONUtil.toJsonStr(eventData);
        
        log.info("Business event - Type: {}, TraceId: {}, Data: {}", 
            eventType, traceId, jsonData);
    }
    
    /**
     * 记录性能指标
     */
    public static void recordPerformanceMetric(String metricName, long duration) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Performance metric - Name: {}, Duration: {}ms, TraceId: {}", 
            metricName, duration, traceId);
    }
}
```

## 使用示例

### 在登录服务中的应用

```java
/**
 * 登录服务 - 集成链路追踪
 */
@Service
@Slf4j
public class LoginService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private AsyncTraceService asyncTraceService;
    
    /**
     * 用户登录 - 完整链路追踪
     */
    public LoginResponse login(LoginRequest request) {
        return TraceUtils.executeWithTrace("user-login", () -> {
            
            // 1. 参数验证
            validateLoginRequest(request);
            
            // 2. 用户认证 (会自动记录SQL执行)
            User user = TraceUtils.executeWithTrace("authenticate-user", () -> {
                log.info("开始用户认证: email={}", request.getEmail());
                return userMapper.selectByEmail(request.getEmail());
            });
            
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 3. 密码验证
            TraceUtils.executeWithTrace("verify-password", () -> {
                if (!BCryptUtil.checkpw(request.getPassword(), user.getPasswordHash())) {
                    throw new BusinessException("密码错误");
                }
                return null;
            });
            
            // 4. 生成Token (会自动记录Redis操作)
            String token = TraceUtils.executeWithTrace("generate-token", () -> {
                SaLoginModel loginModel = new SaLoginModel()
                    .setDevice(request.getDeviceId())
                    .setTimeout(7 * 24 * 60 * 60); // 7天
                    
                StpUtil.login(user.getId(), loginModel);
                return StpUtil.getTokenValue();
            });
            
            // 5. 记录业务事件
            Map<String, Object> eventData = MapUtil.builder()
                .put("userId", user.getId())
                .put("email", user.getEmail())
                .put("deviceId", request.getDeviceId())
                .put("loginTime", DateUtil.now())
                .build();
            TraceUtils.recordBusinessEvent("user_login_success", eventData);
            
            // 6. 异步发送通知 (保留TraceId)
            asyncTraceService.sendEmailAsync(user.getEmail(), "登录通知", "您的账号已成功登录");
            
            return LoginResponse.builder()
                .token(token)
                .userInfo(convertToUserInfo(user))
                .build();
        });
    }
    
    private void validateLoginRequest(LoginRequest request) {
        if (StrUtil.isBlank(request.getEmail())) {
            throw new BusinessException("邮箱不能为空");
        }
        if (StrUtil.isBlank(request.getPassword())) {
            throw new BusinessException("密码不能为空");
        }
    }
}
```

### 控制器中的应用

```java
/**
 * 认证控制器 - 链路追踪集成
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {
    
    @Autowired
    private LoginService loginService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(
        @Valid @RequestBody LoginRequest request,
        HttpServletRequest httpRequest) {
        
        // TraceId会自动从拦截器设置，这里直接使用
        String clientIP = TraceContext.getClientIP(httpRequest);
        
        log.info("Login request received - Email: {}, ClientIP: {}", 
            request.getEmail(), clientIP);
        
        try {
            LoginResponse response = loginService.login(request);
            
            log.info("Login successful - Email: {}", request.getEmail());
            
            return ApiResponse.success(response, "登录成功");
            
        } catch (BusinessException e) {
            log.warn("Login failed - Email: {}, Reason: {}", request.getEmail(), e.getMessage());
            throw e;
        }
    }
}
```

## 监控集成

### Prometheus指标配置

```java
/**
 * 自定义监控指标
 */
@Component
public class TraceMetrics {
    
    private final Counter requestCounter;
    private final Timer requestTimer;
    private final Gauge activeRequestGauge;
    
    public TraceMetrics(MeterRegistry meterRegistry) {
        this.requestCounter = Counter.builder("http_requests_total")
            .description("Total HTTP requests")
            .tag("application", "enterprise-auth-system")
            .register(meterRegistry);
            
        this.requestTimer = Timer.builder("http_request_duration_seconds")
            .description("HTTP request duration")
            .register(meterRegistry);
            
        this.activeRequestGauge = Gauge.builder("http_requests_active")
            .description("Active HTTP requests")
            .register(meterRegistry, this, TraceMetrics::getActiveRequestCount);
    }
    
    public void recordRequest(String method, String uri, int status, long duration) {
        requestCounter.increment(
            Tags.of(
                Tag.of("method", method),
                Tag.of("uri", uri),
                Tag.of("status", String.valueOf(status))
            )
        );
        
        requestTimer.record(duration, TimeUnit.MILLISECONDS);
    }
    
    private double getActiveRequestCount() {
        // 实现获取活跃请求数的逻辑
        return 0.0;
    }
}
```

## 部署配置

### Docker配置

```dockerfile
FROM openjdk:8-jre-slim

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
RUN mkdir -p /app/logs
WORKDIR /app

# 复制应用文件
COPY target/enterprise-auth-system.jar app.jar

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app

USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]
```

### Docker Compose配置

```yaml
version: '3.8'

services:
  enterprise-auth:
    build: .
    container_name: enterprise-auth-system
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_USERNAME=auth_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SA_TOKEN_JWT_SECRET=${SA_TOKEN_JWT_SECRET}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - auth-network

  mysql:
    image: mysql:8.0
    container_name: auth-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=enterprise_auth
      - MYSQL_USER=auth_user
      - MYSQL_PASSWORD=${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - auth-network

  redis:
    image: redis:6.2-alpine
    container_name: auth-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - auth-network

  # 可选：Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: auth-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - auth-network

volumes:
  mysql_data:
  redis_data:

networks:
  auth-network:
    driver: bridge
```

## 总结

本方案基于您现有的Spring Boot 2.7技术栈，提供了完整的企业级链路追踪解决方案：

### 🎯 **核心特性**
- **无侵入集成**：基于现有技术栈，最小化代码改动
- **全链路覆盖**：Web请求 → Sa-Token → MyBatis Plus → Redis → 异步任务
- **Hutool增强**：充分利用Hutool工具类简化开发
- **性能友好**：异步日志 + 采样策略，生产环境可用

### 🔧 **技术亮点**
- **MDC自动传递**：所有日志自动包含TraceId和SpanId
- **拦截器链路**：Web、SQL、Redis操作全覆盖
- **异步任务支持**：TaskDecorator确保TraceId传递
- **Sa-Token集成**：登录、注销事件自动记录链路

### 📊 **监控能力**
- **Prometheus指标**：HTTP请求、响应时间、错误率等
- **JSON日志输出**：便于ELK Stack采集分析
- **业务事件追踪**：登录、操作等业务行为记录
- **性能监控**：各组件执行时间统计

这套方案特别适合您的登录系统，能够在不改变现有架构的基础上，提供企业级的可观测性能力。