# 优化版支付订阅系统设计方案

## 1. 系统概述

基于SpringBoot + Vue3的订阅系统，简化支付架构设计，直接配置支付方式，支持多套餐、多支付周期、优惠码、退款和续费功能。

## 2. 优化后的数据库设计

### 2.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status TINYINT DEFAULT 1 COMMENT '1:正常 0:禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.2 套餐表 (packages)
```sql
CREATE TABLE packages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '套餐名称(basic/plus/pro)',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    description TEXT COMMENT '套餐描述',
    features JSON COMMENT '功能特性列表',
    status TINYINT DEFAULT 1 COMMENT '1:启用 0:禁用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.3 套餐价格表 (package_prices)
```sql
CREATE TABLE package_prices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    package_id BIGINT NOT NULL,
    billing_cycle ENUM('DAY', 'MONTH', 'QUARTER', 'YEAR') NOT NULL,
    cycle_count INT DEFAULT 1 COMMENT '周期数量，如3个月则为3',
    original_price DECIMAL(10,2) NOT NULL COMMENT '原价',
    sale_price DECIMAL(10,2) NOT NULL COMMENT '售价',
    currency VARCHAR(3) DEFAULT 'CNY',
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES packages(id),
    UNIQUE KEY uk_package_cycle (package_id, billing_cycle, cycle_count)
);
```

### 2.4 优惠码表 (discount_codes)
```sql
CREATE TABLE discount_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT '优惠码名称',
    type ENUM('FIXED', 'PERCENTAGE') NOT NULL COMMENT '固定金额/百分比',
    value DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    usage_type ENUM('SINGLE', 'MULTIPLE') NOT NULL COMMENT '一码一用/一码多用',
    max_usage INT DEFAULT 1 COMMENT '最大使用次数',
    used_count INT DEFAULT 0 COMMENT '已使用次数',
    min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最小使用金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额(百分比折扣用)',
    applicable_packages JSON COMMENT '适用套餐ID数组，null表示全部',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.5 订单表 (orders)
```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    user_id BIGINT NOT NULL,
    package_id BIGINT NOT NULL,
    package_price_id BIGINT NOT NULL,
    discount_code_id BIGINT NULL COMMENT '使用的优惠码ID',
    original_amount DECIMAL(10,2) NOT NULL COMMENT '原价',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '最终金额',
    currency VARCHAR(3) DEFAULT 'CNY',
    billing_cycle ENUM('DAY', 'MONTH', 'QUARTER', 'YEAR') NOT NULL,
    cycle_count INT NOT NULL,
    status ENUM('PENDING', 'PAID', 'CANCELLED', 'REFUNDED', 'PARTIAL_REFUNDED') DEFAULT 'PENDING',
    order_type ENUM('NEW', 'RENEWAL', 'UPGRADE') DEFAULT 'NEW',
    metadata JSON COMMENT '订单元数据',
    expired_at TIMESTAMP NULL COMMENT '订单过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (package_id) REFERENCES packages(id),
    FOREIGN KEY (package_price_id) REFERENCES package_prices(id),
    FOREIGN KEY (discount_code_id) REFERENCES discount_codes(id)
);
```

### 2.6 🔥 简化的支付方式表 (payment_methods)
```sql
CREATE TABLE payment_methods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    method_code VARCHAR(50) UNIQUE NOT NULL COMMENT '支付方式代码',
    method_name VARCHAR(100) NOT NULL COMMENT '支付方式名称',
    method_type ENUM('GATEWAY', 'ALIPAY', 'WECHAT', 'UNIONPAY', 'BANK') NOT NULL COMMENT '支付类型',
    provider VARCHAR(50) NOT NULL COMMENT '支付提供商(xunhupay, alipay, wechat)',
    icon_url VARCHAR(500) COMMENT '图标URL',
    description VARCHAR(500) COMMENT '描述信息',
    
    -- 🔥 直接包含所有配置信息
    api_config JSON NOT NULL COMMENT '支付API配置',
    webhook_config JSON COMMENT '回调配置',
    extra_config JSON COMMENT '额外配置参数',
    
    -- 支付限制
    min_amount DECIMAL(10,2) DEFAULT 0.01 COMMENT '最小支付金额',
    max_amount DECIMAL(10,2) COMMENT '最大支付金额',
    
    -- 状态和排序
    status TINYINT DEFAULT 1 COMMENT '1:启用 0:禁用',
    priority INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    sort_order INT DEFAULT 0 COMMENT '显示排序',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.7 支付记录表 (payments)
```sql
CREATE TABLE payments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    payment_no VARCHAR(32) UNIQUE NOT NULL COMMENT '支付流水号',
    order_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    payment_method_id BIGINT NOT NULL COMMENT '支付方式ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    currency VARCHAR(3) DEFAULT 'CNY',
    trade_order_id VARCHAR(32) NOT NULL COMMENT '商户订单号(发给支付平台)',
    transaction_id VARCHAR(64) COMMENT '支付平台交易号',
    platform_order_id VARCHAR(64) COMMENT '第三方平台订单号',
    payment_url VARCHAR(1000) COMMENT '支付跳转URL',
    qr_code_url VARCHAR(1000) COMMENT '二维码URL',
    status ENUM('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'EXPIRED') DEFAULT 'PENDING',
    failure_reason VARCHAR(500) COMMENT '失败原因',
    callback_data JSON COMMENT '支付回调原始数据',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent VARCHAR(1000) COMMENT '用户代理',
    paid_at TIMESTAMP NULL,
    expired_at TIMESTAMP NULL COMMENT '支付过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id)
);
```

### 2.8 用户订阅表 (user_subscriptions)
```sql
CREATE TABLE user_subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    package_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL COMMENT '首次订阅的订单ID',
    status ENUM('ACTIVE', 'EXPIRED', 'CANCELLED') DEFAULT 'ACTIVE',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    auto_renewal TINYINT DEFAULT 0 COMMENT '是否自动续费',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (package_id) REFERENCES packages(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    UNIQUE KEY uk_user_subscription (user_id)
);
```

### 2.9 订阅历史表 (subscription_history)
```sql
CREATE TABLE subscription_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    subscription_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    action ENUM('CREATE', 'RENEW', 'UPGRADE', 'DOWNGRADE', 'CANCEL') NOT NULL,
    from_package_id BIGINT COMMENT '原套餐ID(升级降级时)',
    to_package_id BIGINT NOT NULL,
    from_end_time TIMESTAMP COMMENT '原订阅结束时间',
    to_end_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id),
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

### 2.10 退款记录表 (refunds)
```sql
CREATE TABLE refunds (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    refund_no VARCHAR(32) UNIQUE NOT NULL,
    order_id BIGINT NOT NULL,
    payment_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    refund_reason VARCHAR(500),
    refund_type ENUM('FULL', 'PARTIAL') NOT NULL,
    status ENUM('PENDING', 'SUCCESS', 'FAILED') DEFAULT 'PENDING',
    platform_refund_id VARCHAR(64) COMMENT '支付平台退款单号',
    callback_data JSON COMMENT '退款回调数据',
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (payment_id) REFERENCES payments(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 2.11 优惠码使用记录表 (discount_code_usage)
```sql
CREATE TABLE discount_code_usage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    discount_code_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (discount_code_id) REFERENCES discount_codes(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

## 3. 🔥 优化的虎皮椒支付系统架构

### 3.1 HTTP工具类优化
```java
package com.example.payment.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * HTTP工具类 - 专门用于虎皮椒支付
 */
@Slf4j
public class XunhupayHttpUtils {
    
    private static final int TIMEOUT = 30000; // 30秒超时
    
    /**
     * 发送JSON格式的POST请求
     */
    public static String postJson(String url, String jsonData) {
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(getRequestConfig());
            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
            
            StringEntity stringEntity = new StringEntity(jsonData, StandardCharsets.UTF_8);
            httpPost.setEntity(stringEntity);
            
            try (CloseableHttpClient httpClient = HttpClients.createDefault();
                 CloseableHttpResponse response = httpClient.execute(httpPost)) {
                
                HttpEntity entity = response.getEntity();
                String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                
                log.info("虎皮椒支付API调用成功: url={}, response={}", url, result);
                return result;
            }
        } catch (Exception e) {
            log.error("虎皮椒支付API调用失败: url={}, error={}", url, e.getMessage(), e);
            throw new RuntimeException("虎皮椒支付API调用失败", e);
        }
    }
    
    /**
     * 发送表单格式的POST请求
     */
    public static String postForm(String url, Map<String, Object> params) {
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(getRequestConfig());
            
            List<NameValuePair> formParams = new ArrayList<>();
            params.forEach((key, value) -> {
                if (value != null) {
                    formParams.add(new BasicNameValuePair(key, value.toString()));
                }
            });
            
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            
            try (CloseableHttpClient httpClient = HttpClients.createDefault();
                 CloseableHttpResponse response = httpClient.execute(httpPost)) {
                
                HttpEntity responseEntity = response.getEntity();
                String result = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                
                log.info("虎皮椒支付API调用成功: url={}, response={}", url, result);
                return result;
            }
        } catch (Exception e) {
            log.error("虎皮椒支付API调用失败: url={}, params={}, error={}", url, params, e.getMessage(), e);
            throw new RuntimeException("虎皮椒支付API调用失败", e);
        }
    }
    
    private static RequestConfig getRequestConfig() {
        return RequestConfig.custom()
            .setConnectTimeout(TIMEOUT)
            .setSocketTimeout(TIMEOUT)
            .setConnectionRequestTimeout(TIMEOUT)
            .build();
    }
}
```

### 3.2 虎皮椒支付工具类优化
```java
package com.example.payment.util;

import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 虎皮椒支付工具类
 */
@Slf4j
public class XunhupayUtils {
    
    /**
     * 生成虎皮椒支付签名
     * 算法：参数按key的ASCII码排序，拼接成key1=value1&key2=value2格式，最后加上appsecret，MD5加密
     */
    public static String generateSign(Map<String, Object> params, String appSecret) {
        // 过滤掉hash参数和空值
        String paramString = params.entrySet().stream()
            .filter(entry -> !"hash".equals(entry.getKey()))
            .filter(entry -> entry.getValue() != null && !entry.getValue().toString().isEmpty())
            .sorted(Map.Entry.comparingByKey()) // 按key排序
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining("&"));
        
        // 拼接appsecret
        String stringToSign = paramString + appSecret;
        
        // MD5加密，返回小写
        String sign = SecureUtil.md5(stringToSign).toLowerCase();
        
        log.debug("虎皮椒签名计算: paramString={}, stringToSign={}, sign={}", 
            paramString, stringToSign, sign);
        
        return sign;
    }
    
    /**
     * 验证虎皮椒回调签名
     */
    public static boolean verifySign(Map<String, String> params, String appSecret) {
        String receivedHash = params.get("hash");
        if (receivedHash == null || receivedHash.isEmpty()) {
            log.warn("虎皮椒回调缺少hash参数");
            return false;
        }
        
        // 重新计算签名
        Map<String, Object> signParams = new HashMap<>();
        params.forEach((key, value) -> {
            if (!"hash".equals(key) && value != null && !value.isEmpty()) {
                signParams.put(key, value);
            }
        });
        
        String calculatedHash = generateSign(signParams, appSecret);
        boolean isValid = receivedHash.equals(calculatedHash);
        
        if (!isValid) {
            log.warn("虎皮椒回调签名验证失败: received={}, calculated={}", 
                receivedHash, calculatedHash);
        }
        
        return isValid;
    }
    
    /**
     * 获取当前时间戳（秒）
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis() / 1000;
    }
    
    /**
     * 生成随机字符串
     */
    public static String generateNonceStr(int length) {
        String chars = "**********abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 生成随机数字字符串
     */
    public static String generateRandomNumber(int length) {
        String chars = "**********";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
}
```

### 3.3 支付方式处理器接口
```java
// 支付处理器接口
public interface PaymentProcessor {
    String getProvider();
    PaymentResult createPayment(PaymentMethod paymentMethod, PaymentRequest request);
    PaymentQueryResult queryPayment(PaymentMethod paymentMethod, String tradeOrderId);
    RefundResult refund(PaymentMethod paymentMethod, RefundRequest request);
    boolean verifyCallback(PaymentMethod paymentMethod, String callbackData);
    CallbackResult parseCallback(PaymentMethod paymentMethod, String callbackData);
}

// 虎皮椒支付处理器
@Component("xunhupayProcessor")
@Slf4j
public class XunhupayPaymentProcessor implements PaymentProcessor {
    
    @Value("${payment.callback-base-url}")
    private String callbackBaseUrl;
    
    @Override
    public String getProvider() {
        return "xunhupay";
    }
    
    @Override
    public PaymentResult createPayment(PaymentMethod paymentMethod, PaymentRequest request) {
        try {
            // 从支付方式中获取配置
            JSONObject apiConfig = paymentMethod.getApiConfig();
            String appId = apiConfig.getString("app_id");
            String appSecret = apiConfig.getString("app_secret");
            String apiUrl = apiConfig.getString("api_url");
            
            // 构建虎皮椒支付参数
            Map<String, Object> params = buildXunhupayParams(request, appId, appSecret, paymentMethod);
            
            // 调用虎皮椒API
            String response = XunhupayHttpUtils.postJson(apiUrl, JSON.toJSONString(params));
            XunhupayResponse result = JSON.parseObject(response, XunhupayResponse.class);
            
            if (result.getErrcode() == 0 && "success!".equals(result.getErrmsg())) {
                log.info("虎皮椒支付创建成功: tradeOrderId={}, openid={}", 
                    request.getTradeOrderId(), result.getOpenid());
                
                return PaymentResult.success()
                    .paymentUrl(result.getUrl())              // 自适应跳转链接
                    .qrCodeUrl(result.getUrlQrcode())         // PC二维码链接
                    .platformOrderId(result.getOpenid());    // 虎皮椒内部订单ID
            } else {
                log.error("虎皮椒支付创建失败: tradeOrderId={}, errcode={}, errmsg={}", 
                    request.getTradeOrderId(), result.getErrcode(), result.getErrmsg());
                    
                return PaymentResult.failed(result.getErrmsg());
            }
        } catch (Exception e) {
            log.error("虎皮椒支付创建异常: tradeOrderId={}", request.getTradeOrderId(), e);
            return PaymentResult.failed("支付系统异常，请稍后重试");
        }
    }
    
    @Override
    public PaymentQueryResult queryPayment(PaymentMethod paymentMethod, String tradeOrderId) {
        try {
            JSONObject apiConfig = paymentMethod.getApiConfig();
            String appId = apiConfig.getString("app_id");
            String appSecret = apiConfig.getString("app_secret");
            String queryUrl = apiConfig.getString("query_url", "https://api.xunhupay.com/payment/query.html");
            
            Map<String, Object> params = new HashMap<>();
            params.put("appid", appId);
            params.put("trade_order_id", tradeOrderId);
            params.put("time", XunhupayUtils.getCurrentTimestamp());
            params.put("nonce_str", XunhupayUtils.generateNonceStr(16));
            params.put("hash", XunhupayUtils.generateSign(params, appSecret));
            
            String response = XunhupayHttpUtils.postJson(queryUrl, JSON.toJSONString(params));
            // 解析查询结果...
            
            return PaymentQueryResult.success(); // 具体实现
        } catch (Exception e) {
            log.error("虎皮椒支付查询异常: tradeOrderId={}", tradeOrderId, e);
            return PaymentQueryResult.failed("查询失败");
        }
    }
    
    @Override
    public boolean verifyCallback(PaymentMethod paymentMethod, String callbackData) {
        try {
            JSONObject apiConfig = paymentMethod.getApiConfig();
            String appSecret = apiConfig.getString("app_secret");
            
            Map<String, String> params = parseCallbackParams(callbackData);
            return XunhupayUtils.verifySign(params, appSecret);
        } catch (Exception e) {
            log.error("虎皮椒回调验签异常: callbackData={}", callbackData, e);
            return false;
        }
    }
    
    @Override
    public CallbackResult parseCallback(PaymentMethod paymentMethod, String callbackData) {
        try {
            Map<String, String> params = parseCallbackParams(callbackData);
            
            String status = params.get("status");
            if (!"OD".equals(status)) {
                return CallbackResult.failed("支付状态异常: " + status);
            }
            
            return CallbackResult.success()
                .tradeOrderId(params.get("trade_order_id"))
                .transactionId(params.get("transaction_id"))
                .platformOrderId(params.get("open_order_id"))
                .amount(new BigDecimal(params.get("total_fee")))
                .orderTitle(params.get("order_title"))
                .attach(params.get("attach"));
                
        } catch (Exception e) {
            log.error("虎皮椒回调解析异常: callbackData={}", callbackData, e);
            return CallbackResult.failed("回调数据解析失败");
        }
    }
    
    private Map<String, Object> buildXunhupayParams(PaymentRequest request, String appId, 
            String appSecret, PaymentMethod paymentMethod) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("version", "1.1");
        params.put("appid", appId);
        params.put("trade_order_id", request.getTradeOrderId());
        params.put("total_fee", request.getAmount());
        params.put("title", request.getTitle());
        params.put("time", XunhupayUtils.getCurrentTimestamp());
        params.put("notify_url", callbackBaseUrl + "/payment/notify/xunhupay");
        params.put("return_url", request.getReturnUrl());
        params.put("callback_url", request.getCancelUrl());
        params.put("plugins", "SpringBoot-Payment-System-v1.0");
        
        // 备注信息，可以传递一些业务参数
        if (request.getAttach() != null) {
            params.put("attach", request.getAttach());
        }
        
        // 随机字符串
        params.put("nonce_str", XunhupayUtils.generateNonceStr(16));
        
        // 根据支付方式添加特殊参数
        JSONObject extraConfig = paymentMethod.getExtraConfig();
        if (extraConfig != null) {
            // H5支付配置
            if (extraConfig.containsKey("type")) {
                params.put("type", extraConfig.getString("type")); // WAP、JSAPI等
            }
            if (extraConfig.containsKey("wap_url")) {
                params.put("wap_url", extraConfig.getString("wap_url"));
            }
            if (extraConfig.containsKey("wap_name")) {
                params.put("wap_name", extraConfig.getString("wap_name"));
            }
        }
        
        // 生成签名
        params.put("hash", XunhupayUtils.generateSign(params, appSecret));
        
        return params;
    }
    
    private Map<String, String> parseCallbackParams(String callbackData) {
        // 解析回调参数，支持form表单和JSON格式
        try {
            // 尝试解析为JSON
            JSONObject json = JSON.parseObject(callbackData);
            Map<String, String> params = new HashMap<>();
            json.forEach((key, value) -> params.put(key, value != null ? value.toString() : ""));
            return params;
        } catch (Exception e) {
            // 如果不是JSON，尝试解析为form参数
            return parseFormParams(callbackData);
        }
    }
    
    private Map<String, String> parseFormParams(String formData) {
        Map<String, String> params = new HashMap<>();
        if (formData != null && !formData.isEmpty()) {
            String[] pairs = formData.split("&");
            for (String pair : pairs) {
                String[] kv = pair.split("=", 2);
                if (kv.length == 2) {
                    params.put(kv[0], kv[1]);
                }
            }
        }
        return params;
    }
}


```

### 3.4 虎皮椒响应模型
```java
/**
 * 虎皮椒支付响应
 */
@Data
public class XunhupayResponse {
    private Integer errcode;          // 错误码，0表示成功
    private String errmsg;            // 错误信息，success!表示成功
    private String openid;            // 虎皮椒内部订单ID（注意：这里字段名是openid但实际是orderid）
    private String url;               // 支付跳转链接（自适应PC/移动端）
    private String urlQrcode;         // 二维码链接（PC端使用）
    private String hash;              // 签名
}

/**
 * 虎皮椒回调数据
 */
@Data
public class XunhupayCallback {
    private String tradeOrderId;      // 商户订单号
    private String totalFee;          // 支付金额
    private String transactionId;     // 交易号
    private String openOrderId;       // 虎皮椒内部订单号
    private String orderTitle;        // 订单标题
    private String status;            // 订单状态：OD已支付，CD已退款，RD退款中，UD退款失败
    private String plugins;           // 插件ID
    private String attach;            // 备注
    private String appid;             // 支付渠道ID
    private String time;              // 时间戳
    private String nonceStr;          // 随机字符串
    private String hash;              // 签名
}
```

### 3.5 统一回调处理优化
```java
@RestController
@RequestMapping("/payment/notify")
@Slf4j
public class PaymentNotifyController {
    
    private final PaymentService paymentService;
    private final PaymentProcessorFactory processorFactory;
    
    /**
     * 虎皮椒支付回调处理
     */
    @PostMapping("/xunhupay")
    public String handleXunhupayNotify(HttpServletRequest request) {
        
        try {
            // 1. 获取回调数据
            String callbackData = getCallbackData(request);
            log.info("收到虎皮椒支付回调: {}", callbackData);
            
            // 2. 解析回调参数
            Map<String, String> params = parseCallbackParams(callbackData);
            String tradeOrderId = params.get("trade_order_id");
            
            if (tradeOrderId == null || tradeOrderId.isEmpty()) {
                log.error("虎皮椒回调缺少商户订单号");
                return "fail";
            }
            
            // 3. 查找支付记录
            Payment payment = paymentService.findByTradeOrderId(tradeOrderId);
            if (payment == null) {
                log.error("未找到支付记录: tradeOrderId={}", tradeOrderId);
                return "fail";
            }
            
            // 4. 检查支付状态，避免重复处理
            if (payment.getStatus() == PaymentStatus.SUCCESS) {
                log.info("支付已成功，跳过重复处理: paymentNo={}", payment.getPaymentNo());
                return "success";
            }
            
            // 5. 获取支付方式和处理器
            PaymentMethod paymentMethod = payment.getPaymentMethod();
            PaymentProcessor processor = processorFactory.getProcessor("xunhupay");
            
            // 6. 验证回调签名
            if (!processor.verifyCallback(paymentMethod, callbackData)) {
                log.error("虎皮椒回调签名验证失败: tradeOrderId={}", tradeOrderId);
                return "fail";
            }
            
            // 7. 解析回调结果
            CallbackResult result = processor.parseCallback(paymentMethod, callbackData);
            
            if (result.isSuccess()) {
                // 8. 处理支付成功
                handlePaymentSuccess(payment, result, callbackData);
                return "success";
            } else {
                log.warn("虎皮椒回调返回失败状态: tradeOrderId={}, reason={}", 
                    tradeOrderId, result.getFailureReason());
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("处理虎皮椒支付回调异常", e);
            return "fail";
        }
    }
    
    /**
     * 通用支付回调处理（支持多种支付方式）
     */
    @PostMapping("/{provider}")
    public String handlePaymentNotify(@PathVariable String provider,
                                     HttpServletRequest request) {
        
        try {
            log.info("收到{}支付回调", provider);
            
            // 获取回调数据
            String callbackData = getCallbackData(request);
            
            // 获取支付处理器
            PaymentProcessor processor = processorFactory.getProcessor(provider);
            
            // 从回调数据中提取商户订单号
            String tradeOrderId = extractTradeOrderId(callbackData, provider);
            if (tradeOrderId == null) {
                log.error("无法从{}回调数据中提取商户订单号", provider);
                return "fail";
            }
            
            // 查找支付记录
            Payment payment = paymentService.findByTradeOrderId(tradeOrderId);
            if (payment == null) {
                log.error("未找到支付记录: provider={}, tradeOrderId={}", provider, tradeOrderId);
                return "fail";
            }
            
            // 验证回调
            PaymentMethod paymentMethod = payment.getPaymentMethod();
            if (!processor.verifyCallback(paymentMethod, callbackData)) {
                log.error("{}回调签名验证失败: tradeOrderId={}", provider, tradeOrderId);
                return "fail";
            }
            
            // 处理回调结果
            CallbackResult result = processor.parseCallback(paymentMethod, callbackData);
            if (result.isSuccess()) {
                handlePaymentSuccess(payment, result, callbackData);
                return "success";
            } else {
                log.warn("{}回调返回失败状态: tradeOrderId={}", provider, tradeOrderId);
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("处理{}支付回调异常", provider, e);
            return "fail";
        }
    }
    
    @Transactional
    private void handlePaymentSuccess(Payment payment, CallbackResult result, String callbackData) {
        // 更新支付记录
        payment.setStatus(PaymentStatus.SUCCESS);
        payment.setTransactionId(result.getTransactionId());
        payment.setPlatformOrderId(result.getPlatformOrderId());
        payment.setCallbackData(callbackData);
        payment.setPaidAt(new Date());
        paymentService.updatePayment(payment);
        
        // 更新订单状态
        Order order = payment.getOrder();
        order.setStatus(OrderStatus.PAID);
        paymentService.updateOrder(order);
        
        // 处理订阅
        paymentService.processSubscription(order);
        
        // 记录优惠码使用
        if (order.getDiscountCodeId() != null) {
            paymentService.recordDiscountCodeUsage(order);
        }
        
        // 发送支付成功通知
        paymentService.sendPaymentSuccessNotification(order);
        
        log.info("支付处理完成: paymentNo={}, orderId={}, amount={}", 
            payment.getPaymentNo(), order.getId(), payment.getAmount());
    }
    
    private String getCallbackData(HttpServletRequest request) throws Exception {
        // 先尝试获取JSON格式的请求体
        String contentType = request.getContentType();
        if (contentType != null && contentType.contains("application/json")) {
            StringBuilder sb = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
            }
            return sb.toString();
        } else {
            // 表单格式，构建查询字符串
            StringBuilder sb = new StringBuilder();
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String paramValue = request.getParameter(paramName);
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(paramName).append("=").append(paramValue);
            }
            return sb.toString();
        }
    }
    
    private Map<String, String> parseCallbackParams(String callbackData) {
        Map<String, String> params = new HashMap<>();
        try {
            // 尝试解析为JSON
            JSONObject json = JSON.parseObject(callbackData);
            json.forEach((key, value) -> params.put(key, value != null ? value.toString() : ""));
        } catch (Exception e) {
            // 解析为表单参数
            if (callbackData != null && !callbackData.isEmpty()) {
                String[] pairs = callbackData.split("&");
                for (String pair : pairs) {
                    String[] kv = pair.split("=", 2);
                    if (kv.length == 2) {
                        params.put(kv[0], kv[1]);
                    }
                }
            }
        }
        return params;
    }
    
    private String extractTradeOrderId(String callbackData, String provider) {
        Map<String, String> params = parseCallbackParams(callbackData);
        return params.get("trade_order_id");
    }
}

### 3.3 支付方式服务
```java
@Service
public class PaymentMethodService {
    
    private final PaymentMethodRepository paymentMethodRepository;
    private final PaymentProcessorFactory processorFactory;
    
    // 获取可用支付方式
    public List<PaymentMethodVO> getAvailablePaymentMethods(BigDecimal amount) {
        // 1. 获取所有启用的支付方式
        List<PaymentMethod> methods = paymentMethodRepository.findByStatusAndOrderByPriorityDescSortOrderDesc(1);
        
        // 2. 根据金额筛选
        methods = methods.stream()
            .filter(method -> amount.compareTo(method.getMinAmount()) >= 0)
            .filter(method -> method.getMaxAmount() == null || 
                amount.compareTo(method.getMaxAmount()) <= 0)
            .collect(Collectors.toList());
        
        // 3. 检查支付处理器可用性
        methods = methods.stream()
            .filter(this::isProcessorAvailable)
            .collect(Collectors.toList());
        
        return methods.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }
    
    // 选择支付方式
    public PaymentMethod selectPaymentMethod(String methodCode, BigDecimal amount) {
        PaymentMethod method = paymentMethodRepository.findByMethodCodeAndStatus(methodCode, 1);
        
        if (method == null) {
            throw new BusinessException("支付方式不存在或已禁用");
        }
        
        // 检查金额限制
        if (amount.compareTo(method.getMinAmount()) < 0) {
            throw new BusinessException("支付金额低于最小限制");
        }
        
        if (method.getMaxAmount() != null && 
            amount.compareTo(method.getMaxAmount()) > 0) {
            throw new BusinessException("支付金额超过最大限制");
        }
        
        // 检查处理器可用性
        if (!isProcessorAvailable(method)) {
            throw new BusinessException("支付方式暂不可用");
        }
        
        return method;
    }
    
    private boolean isProcessorAvailable(PaymentMethod method) {
        try {
            processorFactory.getProcessor(method);
            return true;
        } catch (Exception e) {
            log.warn("支付方式{}的处理器不可用: {}", method.getMethodCode(), e.getMessage());
            return false;
        }
    }
    
    private PaymentMethodVO convertToVO(PaymentMethod method) {
        return PaymentMethodVO.builder()
            .methodCode(method.getMethodCode())
            .methodName(method.getMethodName())
            .methodType(method.getMethodType())
            .provider(method.getProvider())
            .iconUrl(method.getIconUrl())
            .description(method.getDescription())
            .minAmount(method.getMinAmount())
            .maxAmount(method.getMaxAmount())
            .build();
    }
}
```

## 4. 🔥 简化的支付创建流程

### 4.1 支付创建服务
```java
@Service
@Transactional
public class PaymentService {
    
    private final PaymentMethodService paymentMethodService;
    private final PaymentProcessorFactory processorFactory;
    
    public PaymentCreateResult createPayment(PaymentCreateRequest request) {
        // 1. 获取订单信息
        Order order = findOrderById(request.getOrderId());
        if (order.getStatus() != OrderStatus.PENDING) {
            throw new BusinessException("订单状态不允许支付");
        }
        
        // 2. 选择支付方式
        PaymentMethod paymentMethod = paymentMethodService
            .selectPaymentMethod(request.getMethodCode(), order.getFinalAmount());
        
        // 3. 创建支付记录
        Payment payment = new Payment();
        payment.setPaymentNo(generatePaymentNo());
        payment.setOrderId(order.getId());
        payment.setUserId(order.getUserId());
        payment.setPaymentMethodId(paymentMethod.getId());
        payment.setAmount(order.getFinalAmount());
        payment.setTradeOrderId(generateTradeOrderId());
        payment.setStatus(PaymentStatus.PENDING);
        payment.setClientIp(request.getClientIp());
        payment.setUserAgent(request.getUserAgent());
        payment.setExpiredAt(calculateExpiredTime());
        
        // 4. 调用支付处理器
        PaymentProcessor processor = processorFactory.getProcessor(paymentMethod);
        
        PaymentRequest paymentRequest = PaymentRequest.builder()
            .tradeOrderId(payment.getTradeOrderId())
            .amount(payment.getAmount())
            .title(buildOrderTitle(order))
            .returnUrl(request.getReturnUrl())
            .cancelUrl(request.getCancelUrl())
            .clientIp(payment.getClientIp())
            .build();
        
        PaymentResult platformResult = processor.createPayment(paymentMethod, paymentRequest);
        
        if (platformResult.isSuccess()) {
            payment.setPaymentUrl(platformResult.getPaymentUrl());
            payment.setQrCodeUrl(platformResult.getQrCodeUrl());
            payment.setPlatformOrderId(platformResult.getPlatformOrderId());
        } else {
            payment.setStatus(PaymentStatus.FAILED);
            payment.setFailureReason(platformResult.getErrorMessage());
        }
        
        savePayment(payment);
        
        return PaymentCreateResult.builder()
            .paymentNo(payment.getPaymentNo())
            .paymentUrl(payment.getPaymentUrl())
            .qrCodeUrl(payment.getQrCodeUrl())
            .amount(payment.getAmount())
            .expiredAt(payment.getExpiredAt())
            .methodInfo(buildMethodInfo(paymentMethod))
            .build();
    }
}
```

### 4.2 统一回调处理
```java
@RestController
@RequestMapping("/payment/notify")
public class PaymentNotifyController {
    
    private final PaymentService paymentService;
    private final PaymentProcessorFactory processorFactory;
    
    // 通用回调处理接口
    @PostMapping("/{provider}")
    public String handlePaymentNotify(@PathVariable String provider,
                                     @RequestBody String callbackData,
                                     HttpServletRequest request) {
        
        try {
            // 1. 获取支付处理器
            PaymentProcessor processor = processorFactory.getProcessor(provider);
            
            // 2. 解析回调参数获取商户订单号
            String tradeOrderId = extractTradeOrderId(callbackData, provider);
            if (tradeOrderId == null) {
                log.error("无法从回调数据中提取商户订单号: provider={}, data={}", provider, callbackData);
                return "fail";
            }
            
            // 3. 查找支付记录
            Payment payment = findByTradeOrderId(tradeOrderId);
            if (payment == null) {
                log.error("未找到支付记录: tradeOrderId={}", tradeOrderId);
                return "fail";
            }
            
            // 4. 获取支付方式配置
            PaymentMethod paymentMethod = payment.getPaymentMethod();
            
            // 5. 验证回调签名
            if (!processor.verifyCallback(paymentMethod, callbackData)) {
                log.error("回调签名验证失败: provider={}, tradeOrderId={}", provider, tradeOrderId);
                return "fail";
            }
            
            // 6. 检查支付状态，避免重复处理
            if (payment.getStatus() == PaymentStatus.SUCCESS) {
                log.info("支付已成功，跳过重复处理: paymentNo={}", payment.getPaymentNo());
                return "success";
            }
            
            // 7. 解析回调结果
            CallbackResult result = processor.parseCallback(paymentMethod, callbackData);
            
            if (result.isSuccess()) {
                // 8. 更新支付记录
                payment.setStatus(PaymentStatus.SUCCESS);
                payment.setTransactionId(result.getTransactionId());
                payment.setPlatformOrderId(result.getPlatformOrderId());
                payment.setCallbackData(callbackData);
                payment.setPaidAt(new Date());
                
                // 9. 更新订单状态
                Order order = payment.getOrder();
                order.setStatus(OrderStatus.PAID);
                
                // 10. 处理订阅
                processSubscription(order);
                
                // 11. 记录优惠码使用
                if (order.getDiscountCodeId() != null) {
                    recordDiscountCodeUsage(order);
                }
                
                // 12. 发送通知
                sendPaymentSuccessNotification(order);
                
                return "success";
            } else {
                log.warn("支付回调返回失败状态: tradeOrderId={}, reason={}", 
                    tradeOrderId, result.getFailureReason());
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("处理{}支付回调异常: {}", provider, e.getMessage(), e);
            return "fail";
        }
    }
    
    private String extractTradeOrderId(String callbackData, String provider) {
        // 根据不同的支付提供商解析商户订单号
        switch (provider) {
            case "xunhupay":
                return extractXunhupayTradeOrderId(callbackData);
            case "alipay":
                return extractAlipayTradeOrderId(callbackData);
            case "wechat":
                return extractWechatTradeOrderId(callbackData);
            default:
                return null;
        }
    }
}
```

## 5. 🔥 支付方式配置管理

### 5.1 管理接口
```java
@RestController
@RequestMapping("/admin/payment-methods")
public class PaymentMethodManageController {
    
    private final PaymentMethodService paymentMethodService;
    
    // 获取所有支付方式
    @GetMapping
    public Result<List<PaymentMethodConfigVO>> getAllPaymentMethods() {
        List<PaymentMethod> methods = paymentMethodService.findAll();
        return Result.success(methods.stream()
            .map(this::convertToConfigVO)
            .collect(Collectors.toList()));
    }
    
    // 创建支付方式
    @PostMapping
    public Result<Void> createPaymentMethod(@RequestBody @Valid PaymentMethodCreateRequest request) {
        PaymentMethod method = new PaymentMethod();
        method.setMethodCode(request.getMethodCode());
        method.setMethodName(request.getMethodName());
        method.setMethodType(request.getMethodType());
        method.setProvider(request.getProvider());
        method.setIconUrl(request.getIconUrl());
        method.setDescription(request.getDescription());
        method.setApiConfig(request.getApiConfig());
        method.setWebhookConfig(request.getWebhookConfig());
        method.setExtraConfig(request.getExtraConfig());
        method.setMinAmount(request.getMinAmount());
        method.setMaxAmount(request.getMaxAmount());
        method.setStatus(request.getStatus());
        method.setPriority(request.getPriority());
        method.setSortOrder(request.getSortOrder());
        
        paymentMethodService.save(method);
        return Result.success();
    }
    
    // 更新支付方式
    @PutMapping("/{id}")
    public Result<Void> updatePaymentMethod(@PathVariable Long id, 
                                           @RequestBody @Valid PaymentMethodUpdateRequest request) {
        PaymentMethod method = paymentMethodService.findById(id);
        if (method == null) {
            return Result.error("支付方式不存在");
        }
        
        // 更新字段
        method.setMethodName(request.getMethodName());
        method.setIconUrl(request.getIconUrl());
        method.setDescription(request.getDescription());
        method.setApiConfig(request.getApiConfig());
        method.setWebhookConfig(request.getWebhookConfig());
        method.setExtraConfig(request.getExtraConfig());
        method.setMinAmount(request.getMinAmount());
        method.setMaxAmount(request.getMaxAmount());
        method.setStatus(request.getStatus());
        method.setPriority(request.getPriority());
        method.setSortOrder(request.getSortOrder());
        
        paymentMethodService.save(method);
        return Result.success();
    }
    
    // 测试支付方式
    @PostMapping("/{id}/test")
    public Result<PaymentTestResult> testPaymentMethod(@PathVariable Long id) {
        PaymentMethod method = paymentMethodService.findById(id);
        if (method == null) {
            return Result.error("支付方式不存在");
        }
        
        try {
            PaymentProcessor processor = processorFactory.getProcessor(method);
            
            // 创建测试支付请求
            PaymentRequest testRequest = createTestPaymentRequest();
            PaymentResult result = processor.createPayment(method, testRequest);
            
            return Result.success(new PaymentTestResult(
                result.isSuccess(), 
                result.isSuccess() ? "测试成功" : result.getErrorMessage(), 
                result
            ));
        } catch (Exception e) {
            return Result.success(new PaymentTestResult(false, e.getMessage(), null));
        }
    }
    
    // 启用/禁用支付方式
    @PatchMapping("/{id}/status")
    public Result<Void> updatePaymentMethodStatus(@PathVariable Long id, 
                                                  @RequestParam Integer status) {
        paymentMethodService.updateStatus(id, status);
        return Result.success();
    }
}
```

## 6. 🔥 初始化数据示例

### 6.1 支付方式配置示例
```sql
-- 虎皮椒支付（推荐）
INSERT INTO payment_methods (
    method_code, method_name, method_type, provider, 
    icon_url, description, api_config, webhook_config, 
    min_amount, max_amount, status, priority, sort_order
) VALUES (
    'xunhupay_auto', 
    '支付宝/微信支付', 
    'GATEWAY', 
    'xunhupay',
    'https://example.com/icons/xunhupay.png',
    '支持支付宝和微信支付，自动适配PC和手机端',
    '{
        "app_id": "your_xunhupay_app_id",
        "app_secret": "your_xunhupay_app_secret",
        "api_url": "https://api.xunhupay.com"
    }',
    '{
        "notify_url": "/payment/notify/xunhupay",
        "verify_sign": true
    }',
    0.01, 50000.00, 1, 100, 1
);


```

### 6.2 套餐和价格数据
```sql
-- 基础套餐
INSERT INTO packages (name, display_name, description, features, status, sort_order) VALUES 
('basic', '基础版', '适合个人用户', '["基础功能", "邮件支持", "5GB存储"]', 1, 1),
('plus', '进阶版', '适合小团队', '["进阶功能", "优先支持", "50GB存储", "API访问"]', 1, 2),
('pro', '专业版', '适合企业用户', '["全部功能", "24/7支持", "500GB存储", "高级API", "自定义集成"]', 1, 3);

-- 基础版价格
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price) VALUES 
(1, 'MONTH', 1, 99.00, 89.00),
(1, 'QUARTER', 3, 297.00, 249.00),
(1, 'YEAR', 1, 1188.00, 899.00);

-- 进阶版价格
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price) VALUES 
(2, 'MONTH', 1, 199.00, 179.00),
(2, 'QUARTER', 3, 597.00, 499.00),
(2, 'YEAR', 1, 2388.00, 1799.00);

-- 专业版价格
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price) VALUES 
(3, 'MONTH', 1, 399.00, 359.00),
(3, 'QUARTER', 3, 1197.00, 999.00),
(3, 'YEAR', 1, 4788.00, 3599.00);
```

## 7. 🔥 配置文件示例

### 7.1 应用配置
```yaml
# application.yml
spring:
  profiles:
    active: dev
  datasource:
    url: **************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver

# 支付系统配置
payment:
  # 默认配置
  default-expire-minutes: 30
  max-retry-count: 3
  callback-base-url: ${CALLBACK_BASE_URL:http://localhost:8080}
  
  # 虎皮椒支付默认配置（可被数据库中的配置覆盖）
  xunhupay:
    default-config:
      api-url: https://api.xunhupay.com
      timeout: 30000
      retry-count: 3


# 订阅配置
subscription:
  expire-notice-days: [7, 3, 1]
  grace-period-days: 3

# 优惠码配置
discount:
  max-usage-per-user: 5
  code-length: 8
  code-prefix: "SAVE"

logging:
  level:
    com.example.payment: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 8. 🔥 API接口优化

### 8.1 前端支付接口
```java
// 获取支付方式列表（用户端）
GET /api/payment-methods?amount=100
Response: {
    "code": 200,
    "data": [
        {
            "methodCode": "xunhupay_auto",
            "methodName": "支付宝/微信支付",
            "methodType": "GATEWAY",
            "provider": "xunhupay",
            "iconUrl": "https://example.com/icons/xunhupay.png",
            "description": "支持支付宝和微信支付，自动适配PC和手机端",
            "minAmount": 0.01,
            "maxAmount": 50000.00,
            "recommended": true  // 推荐标识
        }
    ]
}

// 创建支付
POST /api/payments
Request: {
    "orderId": 12345,
    "methodCode": "xunhupay_auto",
    "clientIp": "***********",
    "userAgent": "Mozilla/5.0...",
    "returnUrl": "https://example.com/success",
    "cancelUrl": "https://example.com/cancel"
}
Response: {
    "paymentNo": "PAY20240616001",
    "paymentUrl": "https://api.xunhupay.com/pay/...",
    "qrCodeUrl": "https://api.xunhupay.com/qrcode/...",
    "amount": 100.00,
    "expiredAt": "2024-06-16T11:30:00Z",
    "methodInfo": {
        "methodCode": "xunhupay_auto",
        "methodName": "支付宝/微信支付",
        "provider": "xunhupay"
    },
    "instructions": {
        "pc": "请扫描二维码完成支付",
        "mobile": "点击立即支付跳转到支付页面"
    }
}
```

### 8.2 管理后台接口
```java
// 支付方式管理（管理员）
GET /admin/payment-methods
Response: {
    "code": 200,
    "data": [
        {
            "id": 1,
            "methodCode": "xunhupay_auto",
            "methodName": "支付宝/微信支付",
            "methodType": "GATEWAY",
            "provider": "xunhupay",
            "status": 1,
            "priority": 100,
            "config": {
                "apiConfig": {
                    "app_id": "***",
                    "app_secret": "***",
                    "api_url": "https://api.xunhupay.com"
                },
                "minAmount": 0.01,
                "maxAmount": 50000.00
            },
            "stats": {
                "todayPayments": 125,
                "todayAmount": 12580.50,
                "successRate": 98.5
            }
        }
    ]
}

// 支付统计
GET /admin/payment-stats
Response: {
    "overview": {
        "todayPayments": 456,
        "todayAmount": 45680.50,
        "monthlyPayments": 12345,
        "monthlyAmount": 1234567.89
    },
    "byMethod": [
        {
            "methodCode": "xunhupay_auto",
            "methodName": "支付宝/微信支付",
            "count": 380,
            "amount": 38567.20,
            "successRate": 98.5
        }
    ]
}
```

## 11. 🔥 虎皮椒支付优化总结

### 11.1 **虎皮椒支付特色功能**

#### ✅ **统一API接口**
- 支付宝和微信使用同一个API endpoint
- 只需配置不同的`appid`和`appsecret`
- 大大简化了接入复杂度

#### ✅ **智能设备适配**
```java
// 虎皮椒返回两种链接
{
  "url": "https://api.xunhupay.com/pay/...",        // 自适应链接
  "url_qrcode": "https://api.xunhupay.com/qr/..."  // 二维码链接
}

// 前端处理逻辑
if (isMobile()) {
    window.location.href = result.paymentUrl;  // 移动端直接跳转
} else {
    showQrCode(result.qrCodeUrl);              // PC端显示二维码
}
```

#### ✅ **用户友好的支付选择**
- 用户在虎皮椒页面可以选择支付宝或微信
- 不需要在商户页面预先选择支付方式
- 提升用户体验，降低流失率

#### ✅ **个人开发者友好**
- 无需企业资质即可接入
- 支持个人银行卡收款
- 资金T+1自动到账

### 11.2 **系统架构优化亮点**

#### 🚀 **简化配置管理**
```sql
-- 推荐配置：统一接入，用户选择支付方式
INSERT INTO payment_methods VALUES (
    'xunhupay_auto', '支付宝/微信支付', 'GATEWAY', 'xunhupay',
    '{"app_id":"xxx","app_secret":"xxx","api_url":"https://api.xunhupay.com/payment/do.html"}'
);

-- 或者分别配置：明确区分支付方式
INSERT INTO payment_methods VALUES 
('xunhupay_alipay', '支付宝', 'ALIPAY', 'xunhupay', '{"app_id":"alipay_xxx"}'),
('xunhupay_wechat', '微信支付', 'WECHAT', 'xunhupay', '{"app_id":"wechat_xxx"}');
```

#### 🚀 **统一回调处理**
```java
// 一个回调地址处理所有虎皮椒支付
@PostMapping("/payment/notify/xunhupay")
public String handleXunhupayNotify(HttpServletRequest request) {
    // 自动识别支付宝/微信回调
    // 统一验签和状态处理
    return "success";
}
```

#### 🚀 **优化的HTTP工具类**
```java
// 专门针对虎皮椒优化的HTTP工具
XunhupayHttpUtils.postJson(apiUrl, jsonData);  // 支持JSON格式
XunhupayHttpUtils.postForm(apiUrl, formData);  // 支持表单格式
```

#### 🚀 **完善的签名算法**
```java
// 严格按照虎皮椒文档实现
String sign = XunhupayUtils.generateSign(params, appSecret);
boolean valid = XunhupayUtils.verifySign(callbackParams, appSecret);
```

### 11.3 **前端用户体验优化**

#### 💡 **智能支付流程**
1. **PC端**: 自动显示二维码，实时监听支付状态
2. **移动端**: 直接跳转到支付页面，无缝体验
3. **支付状态**: 实时轮询，及时反馈支付结果

#### 💡 **友好的界面设计**
- 推荐支付方式标识
- 支持的支付图标展示
- 加载状态和错误提示
- 支付超时重试机制

### 11.4 **部署和监控建议**

#### 📊 **关键监控指标**
```yaml
monitoring:
  payment:
    success_rate: "> 98%"           # 支付成功率
    response_time: "< 3s"           # API响应时间
    callback_success_rate: "> 99%"  # 回调成功率
  
  xunhupay:
    api_availability: "> 99.5%"     # API可用性
    signature_error_rate: "< 0.1%"  # 签名错误率
```

#### 📊 **日志规范**
```java
// 关键业务日志
log.info("虎皮椒支付创建: tradeOrderId={}, amount={}, method={}", 
    tradeOrderId, amount, methodCode);
log.info("虎皮椒支付成功: tradeOrderId={}, transactionId={}", 
    tradeOrderId, transactionId);
log.warn("虎皮椒回调验签失败: tradeOrderId={}, received={}, calculated={}", 
    tradeOrderId, receivedSign, calculatedSign);
```

### 11.5 **最佳实践建议**

#### 🎯 **配置策略**
1. **小型项目**: 使用`xunhupay_auto`统一配置
2. **大型项目**: 分别配置支付宝和微信，便于数据分析
3. **混合策略**: 主推统一配置，备用分别配置

#### 🎯 **错误处理**
```java
// 完善的异常处理
try {
    PaymentResult result = processor.createPayment(method, request);
    return result;
} catch (XunhupayApiException e) {
    log.error("虎皮椒API异常", e);
    return PaymentResult.failed("支付系统暂时不可用，请稍后重试");
} catch (SignatureException e) {
    log.error("签名异常", e);
    return PaymentResult.failed("系统配置错误，请联系客服");
}
```

#### 🎯 **性能优化**
- HTTP连接池复用
- 支付状态缓存
- 异步回调处理
- 数据库索引优化

## 12. 🎉 结语

这个优化版的支付订阅系统结合了虎皮椒支付的特点，实现了：

✅ **架构简洁** - 去除冗余设计，直接配置支付方式  
✅ **接入简单** - 针对虎皮椒优化的工具类和处理器  
✅ **用户友好** - 智能设备适配和流畅的支付体验  
✅ **扩展灵活** - 保持良好的接口抽象，便于后续扩展  
✅ **维护容易** - 清晰的代码结构和完善的日志监控  

特别适合个人开发者和中小型企业快速搭建支付系统！🚀