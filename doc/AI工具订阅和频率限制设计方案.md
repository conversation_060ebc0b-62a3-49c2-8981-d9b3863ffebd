# AI工具订阅和频率限制设计方案（简化版）

## 1. 概述

采用简化设计，直接在Package表中使用JSON字段配置各AI产品的使用限制，避免复杂的关联表设计。

## 2. 数据库设计

### 2.1 修改现有表结构

#### 2.1.1 套餐表 (packages) - 添加限制配置
```sql
ALTER TABLE `packages` 
ADD COLUMN `limits` json DEFAULT NULL COMMENT 'AI产品使用限制配置';

-- 示例JSON结构
-- {
--   "chatgpt": {"daily": 3, "hourly": null},
--   "image_gen": {"daily": 1, "hourly": null}, 
--   "voice_text": {"daily": 5, "hourly": null}
-- }
```

#### 2.1.2 用户产品使用记录表 (user_product_usage)
```sql
CREATE TABLE `user_product_usage` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `product_code` varchar(50) NOT NULL COMMENT '产品代码：chatgpt/image_gen/voice_text',
  `subscription_id` bigint DEFAULT NULL COMMENT '关联的订阅ID，可为空（免费用户）',
  `usage_count` int DEFAULT '1' COMMENT '使用次数',
  `request_data` json DEFAULT NULL COMMENT '请求数据（可选）',
  `response_data` json DEFAULT NULL COMMENT '响应数据（可选）',
  `cost_tokens` int DEFAULT NULL COMMENT '消耗的token数',
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_product` (`user_id`,`product_code`),
  KEY `idx_user_time` (`user_id`,`created_at`),
  KEY `idx_subscription_id` (`subscription_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `user_product_usage_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户产品使用记录表';
```

### 2.2 表的作用说明

#### 2.2.1 用户订阅表 (user_subscriptions) 的作用
- **存储用户当前的订阅状态**
- 一个用户同时只能有一个有效订阅
- 记录订阅的开始时间、结束时间、状态等
- 用于快速查询用户当前享有的服务等级

#### 2.2.2 订阅历史表 (subscription_history) 的作用  
- **记录用户所有的订阅变更历史**
- 包括新建、续费、升级、降级、取消等操作
- 用于数据分析、客服查询、财务对账
- 保留完整的订阅轨迹

## 3. 套餐限制配置示例

### 3.1 JSON配置格式
```json
{
  "chatgpt": {
    "daily": 3,
    "hourly": null,
    "custom_hours": null,
    "custom_count": null
  },
  "image_gen": {
    "daily": 1,
    "hourly": null
  },
  "voice_text": {
    "daily": 5,
    "hourly": null
  }
}
```

### 3.2 不同套餐的配置示例
```sql
-- 免费套餐
UPDATE packages SET limits = '{
  "chatgpt": {"daily": 3},
  "image_gen": {"daily": 1}, 
  "voice_text": {"daily": 5}
}' WHERE name = 'free';

-- Plus套餐 (50次/3小时)
UPDATE packages SET limits = '{
  "chatgpt": {"custom_hours": 3, "custom_count": 50},
  "image_gen": {"daily": 10},
  "voice_text": {"daily": 50}
}' WHERE name = 'plus';

-- Pro套餐 (100次/3小时)
UPDATE packages SET limits = '{
  "chatgpt": {"custom_hours": 3, "custom_count": 100},
  "image_gen": {"daily": 50},
  "voice_text": {"daily": 200}
}' WHERE name = 'pro';
```

## 4. 支付回调后的订阅逻辑

### 4.1 支付回调处理流程
```java
@Override
@Transactional
public void processSubscription(Order order) {
    Long userId = order.getUserId();
    
    // 1. 查询用户当前是否有有效订阅
    UserSubscription currentSubscription = getCurrentSubscription(userId);
    
    if (currentSubscription == null) {
        // 情况1：用户没有订阅 - 创建新订阅
        createNewSubscription(order);
    } else {
        // 情况2：用户已有订阅 - 处理续费或升级
        handleExistingSubscription(order, currentSubscription);
    }
}

private void createNewSubscription(Order order) {
    // 创建新订阅
    UserSubscription subscription = new UserSubscription();
    subscription.setUserId(order.getUserId());
    subscription.setPackageId(order.getPackageId());
    subscription.setOrderId(order.getId());
    subscription.setStatus("ACTIVE");
    subscription.setStartTime(LocalDateTime.now());
    subscription.setEndTime(calculateEndTime(order));
    userSubscriptionMapper.insert(subscription);
    
    // 记录订阅历史
    recordSubscriptionHistory(subscription, order, "CREATE");
}

private void handleExistingSubscription(Order order, UserSubscription currentSubscription) {
    // 判断是续费还是升级
    if (currentSubscription.getPackageId().equals(order.getPackageId())) {
        // 同套餐续费 - 延长结束时间
        renewSubscription(order, currentSubscription);
    } else {
        // 不同套餐 - 升级或降级
        upgradeSubscription(order, currentSubscription);
    }
}

private void renewSubscription(Order order, UserSubscription currentSubscription) {
    // 从当前结束时间开始延长
    LocalDateTime newEndTime = calculateEndTimeFromBase(order, currentSubscription.getEndTime());
    
    currentSubscription.setEndTime(newEndTime);
    currentSubscription.setUpdatedAt(LocalDateTime.now());
    userSubscriptionMapper.updateById(currentSubscription);
    
    // 记录历史
    recordSubscriptionHistory(currentSubscription, order, "RENEW");
}

private void upgradeSubscription(Order order, UserSubscription currentSubscription) {
    // 立即切换到新套餐
    currentSubscription.setPackageId(order.getPackageId());
    currentSubscription.setEndTime(calculateEndTime(order));
    currentSubscription.setUpdatedAt(LocalDateTime.now());
    userSubscriptionMapper.updateById(currentSubscription);
    
    // 记录历史
    recordSubscriptionHistory(currentSubscription, order, "UPGRADE");
}
```

## 5. 频率限制检查逻辑

### 5.1 用户权限检查服务
```java
@Service
public class ProductUsageService {
    
    /**
     * 检查用户是否可以使用指定产品
     */
    public ApiResponse<Boolean> checkUserCanUse(Long userId, String productCode) {
        // 1. 获取用户当前订阅
        UserSubscription subscription = getCurrentSubscription(userId);
        
        // 2. 确定用户套餐（没有订阅则使用免费套餐）
        Package userPackage = getUserPackage(subscription);
        
        // 3. 解析套餐限制
        Map<String, Object> limits = parseLimits(userPackage, productCode);
        if (limits == null) {
            return ApiResponse.success(false, "套餐不支持该产品");
        }
        
        // 4. 检查使用限制
        boolean canUse = checkUsageLimits(userId, productCode, limits, subscription);
        
        return ApiResponse.success(canUse, canUse ? "可以使用" : "已达到使用限制");
    }
    
    private Package getUserPackage(UserSubscription subscription) {
        if (subscription == null || subscription.getEndTime().isBefore(LocalDateTime.now())) {
            // 没有订阅或已过期，使用免费套餐
            return packageMapper.selectOne(new QueryWrapper<Package>().eq("name", "free"));
        }
        return packageMapper.selectById(subscription.getPackageId());
    }
    
    private Map<String, Object> parseLimits(Package pkg, String productCode) {
        if (pkg.getLimits() == null) return null;
        
        // 解析JSON配置
        Map<String, Object> allLimits = JSONUtil.toBean(pkg.getLimits().toString(), Map.class);
        return (Map<String, Object>) allLimits.get(productCode);
    }
    
    private boolean checkUsageLimits(Long userId, String productCode, 
                                   Map<String, Object> limits, UserSubscription subscription) {
        
        // 检查每日限制
        if (limits.containsKey("daily")) {
            Integer dailyLimit = (Integer) limits.get("daily");
            if (dailyLimit != null && dailyLimit > 0) {
                Integer todayUsage = getTodayUsage(userId, productCode);
                if (todayUsage >= dailyLimit) {
                    return false;
                }
            }
        }
        
        // 检查每小时限制
        if (limits.containsKey("hourly")) {
            Integer hourlyLimit = (Integer) limits.get("hourly");
            if (hourlyLimit != null && hourlyLimit > 0) {
                Integer hourlyUsage = getHourlyUsage(userId, productCode);
                if (hourlyUsage >= hourlyLimit) {
                    return false;
                }
            }
        }
        
        // 检查自定义时间窗口限制（如3小时50次）
        if (limits.containsKey("custom_hours") && limits.containsKey("custom_count")) {
            Integer customHours = (Integer) limits.get("custom_hours");
            Integer customCount = (Integer) limits.get("custom_count");
            if (customHours != null && customCount != null) {
                Integer customUsage = getCustomWindowUsage(userId, productCode, customHours);
                if (customUsage >= customCount) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 记录产品使用
     */
    public void recordUsage(Long userId, String productCode, 
                           Map<String, Object> requestData, 
                           Map<String, Object> responseData) {
        UserSubscription subscription = getCurrentSubscription(userId);
        
        UserProductUsage usage = new UserProductUsage();
        usage.setUserId(userId);
        usage.setProductCode(productCode);
        usage.setSubscriptionId(subscription != null ? subscription.getId() : null);
        usage.setUsageCount(1);
        usage.setRequestData(requestData);
        usage.setResponseData(responseData);
        
        userProductUsageMapper.insert(usage);
    }
    
    // 获取今日使用次数
    private Integer getTodayUsage(Long userId, String productCode) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        return userProductUsageMapper.countUsage(userId, productCode, startOfDay, LocalDateTime.now());
    }
    
    // 获取最近1小时使用次数
    private Integer getHourlyUsage(Long userId, String productCode) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        return userProductUsageMapper.countUsage(userId, productCode, oneHourAgo, LocalDateTime.now());
    }
    
    // 获取自定义时间窗口使用次数
    private Integer getCustomWindowUsage(Long userId, String productCode, Integer hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        return userProductUsageMapper.countUsage(userId, productCode, startTime, LocalDateTime.now());
    }
}
```

## 6. 控制器接口

### 6.1 产品使用控制器
```java
@RestController
@RequestMapping("/api/ai")
@RequiredArgsConstructor
public class AiProductController {
    
    private final ProductUsageService productUsageService;
    
    /**
     * 使用ChatGPT
     */
    @PostMapping("/chatgpt")
    @SaCheckLogin
    public ApiResponse<String> useChatGPT(@RequestBody Map<String, Object> request) {
        Long userId = StpUtil.getLoginIdAsLong();
        String productCode = "chatgpt";
        
        // 1. 检查使用权限
        ApiResponse<Boolean> canUse = productUsageService.checkUserCanUse(userId, productCode);
        if (!canUse.getSuccess() || !canUse.getData()) {
            return ApiResponse.error(canUse.getMessage());
        }
        
        try {
            // 2. 调用ChatGPT服务
            String response = callChatGPTService(request);
            
            // 3. 记录使用
            Map<String, Object> responseData = Map.of("response", response);
            productUsageService.recordUsage(userId, productCode, request, responseData);
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("ChatGPT服务调用失败", e);
            return ApiResponse.error("服务异常，请稍后重试");
        }
    }
    
    /**
     * 获取用户使用统计
     */
    @GetMapping("/usage-stats")
    @SaCheckLogin
    public ApiResponse<Map<String, Object>> getUsageStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("chatgpt_today", productUsageService.getTodayUsage(userId, "chatgpt"));
        stats.put("image_gen_today", productUsageService.getTodayUsage(userId, "image_gen"));
        stats.put("voice_text_today", productUsageService.getTodayUsage(userId, "voice_text"));
        
        return ApiResponse.success(stats);
    }
    
    /**
     * 检查产品使用权限
     */
    @GetMapping("/check/{productCode}")
    @SaCheckLogin
    public ApiResponse<Map<String, Object>> checkProductUsage(@PathVariable String productCode) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        ApiResponse<Boolean> canUse = productUsageService.checkUserCanUse(userId, productCode);
        
        Map<String, Object> result = new HashMap<>();
        result.put("canUse", canUse.getData());
        result.put("message", canUse.getMessage());
        result.put("todayUsed", productUsageService.getTodayUsage(userId, productCode));
        
        return ApiResponse.success(result);
    }
}
```

## 7. Mapper查询方法

### 7.1 使用记录查询
```java
@Mapper
public interface UserProductUsageMapper extends BaseMapper<UserProductUsage> {
    
    /**
     * 统计指定时间范围内的使用次数
     */
    @Select("SELECT COALESCE(SUM(usage_count), 0) FROM user_product_usage " +
            "WHERE user_id = #{userId} AND product_code = #{productCode} " +
            "AND created_at >= #{startTime} AND created_at <= #{endTime}")
    Integer countUsage(@Param("userId") Long userId, 
                      @Param("productCode") String productCode,
                      @Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取用户每日使用统计
     */
    @Select("SELECT DATE(created_at) as date, SUM(usage_count) as count " +
            "FROM user_product_usage " +
            "WHERE user_id = #{userId} AND product_code = #{productCode} " +
            "AND created_at >= #{startDate} " +
            "GROUP BY DATE(created_at) ORDER BY date DESC")
    List<Map<String, Object>> getDailyUsageStats(@Param("userId") Long userId,
                                                 @Param("productCode") String productCode,
                                                 @Param("startDate") LocalDateTime startDate);
}
```

## 8. 实施步骤

### 8.1 数据库修改
```sql
-- 1. 修改packages表
ALTER TABLE `packages` ADD COLUMN `limits` json DEFAULT NULL COMMENT 'AI产品使用限制配置';

-- 2. 创建使用记录表
-- (见上面的CREATE TABLE语句)

-- 3. 插入示例配置
UPDATE packages SET limits = '{"chatgpt": {"daily": 3}, "image_gen": {"daily": 1}, "voice_text": {"daily": 5}}' WHERE name = 'free';
UPDATE packages SET limits = '{"chatgpt": {"custom_hours": 3, "custom_count": 50}, "image_gen": {"daily": 10}, "voice_text": {"daily": 50}}' WHERE name = 'plus';
UPDATE packages SET limits = '{"chatgpt": {"custom_hours": 3, "custom_count": 100}, "image_gen": {"daily": 50}, "voice_text": {"daily": 200}}' WHERE name = 'pro';
```

### 8.2 修改支付服务
在PaymentService中修改processSubscription方法，按照上面的逻辑处理订阅。

### 8.3 添加新的服务和控制器
实现ProductUsageService和AiProductController。

## 9. 总结

这个简化方案的优势：
- **简单直接**：在Package表中直接配置限制，避免复杂关联
- **灵活配置**：JSON格式支持各种限制策略
- **易于理解**：订阅逻辑清晰，分为新建和续费/升级两种情况
- **扩展性好**：新增AI产品只需在JSON中添加配置

核心逻辑：
1. **用户订阅表**：存储当前订阅状态，快速查询用户权限
2. **订阅历史表**：记录所有变更，用于分析和追溯
3. **支付回调**：区分新用户和老用户，分别处理订阅创建和续费/升级
4. **频率限制**：基于JSON配置和使用记录表实现灵活的限制策略 