# 订单管理 API 文档

## 概述

本文档描述了订单管理相关的所有API接口，包括订单创建、查询、取消、删除以及优惠码相关功能。

**基础URL**: `/orders`

**认证方式**: 所有接口都需要Bearer Token认证

**请求头**:
```http
Authorization: Bearer <your-token>
Content-Type: application/json 或 application/x-www-form-urlencoded
```

---

## 接口列表

### 1. 预验证优惠码

**接口地址**: `POST /orders/preview-discount`

**接口描述**: 在创建订单前预验证优惠码，显示预期优惠效果

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| discountCode | String | 是 | 优惠码，3-50个字符 |
| packagePriceId | Long | 是 | 套餐价格ID |

**请求示例**:
```bash
curl -X POST "https://api.labiai.com/orders/preview-discount" \
  -H "Authorization: Bearer your_access_token" \
  -d "discountCode=SAVE20&packagePriceId=123"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "优惠码验证成功",
  "data": {
    "discountCodeId": 1,
    "discountCode": "SAVE20",
    "valid": true,
    "discountType": "PERCENTAGE",
    "discountValue": 20.00,
    "discountAmount": 19.98,
    "originalAmount": 99.90,
    "finalAmount": 79.92,
    "packageName": "专业版套餐",
    "billingInfo": "3个月",
    "failureReason": null
  },
  "traceId": "trace-123456"
}
```

### 2. 创建订单

**接口地址**: `POST /orders/create`

**接口描述**: 创建新订单，可选择使用优惠码

**请求参数**:
```
Content-Type: application/x-www-form-urlencoded
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| packagePriceId | Long | 是 | 套餐价格ID |
| discountCode | String | 否 | 优惠码，3-50个字符 |

**请求示例**:
```bash
curl -X POST "https://api.labiai.com/orders/create" \
  -H "Authorization: Bearer your_access_token" \
  -d "packagePriceId=123&discountCode=SAVE20"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": 456,
    "orderNo": "ORD20241201143025ABC123",
    "packageName": "专业版套餐",
    "packageFeatures": [
      "无限制AI对话",
      "高级模型访问",
      "优先客服支持"
    ],
    "billingInfo": "3个月",
    "originalAmount": 99.90,
    "discountAmount": 19.98,
    "finalAmount": 79.92,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-01T15:00:25",
    "createdAt": "2024-12-01T14:30:25"
  },
  "traceId": "trace-123456"
}
```

### 业务规则
- 每个用户最多可有4个未付款订单
- 优惠码会在创建订单时被消费（使用次数+1）
- 订单默认30分钟后过期

### 3. 根据订单号查询订单详情

**接口地址**: `GET /orders/order-no/{orderNo}`

**接口描述**: 根据订单号查询订单详情，只能查询属于当前用户的订单

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单号 |

**请求示例**:
```bash
curl -X GET "https://api.labiai.com/orders/order-no/ORD20241201143025ABC123" \
  -H "Authorization: Bearer your_access_token"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "orderId": 456,
    "orderNo": "ORD20241201143025ABC123",
    "packageName": "专业版套餐",
    "packageFeatures": [
      "无限制AI对话",
      "高级模型访问",
      "优先客服支持"
    ],
    "billingInfo": "3个月",
    "originalAmount": 99.90,
    "discountAmount": 19.98,
    "finalAmount": 79.92,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-01T15:00:25",
    "createdAt": "2024-12-01T14:30:25"
  },
  "traceId": "trace-123456"
}
```

### 4. 根据订单ID查询订单详情

**接口地址**: `GET /orders/{orderId}`

**接口描述**: 根据订单ID查询订单详情，只能查询属于当前用户的订单

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 订单ID |

**请求示例**:
```bash
curl -X GET "https://api.labiai.com/orders/456" \
  -H "Authorization: Bearer your_access_token"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "orderId": 456,
    "orderNo": "ORD20241201143025ABC123",
    "packageName": "专业版套餐",
    "packageFeatures": [
      "无限制AI对话",
      "高级模型访问",
      "优先客服支持"
    ],
    "billingInfo": "3个月",
    "originalAmount": 99.90,
    "discountAmount": 19.98,
    "finalAmount": 79.92,
    "currency": "CNY",
    "status": "PENDING",
    "expiredAt": "2024-12-01T15:00:25",
    "createdAt": "2024-12-01T14:30:25"
  },
  "traceId": "trace-123456"
}
```

### 5. 获取用户订单列表

**接口地址**: `GET /orders`

**接口描述**: 获取当前用户的订单列表，支持分页和状态筛选

**查询参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| status | String | 否 | - | 订单状态筛选 |
| page | Integer | 否 | 1 | 页码，从1开始 |
| size | Integer | 否 | 10 | 每页数量，最大100 |

**订单状态说明**:
- `PENDING`: 待支付
- `PAID`: 已支付
- `CANCELLED`: 已取消
- `EXPIRED`: 已过期

**请求示例**:
```bash
curl -X GET "https://api.labiai.com/orders?status=PENDING&page=1&size=10" \
  -H "Authorization: Bearer your_access_token"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取订单列表成功",
  "data": [
    {
      "orderId": 456,
      "orderNo": "ORD20241201143025ABC123",
      "packageName": "专业版套餐",
      "packageFeatures": [
        "无限制AI对话",
        "高级模型访问",
        "优先客服支持"
      ],
      "billingInfo": "3个月",
      "originalAmount": 99.90,
      "discountAmount": 19.98,
      "finalAmount": 79.92,
      "currency": "CNY",
      "status": "PENDING",
      "expiredAt": "2024-12-01T15:00:25",
      "createdAt": "2024-12-01T14:30:25"
    }
  ],
  "traceId": "trace-123456"
}
```

### 6. 取消订单

**接口地址**: `PUT /orders/{orderId}/cancel`

**接口描述**: 取消指定的订单，只能取消状态为"待支付"的订单

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 订单ID |

**请求示例**:
```bash
curl -X PUT "https://api.labiai.com/orders/456/cancel" \
  -H "Authorization: Bearer your_access_token"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "订单取消成功",
  "data": null,
  "traceId": "trace-123456"
}
```

### 业务规则
- 只有状态为`PENDING`的订单可以取消
- **取消订单会自动释放使用的优惠码**，恢复优惠码的使用次数
- 优惠码释放后，其他用户可以继续使用该优惠码
- 如果优惠码释放失败，不会影响订单取消操作

### 7. 删除订单

**接口地址**: `DELETE /orders/{orderId}`

**接口描述**: 删除指定的订单（逻辑删除），只能删除状态为"已取消"的订单

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 订单ID |

**请求示例**:
```bash
curl -X DELETE "https://api.labiai.com/orders/456" \
  -H "Authorization: Bearer your_access_token"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "订单删除成功",
  "data": null,
  "traceId": "trace-123456"
}
```

### 8. 获取订单优惠码详情

**接口地址**: `GET /orders/{orderId}/discount-info`

**接口描述**: 获取订单的优惠码使用详情

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 订单ID |

**请求示例**:
```bash
curl -X GET "https://api.labiai.com/orders/456/discount-info" \
  -H "Authorization: Bearer your_access_token"
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取订单优惠码信息成功",
  "data": {
    "orderId": 456,
    "orderNo": "ORD20241201143025ABC123",
    "originalAmount": 99.90,
    "discountAmount": 19.98,
    "finalAmount": 79.92,
    "hasDiscount": true,
    "discountRate": 20.00,
    "discountCodeInfo": {
      "discountCodeId": 1,
      "discountCode": "SAVE20",
      "discountName": "新用户专享8折",
      "discountType": "PERCENTAGE",
      "discountValue": 20.00,
      "discountAmount": 19.98,
      "usageStatus": "USED",
      "usedAt": "2024-12-01T14:30:25",
      "orderId": 456,
      "userId": 123,
      "codeUsedCount": 1
    },
    "discountCode": "SAVE20",
    "discountType": "PERCENTAGE",
    "discountValue": 20.00,
    "usedAt": "2024-12-01T14:30:25"
  },
  "traceId": "trace-123456"
}
```

### 无优惠码的响应
```json
{
  "success": true,
  "code": 200,
  "message": "获取订单优惠码信息成功",
  "data": {
    "orderId": 456,
    "orderNo": "ORD20241201143025ABC123",
    "originalAmount": 99.90,
    "discountAmount": 0.00,
    "finalAmount": 99.90,
    "hasDiscount": false,
    "discountRate": 0.00,
    "discountCodeInfo": null,
    "discountCode": null
  },
  "traceId": "trace-123456"
}
```

---

## 错误响应

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40100 | 未授权访问 | 检查 access_token 是否有效 |
| 40300 | 访问被禁止 | 无权操作此订单 |
| 40003 | 参数格式不正确 | 检查参数类型和格式 |
| 40400 | 资源不存在 | 订单不存在 |
| 60000 | 业务逻辑错误 | 订单状态不允许当前操作 |
| 61020 | 订单创建失败 | 套餐价格不存在或已禁用 |

### 错误响应示例

```json
{
  "success": false,
  "code": 40300,
  "message": "无权查看此订单",
  "data": null
}
```

---

## 前端集成指南

### 1. 认证处理

所有订单接口都需要用户登录，前端需要在请求头中携带 access_token：

```javascript
// 设置默认请求头
axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;

// 或者在每个请求中单独设置
const response = await axios.get('/orders', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
```

### 2. 错误处理

```javascript
try {
  const response = await axios.get('/orders/12345');
  if (response.data.success) {
    // 处理成功响应
    const order = response.data.data;
    console.log('订单详情:', order);
  } else {
    // 处理业务错误
    console.error('业务错误:', response.data.message);
  }
} catch (error) {
  if (error.response) {
    // 服务器返回错误状态码
    const { code, message } = error.response.data;
    switch (code) {
      case 40100:
        // 未授权，跳转到登录页
        router.push('/login');
        break;
      case 40300:
        // 权限不足
        showMessage('无权操作此订单');
        break;
      default:
        showMessage(message || '操作失败');
    }
  } else {
    // 网络错误
    console.error('网络错误:', error.message);
  }
}
```

### 3. 订单状态处理

```javascript
// 订单状态映射
const ORDER_STATUS_MAP = {
  'PENDING': { text: '待支付', color: 'orange' },
  'PAID': { text: '已支付', color: 'green' },
  'CANCELLED': { text: '已取消', color: 'gray' },
  'REFUNDED': { text: '已退款', color: 'blue' }
};

// 获取状态显示信息
function getOrderStatusInfo(status) {
  return ORDER_STATUS_MAP[status] || { text: '未知', color: 'gray' };
}

// 判断订单是否可以取消
function canCancelOrder(order) {
  return order.status === 'PENDING';
}

// 判断订单是否可以删除
function canDeleteOrder(order) {
  return order.status === 'CANCELLED';
}
```

### 4. 分页处理

```javascript
// 订单列表组件示例
const OrderList = {
  data() {
    return {
      orders: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      statusFilter: ''
    };
  },
  
  methods: {
    async fetchOrders() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.current,
          size: this.pagination.pageSize
        };
        
        if (this.statusFilter) {
          params.status = this.statusFilter;
        }
        
        const response = await axios.get('/orders', { params });
        
        if (response.data.success) {
          this.orders = response.data.data;
          // 注意：当前接口返回的是数组，如果需要总数需要后端返回分页信息
        }
      } catch (error) {
        console.error('获取订单列表失败:', error);
      } finally {
        this.loading = false;
      }
    },
    
    onPageChange(page) {
      this.pagination.current = page;
      this.fetchOrders();
    },
    
    onStatusFilterChange(status) {
      this.statusFilter = status;
      this.pagination.current = 1; // 重置到第一页
      this.fetchOrders();
    }
  },
  
  mounted() {
    this.fetchOrders();
  }
};
```

### 5. 创建订单流程

```javascript
// 创建订单的完整流程
async function createOrder(packagePriceId, discountCode = '') {
  try {
    // 1. 创建订单
    const orderResponse = await axios.post('/orders/create', {
      packagePriceId,
      discountCode
    });
    
    if (!orderResponse.data.success) {
      throw new Error(orderResponse.data.message);
    }
    
    const order = orderResponse.data.data;
    console.log('订单创建成功:', order);
    
    // 2. 跳转到支付页面
    router.push({
      name: 'Payment',
      params: { orderId: order.orderId }
    });
    
    return order;
    
  } catch (error) {
    console.error('创建订单失败:', error);
    throw error;
  }
}
```

---

## 注意事项

1. **订单过期**: 订单创建后30分钟内未支付会自动过期，过期订单使用的优惠码会被释放
2. **优惠码管理**: 
   - 预验证不会消费优惠码使用次数，只用于展示预期效果
   - **创建订单流程优化**：先创建基础订单，再使用原子操作验证并消费优惠码
   - 如果优惠码在创建订单时已被其他用户用完，会自动回滚删除订单并返回错误
   - **手动取消订单会自动释放优惠码**，恢复使用次数供其他用户使用
   - 订单超时自动取消时也会释放优惠码
   - 数据库层面使用原子更新（`validateAndUseDiscountCode`）确保不会出现超售情况
3. **权限控制**: 用户只能操作自己的订单，后端会自动验证订单所有权
4. **状态限制**: 
   - 只有"待支付"状态的订单可以取消
   - 只有"已取消"状态的订单可以删除
5. **并发限制**: 每个用户最多同时拥有4个未付款订单
6. **分页限制**: 每页最大返回100条记录
7. **时间格式**: 所有时间字段使用 ISO 8601 格式 (YYYY-MM-DDTHH:mm:ss)
8. **金额精度**: 所有金额字段保留2位小数
9. **逻辑删除**: 删除操作为逻辑删除，数据不会真正从数据库中移除
10. **追踪ID**: 每个响应都包含traceId，便于问题排查

---

## 更新日志

- **v1.0.0** (2024-12-01): 初始版本，包含基础的订单管理功能
- **v1.1.0** (2024-12-01): 新增优惠码预验证和详情查询功能
- **v1.2.0** (2024-12-01): 优化优惠码管理，手动取消订单和超时自动取消都会释放优惠码 