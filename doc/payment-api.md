# 支付系统API接口文档

基于LabIAI项目的支付系统API接口说明。

## 📝 接口概览

### 基础信息

- **基础URL**: `http://localhost:8080`
- **认证方式**: Sa-Token (Bearer Token)
- **数据格式**: JSON
- **编码方式**: UTF-8

### 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

**状态码说明：**
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未登录或登录已过期
- `403`: 无权限访问
- `500`: 服务器内部错误

## 🎯 套餐相关接口

### 1. 获取套餐列表

**接口地址：** `GET /api/packages`

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "basic",
      "display_name": "基础版",
      "description": "适合个人用户",
      "features": [
        "基础功能",
        "邮件支持", 
        "5GB存储"
      ],
      "sort_order": 1,
      "prices": [
        {
          "id": 1,
          "billing_cycle": "MONTH",
          "cycle_count": 1,
          "original_price": 99.00,
          "sale_price": 89.00,
          "currency": "CNY",
          "display_text": "月付",
          "discount_percent": 10
        },
        {
          "id": 2,
          "billing_cycle": "YEAR",
          "cycle_count": 1,
          "original_price": 1188.00,
          "sale_price": 899.00,
          "currency": "CNY",
          "display_text": "年付",
          "discount_percent": 24
        }
      ]
    }
  ]
}
```

### 2. 获取套餐详情

**接口地址：** `GET /api/packages/{id}`

**请求参数：**
- `id` (path): 套餐ID

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "basic",
    "display_name": "基础版",
    "description": "适合个人用户",
    "features": [
      "基础功能",
      "邮件支持",
      "5GB存储"
    ],
    "prices": [
      {
        "id": 1,
        "billing_cycle": "MONTH",
        "cycle_count": 1,
        "original_price": 99.00,
        "sale_price": 89.00,
        "currency": "CNY",
        "display_text": "月付"
      }
    ]
  }
}
```

### 3. 获取支付方式列表

**接口地址：** `GET /api/packages/payment-methods`

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "method_code": "xunhupay_auto",
      "method_name": "支付宝/微信支付",
      "method_type": "GATEWAY",
      "provider": "xunhupay",
      "icon_url": "https://example.com/icons/xunhupay.png",
      "description": "支持支付宝和微信支付，自动适配PC和手机端",
      "min_amount": 0.01,
      "max_amount": 50000.00,
      "priority": 100,
      "recommended": true
    }
  ]
}
```

## 💳 支付相关接口

### 1. 创建支付订单

**接口地址：** `POST /api/payment/create`

**请求方式：** `application/x-www-form-urlencoded`

**认证要求：** 需要登录

**请求参数：**
- `packagePriceId` (form): 套餐价格ID (必填)
- `methodCode` (form): 支付方式代码 (必填)

**请求示例：**
```bash
curl -X POST "http://localhost:8080/api/payment/create" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d "packagePriceId=1&methodCode=xunhupay_auto"
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "payment_no": "P20241216123456123456",
    "order_no": "O20241216123456123456",
    "amount": 89.00,
    "payment_url": "https://api.xunhupay.com/pay/abc123",
    "qr_code_url": "https://api.xunhupay.com/qrcode/abc123",
    "expired_at": "2024-12-16T13:24:56",
    "package_name": "基础版",
    "billing_info": "MONTH x 1"
  }
}
```

**错误响应示例：**
```json
{
  "code": 400,
  "message": "套餐价格不存在",
  "data": null
}
```

### 2. 支付成功回跳

**接口地址：** `GET /api/payment/success`

**请求参数：**
- `trade_order_id` (query): 商户订单号 (可选)

**响应示例：**
```json
{
  "code": 200,
  "message": "支付成功",
  "data": null
}
```

### 3. 支付取消回跳

**接口地址：** `GET /api/payment/cancel`

**请求参数：**
- `trade_order_id` (query): 商户订单号 (可选)

**响应示例：**
```json
{
  "code": 200,
  "message": "支付已取消",
  "data": null
}
```

### 4. 支付回调（内部接口）

**接口地址：** `POST /api/payment/notify/xunhupay`

**说明：** 此接口由虎皮椒支付平台调用，用于通知支付结果

**请求方式：** `application/x-www-form-urlencoded`

**响应：** 字符串 `"success"` 或 `"fail"`

## 🔐 认证说明

### 获取Token

**接口地址：** `POST /auth/login`

**请求参数：**
```json
{
  "username": "<EMAIL>",
  "password": "password"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200
  }
}
```

### 使用Token

在请求头中添加：
```
Authorization: Bearer YOUR_TOKEN
```

## 📱 前端集成示例

### Vue.js示例

```javascript
// 获取套餐列表
async function getPackages() {
  try {
    const response = await fetch('/api/packages');
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取套餐失败:', error);
  }
}

// 创建支付订单
async function createPayment(packagePriceId, methodCode) {
  try {
    const token = localStorage.getItem('token');
    const formData = new FormData();
    formData.append('packagePriceId', packagePriceId);
    formData.append('methodCode', methodCode);
    
    const response = await fetch('/api/payment/create', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 跳转到支付页面
      if (isMobile()) {
        window.location.href = result.data.payment_url;
      } else {
        // PC端显示二维码
        showQRCode(result.data.qr_code_url);
      }
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建支付失败:', error);
  }
}

// 检测是否为移动端
function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}
```

### React示例

```jsx
import React, { useState, useEffect } from 'react';

function PaymentPage() {
  const [packages, setPackages] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadPackages();
    loadPaymentMethods();
  }, []);

  const loadPackages = async () => {
    try {
      const response = await fetch('/api/packages');
      const result = await response.json();
      if (result.code === 200) {
        setPackages(result.data);
      }
    } catch (error) {
      console.error('加载套餐失败:', error);
    }
  };

  const loadPaymentMethods = async () => {
    try {
      const response = await fetch('/api/packages/payment-methods');
      const result = await response.json();
      if (result.code === 200) {
        setPaymentMethods(result.data);
      }
    } catch (error) {
      console.error('加载支付方式失败:', error);
    }
  };

  const handlePayment = async (packagePriceId, methodCode) => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const formData = new FormData();
      formData.append('packagePriceId', packagePriceId);
      formData.append('methodCode', methodCode);

      const response = await fetch('/api/payment/create', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      const result = await response.json();
      
      if (result.code === 200) {
        // 跳转支付
        window.location.href = result.data.payment_url;
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error('支付失败:', error);
      alert('支付创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="payment-page">
      <h1>选择套餐</h1>
      {packages.map(pkg => (
        <div key={pkg.id} className="package-card">
          <h3>{pkg.display_name}</h3>
          <p>{pkg.description}</p>
          <div className="prices">
            {pkg.prices.map(price => (
              <button
                key={price.id}
                onClick={() => handlePayment(price.id, 'xunhupay_auto')}
                disabled={loading}
              >
                {price.display_text} - ¥{price.sale_price}
              </button>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

export default PaymentPage;
```

## ⚠️ 错误码说明

### 支付相关错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 套餐价格不存在 | 传入的packagePriceId无效 |
| 400 | 套餐不存在或已禁用 | 套餐已下架或禁用 |
| 400 | 支付方式不存在或已禁用 | 支付方式配置错误 |
| 400 | 支付金额低于最小限制 | 订单金额太小 |
| 400 | 支付金额超过最大限制 | 订单金额太大 |
| 401 | 用户未登录 | 需要先登录 |
| 500 | 虎皮椒支付创建失败 | 第三方支付接口错误 |
| 500 | 支付系统异常，请稍后重试 | 系统内部错误 |

## 📊 数据字典

### 计费周期枚举

| 值 | 说明 |
|----|------|
| DAY | 按天计费 |
| MONTH | 按月计费 |
| QUARTER | 按季度计费 |
| YEAR | 按年计费 |

### 支付方式类型

| 值 | 说明 |
|----|------|
| GATEWAY | 支付网关 |
| ALIPAY | 支付宝 |
| WECHAT | 微信支付 |
| UNIONPAY | 银联支付 |
| BANK | 银行转账 |

### 订单状态

| 值 | 说明 |
|----|------|
| PENDING | 待支付 |
| PAID | 已支付 |
| CANCELLED | 已取消 |
| REFUNDED | 已退款 |
| PARTIAL_REFUNDED | 部分退款 |

### 支付状态

| 值 | 说明 |
|----|------|
| PENDING | 待支付 |
| SUCCESS | 支付成功 |
| FAILED | 支付失败 |
| CANCELLED | 已取消 |
| EXPIRED | 已过期 |

## 🔧 测试工具

### Postman集合

可以导入以下Postman集合进行API测试：

```json
{
  "info": {
    "name": "LabIAI Payment API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "获取套餐列表",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}/api/packages",
          "host": ["{{baseUrl}}"],
          "path": ["api", "packages"]
        }
      }
    },
    {
      "name": "创建支付订单",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{token}}"
          }
        ],
        "body": {
          "mode": "urlencoded",
          "urlencoded": [
            {
              "key": "packagePriceId",
              "value": "1"
            },
            {
              "key": "methodCode",
              "value": "xunhupay_auto"
            }
          ]
        },
        "url": {
          "raw": "{{baseUrl}}/api/payment/create",
          "host": ["{{baseUrl}}"],
          "path": ["api", "payment", "create"]
        }
      }
    }
  ],
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080"
    },
    {
      "key": "token",
      "value": "your_token_here"
    }
  ]
}
```

### cURL测试命令

```bash
# 设置基础变量
BASE_URL="http://localhost:8080"
TOKEN="your_token_here"

# 1. 获取套餐列表
curl -X GET "${BASE_URL}/api/packages" \
     -H "Content-Type: application/json"

# 2. 获取支付方式
curl -X GET "${BASE_URL}/api/packages/payment-methods" \
     -H "Content-Type: application/json"

# 3. 创建支付订单
curl -X POST "${BASE_URL}/api/payment/create" \
     -H "Authorization: Bearer ${TOKEN}" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "packagePriceId=1&methodCode=xunhupay_auto"
```

## 📞 技术支持

如有API使用问题，请联系技术支持团队或查看详细的错误日志。 