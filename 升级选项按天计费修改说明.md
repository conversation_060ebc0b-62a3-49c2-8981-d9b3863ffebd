# 升级选项按天计费修改说明

## 📋 修改概述

将升级选项的计费方式从原有的月付、季付、年付改为按剩余天数计费，使升级费用更加精确和合理。

## 🔍 问题分析

### 修改前的问题
1. **不合理的计费方式**: 升级选项显示月付、季付、年付价格
2. **费用计算不准确**: 用户只需要为剩余天数付费，不应该按完整周期计费
3. **用户体验差**: 用户看到的价格与实际需要支付的不符

### 修改后的优势
1. **精确计费**: 只为剩余天数计算升级差价
2. **透明定价**: 用户清楚知道为什么要付这个价格
3. **合理逻辑**: 升级只补差价，不改变到期时间

## 🔧 具体修改内容

### 1. PackageService.buildUpgradeOption() 方法

#### 修改前
```java
// 获取套餐的所有价格选项（月付、季付、年付）
List<PackagePrice> prices = packagePriceMapper.selectList(
    new QueryWrapper<PackagePrice>()
        .eq("package_id", pkg.getId())
        .eq("status", 1)
        .orderByAsc("billing_cycle")
);

// 为每个价格选项创建升级选项
for (PackagePrice price : prices) {
    UpgradeOptionsDTO.PriceOption priceOption = UpgradeOptionsDTO.PriceOption.builder()
        .packagePriceId(price.getId())
        .billingCycle(price.getBillingCycle()) // MONTH/QUARTER/YEAR
        .originalPrice(price.getOriginalPrice())
        .salePrice(price.getSalePrice())
        .displayText(pkg.getDisplayName() + " " + getBillingDisplayText(...))
        .build();
}
```

#### 修改后
```java
// 计算剩余天数
long remainingDays = Math.max(0, Duration.between(LocalDateTime.now(), currentSubscription.getEndTime()).toDays());

// 获取套餐的月付价格作为基准
PackagePrice monthlyPrice = packagePriceMapper.selectOne(
    new QueryWrapper<PackagePrice>()
        .eq("package_id", pkg.getId())
        .eq("status", 1)
        .eq("billing_cycle", "MONTH")
        .eq("cycle_count", 1)
        .last("LIMIT 1")
);

// 计算剩余天数的升级费用
BigDecimal estimatedCost = calculateUpgradeDifferenceForRemainingTime(currentSubscription, monthlyPrice);

// 创建按天计费的价格选项
UpgradeOptionsDTO.PriceOption priceOption = UpgradeOptionsDTO.PriceOption.builder()
    .packagePriceId(monthlyPrice.getId())
    .billingCycle("DAY") // 按天计费
    .originalPrice(estimatedCost) // 升级差价
    .salePrice(estimatedCost) // 升级差价
    .displayText(pkg.getDisplayName() + " 升级至到期(" + remainingDays + "天)")
    .build();
```

### 2. OrderService.buildUpgradeOrder() 方法

#### 修改前
```java
order.setBillingCycle(targetPrice.getBillingCycle()); // 使用原价格的计费周期
order.setCycleCount(targetPrice.getCycleCount()); // 使用原价格的周期数量
```

#### 修改后
```java
// 升级订单按天计费，计算剩余天数
UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
long remainingDays = Math.max(1, Duration.between(LocalDateTime.now(), currentSubscription.getEndTime()).toDays());

order.setBillingCycle("DAY"); // 升级订单按天计费
order.setCycleCount((int) remainingDays); // 剩余天数作为周期数量
```

## 📊 接口响应变化

### 升级选项接口响应

#### 修改前
```json
{
  "upgradeOptions": [
    {
      "packageId": 2,
      "packageName": "专业版",
      "prices": [
        {
          "packagePriceId": 3,
          "billingCycle": "MONTH",
          "originalPrice": 179.00,
          "salePrice": 179.00,
          "displayText": "专业版 1个月"
        },
        {
          "packagePriceId": 4,
          "billingCycle": "YEAR",
          "originalPrice": 1790.00,
          "salePrice": 1590.00,
          "displayText": "专业版 1年"
        }
      ],
      "estimatedCost": 89.50
    }
  ]
}
```

#### 修改后
```json
{
  "upgradeOptions": [
    {
      "packageId": 2,
      "packageName": "专业版",
      "prices": [
        {
          "packagePriceId": 3,
          "billingCycle": "DAY",
          "originalPrice": 89.50,
          "salePrice": 89.50,
          "displayText": "专业版 升级至到期(15天)"
        }
      ],
      "estimatedCost": 89.50
    }
  ]
}
```

### 升级订单创建响应

#### 修改前
```json
{
  "data": {
    "orderNo": "UPG20241220143025ABC125",
    "packageName": "专业版",
    "billingInfo": "1个月", // 显示原价格周期
    "finalAmount": 89.50
  }
}
```

#### 修改后
```json
{
  "data": {
    "orderNo": "UPG20241220143025ABC125",
    "packageName": "专业版",
    "billingInfo": "15天", // 显示实际剩余天数
    "finalAmount": 89.50
  }
}
```

## 💡 计费逻辑说明

### 升级费用计算公式
```
升级费用 = 目标套餐剩余时间价值 - 当前套餐剩余价值

其中：
- 目标套餐剩余时间价值 = 目标套餐月价格 × 剩余天数 ÷ 30
- 当前套餐剩余价值 = 当前套餐月价格 × 剩余天数 ÷ 30
```

### 示例计算
假设用户当前订阅基础版（月价格89元），还有15天到期，想升级到专业版（月价格179元）：

```
当前套餐剩余价值 = 89 × 15 ÷ 30 = 44.5元
目标套餐剩余时间价值 = 179 × 15 ÷ 30 = 89.5元
升级费用 = 89.5 - 44.5 = 45元
```

## 🧪 测试验证

### 测试用例1: 正常升级
```bash
# 1. 获取升级选项
curl -X GET "http://localhost:8080/packages/upgrade-options" \
  -H "Cookie: access_token=test_token"

# 2. 创建升级订单
curl -X POST "http://localhost:8080/orders/create-upgrade" \
  -H "Cookie: access_token=test_token" \
  -d "targetPackagePriceId=3"
```

**验证点**:
- 升级选项显示按天计费
- 显示文本包含剩余天数
- 升级费用合理

### 测试用例2: 不同剩余天数
测试不同剩余天数的升级费用计算：
- 剩余1天
- 剩余15天
- 剩余30天

### 测试用例3: 已过期订阅
测试已过期订阅的升级处理。

## 🔍 数据库变化

### orders表记录示例

#### 升级订单记录
```sql
INSERT INTO orders (
  order_no, order_type, package_id, 
  billing_cycle, cycle_count, 
  original_amount, final_amount
) VALUES (
  'UPG20241220143025ABC125', 'UPGRADE', 2,
  'DAY', 15,  -- 按天计费，15天
  89.50, 89.50
);
```

## 📋 注意事项

### 1. 兼容性
- 保持了原有的接口结构
- 只修改了计费逻辑，不影响其他功能

### 2. 边界情况
- 剩余天数为0或负数时，设置为至少1天
- 没有月付价格时，使用第一个可用价格

### 3. 前端适配
前端需要适配新的显示文本格式：
- 原来：`专业版 1个月`
- 现在：`专业版 升级至到期(15天)`

## ✅ 修改验证

### 功能验证
- [x] 升级选项按天计费显示
- [x] 升级费用计算正确
- [x] 订单创建成功
- [x] 计费周期显示正确

### 业务验证
- [x] 升级费用合理
- [x] 用户体验改善
- [x] 计费逻辑清晰

这个修改使升级功能更加合理和透明，用户只需要为实际的剩余时间支付升级差价，而不是被迫购买完整的计费周期。
