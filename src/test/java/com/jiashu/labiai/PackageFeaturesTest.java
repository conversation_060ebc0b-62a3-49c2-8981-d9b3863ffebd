package com.jiashu.labiai;

import com.jiashu.labiai.entity.Package;
import com.jiashu.labiai.service.IPackageService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * Package features 字段测试
 */
@SpringBootTest
public class PackageFeaturesTest {

    @Autowired
    private IPackageService packageService;

    @Test
    public void testPackageFeatures() {
        // 查询所有套餐
        List<Package> packages = packageService.list();
        
        for (Package pkg : packages) {
            System.out.println("套餐ID: " + pkg.getId());
            System.out.println("套餐名称: " + pkg.getDisplayName());
            System.out.println("功能特性: " + pkg.getFeatures());
            System.out.println("功能特性类型: " + (pkg.getFeatures() != null ? pkg.getFeatures().getClass().getName() : "null"));
            System.out.println("---");
        }
    }
    
    @Test
    public void testSinglePackage() {
        // 查询单个套餐
//        Package pkg = packageMapper.selectById(1L);
//        if (pkg != null) {
//            System.out.println("单个套餐查询结果:");
//            System.out.println("Features: " + pkg.getFeatures());
//            System.out.println("Features Class: " + (pkg.getFeatures() != null ? pkg.getFeatures().getClass() : "null"));
//        } else {
//            System.out.println("未找到ID为1的套餐");
//        }
    }
} 