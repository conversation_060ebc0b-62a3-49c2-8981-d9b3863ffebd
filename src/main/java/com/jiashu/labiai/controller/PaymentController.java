package com.jiashu.labiai.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.exception.AuthorizationException;
import com.jiashu.labiai.exception.OrderException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.IOrderService;
import com.jiashu.labiai.service.IPaymentService;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付控制器
 */
@RestController
@RequestMapping("/payment")
@RequiredArgsConstructor
@Slf4j
public class PaymentController {

    private final IPaymentService paymentService;
    private final IOrderService orderService;

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    @SaCheckLogin
    public ApiResponse<PaymentCreateResponseDTO> createPayment(
            @RequestParam Long orderId,
            @RequestParam String paymentMethodCode,
            HttpServletRequest request) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        String clientIp = ServletUtil.getClientIP(request);
        String userAgent = request.getHeader("User-Agent");
        
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("orderId", orderId);
        requestDetails.put("paymentMethodCode", paymentMethodCode);
        requestDetails.put("clientIp", clientIp);
        TraceUtils.recordBusinessEvent("PAYMENT_CREATE_API_REQUEST", requestDetails);
        
        // 参数验证
        if (orderId == null || orderId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", orderId);
            TraceUtils.recordError("INVALID_ORDER_ID", errorDetails);
            throw OrderException.orderNotFound(orderId);
//            return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "订单ID无效");
        }
        
        if (StrUtil.isBlank(paymentMethodCode)) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("paymentMethodCode", paymentMethodCode);
            TraceUtils.recordError("MISSING_PAYMENT_METHOD", errorDetails);
            throw ValidationException.missingParameter("paymentMethodCode");
//            return ApiResponse.error(ResponseCode.MISSING_PARAMETER, "支付方式不能为空");
        }
        
        ApiResponse<PaymentCreateResponseDTO> result = paymentService.createPayment(
            orderId, paymentMethodCode, clientIp, userAgent);
        
        if (result.getSuccess()) {
            Map<String, Object> successDetails = new HashMap<>();
            successDetails.put("orderId", orderId);
            successDetails.put("traceId", TraceContext.getTraceId());
            TraceUtils.recordBusinessEvent("PAYMENT_CREATE_API_SUCCESS", successDetails);
        } else {
            Map<String, Object> failDetails = new HashMap<>();
            failDetails.put("orderId", orderId);
            failDetails.put("errorCode", result.getCode());
            failDetails.put("errorMessage", result.getMessage());
            TraceUtils.recordError("PAYMENT_CREATE_API_FAILED", failDetails);
        }
        
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取可用支付方式
     */
    @GetMapping("/methods")
    public ApiResponse<List<PaymentMethodDTO>> getPaymentMethods(
            @RequestParam(required = false, defaultValue = "0.01") BigDecimal amount) {
        
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("amount", amount);
        TraceUtils.recordBusinessEvent("PAYMENT_METHODS_REQUEST", requestDetails);
        
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("amount", amount);
            TraceUtils.recordError("INVALID_AMOUNT", errorDetails);
            throw ValidationException.invalidParameter("amount", amount);
//            return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "金额必须大于0");
        }
        
        ApiResponse<List<PaymentMethodDTO>> result = paymentService.getAvailablePaymentMethods(amount);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/status/{paymentNo}")
    @SaCheckLogin
    public ApiResponse<PaymentStatusDTO> queryPaymentStatus(@PathVariable String paymentNo) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        Map<String, Object> queryDetails = new HashMap<>();
        queryDetails.put("userId", userId);
        queryDetails.put("paymentNo", paymentNo);
        TraceUtils.recordBusinessEvent("PAYMENT_STATUS_QUERY", queryDetails);
        
        if (StrUtil.isBlank(paymentNo)) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("paymentNo", paymentNo);
            TraceUtils.recordError("MISSING_PAYMENT_NO", errorDetails);
            throw ValidationException.missingParameter("paymentNo");
//            return ApiResponse.error(ResponseCode.MISSING_PARAMETER, "支付单号不能为空");
        }
        
        ApiResponse<PaymentStatusDTO> result = paymentService.queryPaymentStatus(paymentNo);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 根据订单ID查询支付状态
     */
    @GetMapping("/order/{orderId}/status")
    @SaCheckLogin
    public ApiResponse<PaymentStatusDTO> queryPaymentStatusByOrderId(@PathVariable Long orderId) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        Map<String, Object> queryDetails = new HashMap<>();
        queryDetails.put("userId", userId);
        queryDetails.put("orderId", orderId);
        TraceUtils.recordBusinessEvent("ORDER_PAYMENT_STATUS_QUERY", queryDetails);
        
        if (orderId == null || orderId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", orderId);
            TraceUtils.recordError("INVALID_ORDER_ID", errorDetails);
            throw ValidationException.invalidParameter("orderId", orderId);
        }
        
        // 验证订单所有权 - 通过查询订单来验证
        try {
            ApiResponse<OrderDTO> orderResponse = orderService.getOrderById(orderId);
            if (!orderResponse.getSuccess() || orderResponse.getData() == null) {
                throw new AuthorizationException("无权查看此订单的支付状态");
            }
        } catch (Exception e) {
            log.warn("验证订单所有权失败: orderId={}, userId={}", orderId, userId, e);
            throw new AuthorizationException("无权查看此订单的支付状态");
        }
        
        ApiResponse<PaymentStatusDTO> result = paymentService.queryPaymentStatusByOrderId(orderId);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 虎皮椒支付回调
     */
    @PostMapping("/notify/xunhupay")
    public String handleXunhupayNotify(HttpServletRequest request) {
        try {
            String callbackData = getCallbackData(request);
            
            Map<String, Object> notifyDetails = new HashMap<>();
            notifyDetails.put("dataLength", callbackData.length());
            notifyDetails.put("clientIp", ServletUtil.getClientIP(request));
            TraceUtils.recordBusinessEvent("XUNHUPAY_NOTIFY_RECEIVED", notifyDetails);
            
            ApiResponse<String> result = paymentService.handlePaymentCallback("xunhupay", callbackData);
            
            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("result", result.getData());
                TraceUtils.recordBusinessEvent("XUNHUPAY_NOTIFY_SUCCESS", successDetails);
                return result.getData();
            } else {
                Map<String, Object> failDetails = new HashMap<>();
                failDetails.put("errorCode", result.getCode());
                failDetails.put("errorMessage", result.getMessage());
                TraceUtils.recordError("XUNHUPAY_NOTIFY_FAILED", failDetails);
                return "fail";
            }
            
        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("XUNHUPAY_NOTIFY_EXCEPTION", errorDetails);
            log.error("处理虎皮椒支付回调异常", e);
            return "fail";
        }
    }

    /**
     * 通用支付回调处理
     */
    @PostMapping("/notify/{provider}")
    public String handlePaymentNotify(@PathVariable String provider, HttpServletRequest request) {
        try {
            String callbackData = getCallbackData(request);
            
            Map<String, Object> notifyDetails = new HashMap<>();
            notifyDetails.put("provider", provider);
            notifyDetails.put("dataLength", callbackData.length());
            notifyDetails.put("clientIp", ServletUtil.getClientIP(request));
            TraceUtils.recordBusinessEvent("PAYMENT_NOTIFY_RECEIVED", notifyDetails);
            
            ApiResponse<String> result = paymentService.handlePaymentCallback(provider, callbackData);
            
            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("provider", provider);
                successDetails.put("result", result.getData());
                TraceUtils.recordBusinessEvent("PAYMENT_NOTIFY_SUCCESS", successDetails);
                return result.getData();
            } else {
                Map<String, Object> failDetails = new HashMap<>();
                failDetails.put("provider", provider);
                failDetails.put("errorCode", result.getCode());
                failDetails.put("errorMessage", result.getMessage());
                TraceUtils.recordError("PAYMENT_NOTIFY_FAILED", failDetails);
                return "fail";
            }
            
        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("provider", provider);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("PAYMENT_NOTIFY_EXCEPTION", errorDetails);
            log.error("处理{}支付回调异常", provider, e);
            return "fail";
        }
    }

    /**
     * 支付成功页面跳转
     */
    @GetMapping("/success")
    public ApiResponse<PaymentCallbackDTO> paymentSuccess(
            @RequestParam(required = false) String trade_order_id,
            @RequestParam(required = false) String payment_no) {
        
        Map<String, Object> pageDetails = new HashMap<>();
        pageDetails.put("tradeOrderId", trade_order_id);
        pageDetails.put("paymentNo", payment_no);
        TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PAGE", pageDetails);
        
        PaymentCallbackDTO result = new PaymentCallbackDTO()
            .setStatus("success")
            .setMessage("支付成功")
            .setTradeOrderId(trade_order_id)
            .setPaymentNo(payment_no)
            .setTimestamp(System.currentTimeMillis());
        
        return ApiResponse.success(result, "支付成功")
            .withTraceId(TraceContext.getTraceId());
    }

    /**
     * 支付取消页面跳转
     */
    @GetMapping("/cancel")
    public ApiResponse<PaymentCallbackDTO> paymentCancel(
            @RequestParam(required = false) String trade_order_id,
            @RequestParam(required = false) String payment_no) {
        
        Map<String, Object> pageDetails = new HashMap<>();
        pageDetails.put("tradeOrderId", trade_order_id);
        pageDetails.put("paymentNo", payment_no);
        TraceUtils.recordBusinessEvent("PAYMENT_CANCEL_PAGE", pageDetails);
        
        PaymentCallbackDTO result = new PaymentCallbackDTO()
            .setStatus("cancel")
            .setMessage("支付已取消")
            .setTradeOrderId(trade_order_id)
            .setPaymentNo(payment_no)
            .setTimestamp(System.currentTimeMillis());
        
        return ApiResponse.success(result, "支付已取消")
            .withTraceId(TraceContext.getTraceId());
    }

    // ================== 私有方法 ==================

    /**
     * 获取回调数据
     */
    private String getCallbackData(HttpServletRequest request) throws Exception {
        String contentType = request.getContentType();
        
        if (contentType != null && contentType.contains("application/json")) {
            // JSON格式
            StringBuilder sb = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
            }
            return sb.toString();
        } else {
            // 表单格式
            StringBuilder sb = new StringBuilder();
            Enumeration<String> paramNames = request.getParameterNames();
            
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String paramValue = request.getParameter(paramName);
                
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(paramName).append("=").append(paramValue != null ? paramValue : "");
            }
            
            return sb.toString();
        }
    }
} 