package com.jiashu.labiai.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.request.DeviceManageRequest;
import com.jiashu.labiai.dto.response.DeviceInfoResponse;
import com.jiashu.labiai.dto.response.DeviceRiskResponse;
import com.jiashu.labiai.dto.response.SecurityEventResponse;
import com.jiashu.labiai.service.ISecurityEventsService;
import com.jiashu.labiai.service.IUserDevicesService;
import com.jiashu.labiai.trace.TraceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/device")
@RequiredArgsConstructor
@Validated
public class DeviceManageController {

    private final IUserDevicesService userDevicesService;
    private final ISecurityEventsService securityEventsService;

    /**
     * 获取用户所有设备列表
     */
    @GetMapping("/list")
    public ApiResponse<List<DeviceInfoResponse>> getUserDevices() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<DeviceInfoResponse> devices = userDevicesService.getUserDevices(userId);

        // 标记当前设备
        String currentDeviceId = TraceContext.getVisitorId();
        if (currentDeviceId != null) {
            devices.forEach(device -> {
                if (currentDeviceId.equals(device.getDeviceId())) {
                    device.setIsCurrent(true);
                }
            });
        }

        return ApiResponse.success(devices, "获取设备列表成功");
    }

    /**
     * 获取指定设备详细信息
     */
    @GetMapping("/{deviceId}")
    public ApiResponse<DeviceInfoResponse> getDeviceInfo(@PathVariable String deviceId) {
        Long userId = StpUtil.getLoginIdAsLong();
        DeviceInfoResponse device = userDevicesService.getDeviceInfo(userId, deviceId);

        if (device == null) {
            return ApiResponse.error(404, "设备不存在");
        }

        // 标记是否为当前设备
        String currentDeviceId = TraceContext.getVisitorId();
        device.setIsCurrent(deviceId.equals(currentDeviceId));

        return ApiResponse.success(device, "获取设备信息成功");
    }

    /**
     * 设备风险评估
     */
    @GetMapping("/{deviceId}/risk")
    public ApiResponse<DeviceRiskResponse> assessDeviceRisk(@PathVariable String deviceId,
                                                            HttpServletRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        String clientIP = TraceContext.getClientIPFromMDC();

        DeviceRiskResponse riskResponse = userDevicesService.assessDeviceRisk(userId, deviceId, clientIP);

        return ApiResponse.success(riskResponse, "设备风险评估完成");
    }

    /**
     * 管理设备（信任、锁定、删除等）
     */
    @PostMapping("/manage")
    public ApiResponse<Void> manageDevice(@Valid @RequestBody DeviceManageRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 检查是否为当前设备的敏感操作
        String currentDeviceId = TraceContext.getVisitorId();
        if (request.getDeviceId().equals(currentDeviceId) &&
                (request.getAction() == DeviceManageRequest.DeviceManageAction.LOCK ||
                        request.getAction() == DeviceManageRequest.DeviceManageAction.DELETE)) {
            return ApiResponse.error(400, "不能对当前设备执行锁定或删除操作");
        }

        boolean success = userDevicesService.manageDevice(userId, request);

        if (success) {
            return ApiResponse.success(null, "设备管理操作成功");
        } else {
            return ApiResponse.error(500, "设备管理操作失败");
        }
    }

    /**
     * 设置设备信任状态
     */
    @PostMapping("/{deviceId}/trust")
    public ApiResponse<Void> updateDeviceTrust(@PathVariable String deviceId,
                                               @RequestParam boolean trusted,
                                               @RequestParam(required = false) String reason) {
        Long userId = StpUtil.getLoginIdAsLong();

        boolean success = userDevicesService.updateDeviceTrust(userId, deviceId, trusted, reason);

        if (success) {
            return ApiResponse.success(null, trusted ? "设备已设为信任" : "设备信任已取消");
        } else {
            return ApiResponse.error(500, "设备信任状态更新失败");
        }
    }

    /**
     * 删除设备
     */
    @DeleteMapping("/{deviceId}")
    public ApiResponse<Void> removeDevice(@PathVariable String deviceId) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 检查是否为当前设备
        String currentDeviceId = TraceContext.getVisitorId();
        if (deviceId.equals(currentDeviceId)) {
            return ApiResponse.error(400, "不能删除当前正在使用的设备");
        }

        boolean success = userDevicesService.removeDevice(userId, deviceId);

        if (success) {
            return ApiResponse.success(null, "设备删除成功");
        } else {
            return ApiResponse.error(500, "设备删除失败");
        }
    }

    /**
     * 获取设备相关安全事件
     */
    @GetMapping("/{deviceId}/security-events")
    public ApiResponse<List<SecurityEventResponse>> getDeviceSecurityEvents(
            @PathVariable String deviceId,
            @RequestParam(defaultValue = "20") Integer limit) {
        Long userId = StpUtil.getLoginIdAsLong();

        List<SecurityEventResponse> events = securityEventsService.getDeviceSecurityEvents(
                userId, deviceId, limit);

        return ApiResponse.success(events, "获取设备安全事件成功");
    }

    /**
     * 注册当前设备（手动）
     */
    @PostMapping("/register-current")
    public ApiResponse<DeviceInfoResponse> registerCurrentDevice(HttpServletRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        String deviceId = TraceContext.getVisitorId();
        String deviceHash = TraceContext.getDeviceHash();

        if (deviceId == null) {
            return ApiResponse.error(400, "无法获取设备ID");
        }

        try {
            userDevicesService.registerOrUpdateDevice(userId, deviceId, deviceHash, request);
            DeviceInfoResponse device = userDevicesService.getDeviceInfo(userId, deviceId);
            device.setIsCurrent(true);

            return ApiResponse.success(device, "设备注册成功");
        } catch (Exception e) {
            log.error("设备注册失败: userId={}, deviceId={}", userId, deviceId, e);
            return ApiResponse.error(500, "设备注册失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备指纹信息
     */
    @GetMapping("/fingerprint")
    public ApiResponse<Object> getDeviceFingerprint() {
        String visitorId = TraceContext.getVisitorId();
        String deviceHash = TraceContext.getDeviceHash();
        String clientIP = TraceContext.getClientIPFromMDC();
        String userAgent = TraceContext.getUserAgent();

        Map<String, Object> map = new HashMap<>();
        map.put("visitorId", visitorId);
        map.put("deviceHash", deviceHash);
        map.put("clientIP", clientIP);
        map.put("userAgent", userAgent);

        return ApiResponse.success(map, "获取设备指纹信息成功");
    }

    /**
     * 检查设备异常
     */
    @GetMapping("/{deviceId}/check-anomaly")
    public ApiResponse<Object> checkDeviceAnomaly(@PathVariable String deviceId) {
        Long userId = StpUtil.getLoginIdAsLong();
        String deviceHash = TraceContext.getDeviceHash();
        String clientIP = TraceContext.getClientIPFromMDC();

        String anomaly = userDevicesService.checkDeviceAnomaly(userId, deviceId, deviceHash, clientIP);
        Map<String, Object> map = new HashMap<>();
        map.put("hasAnomaly", anomaly != null);
        map.put("anomalyDescription", anomaly != null ? anomaly : "");
        map.put("checkTime", java.time.LocalDateTime.now());

        return ApiResponse.success(map, "设备异常检查完成");
    }

    /**
     * 批量管理设备
     */
    @PostMapping("/batch-manage")
    public ApiResponse<Object> batchManageDevices(@RequestBody List<DeviceManageRequest> requests) {
        Long userId = StpUtil.getLoginIdAsLong();
        String currentDeviceId = TraceContext.getVisitorId();

        int successCount = 0;
        int failureCount = 0;

        for (DeviceManageRequest request : requests) {
            // 检查是否为当前设备的敏感操作
            if (request.getDeviceId().equals(currentDeviceId) &&
                    (request.getAction() == DeviceManageRequest.DeviceManageAction.LOCK ||
                            request.getAction() == DeviceManageRequest.DeviceManageAction.DELETE)) {
                failureCount++;
                continue;
            }

            try {
                boolean success = userDevicesService.manageDevice(userId, request);
                if (success) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("批量设备管理失败: deviceId={}, action={}",
                        request.getDeviceId(), request.getAction(), e);
                failureCount++;
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("successCount", successCount);
        map.put("failureCount", failureCount);
        map.put("total", requests.size());

        return ApiResponse.success(
                map,
                String.format("批量操作完成，成功%d个，失败%d个", successCount, failureCount)
        );
    }
} 