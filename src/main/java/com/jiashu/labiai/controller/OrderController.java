package com.jiashu.labiai.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.exception.AuthorizationException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.IOrderService;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/orders")
@RequiredArgsConstructor
@Slf4j
public class OrderController {

    private final IOrderService orderService;
//    private final IDiscountCodeService discountCodeService;

    /**
     * 预验证优惠码（创建订单前调用）
     */
    @PostMapping("/preview-discount")
    @SaCheckLogin
    public ApiResponse<DiscountCodeValidationDTO> previewDiscountCode(
            @RequestParam String discountCode,
            @RequestParam Long packagePriceId) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("discountCode", discountCode);
        requestDetails.put("packagePriceId", packagePriceId);
        TraceUtils.recordBusinessEvent("ORDER_PREVIEW_DISCOUNT_API_REQUEST", requestDetails);

        // 参数验证
        if (StrUtil.isBlank(discountCode)) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("discountCode", discountCode);
            TraceUtils.recordError("MISSING_DISCOUNT_CODE", errorDetails);
            throw ValidationException.missingParameter("discountCode");
        }

        if (packagePriceId == null || packagePriceId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("packagePriceId", packagePriceId);
            TraceUtils.recordError("INVALID_PACKAGE_PRICE_ID", errorDetails);
            throw ValidationException.invalidParameter("packagePriceId", packagePriceId);
        }

        try {
            // 获取套餐价格信息来验证优惠码
            ApiResponse<DiscountCodeValidationDTO> result = orderService.previewOrderWithDiscount(
                    userId, packagePriceId, discountCode);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("discountCode", discountCode);
                successDetails.put("discountAmount", result.getData().getDiscountAmount());
                TraceUtils.recordBusinessEvent("ORDER_PREVIEW_DISCOUNT_API_SUCCESS", successDetails);
            } else {
                Map<String, Object> failDetails = new HashMap<>();
                failDetails.put("userId", userId);
                failDetails.put("discountCode", discountCode);
                failDetails.put("errorCode", result.getCode());
                TraceUtils.recordError("ORDER_PREVIEW_DISCOUNT_API_FAILED", failDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("discountCode", discountCode);
            errorDetails.put("packagePriceId", packagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_PREVIEW_DISCOUNT_EXCEPTION", errorDetails);
            log.error("预览优惠码失败", e);
            throw e;
        }
    }

    /**
     * 创建订单
     */
    @PostMapping("/create")
    @SaCheckLogin
    public ApiResponse<OrderDTO> createOrder(
            @RequestParam Long packagePriceId,
            @RequestParam(required = false) String discountCode) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("packagePriceId", packagePriceId);
        requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
        if (StrUtil.isNotBlank(discountCode)) {
            requestDetails.put("discountCode", discountCode);
        }
        TraceUtils.recordBusinessEvent("ORDER_CREATE_API_REQUEST", requestDetails);

        // 参数验证
        if (packagePriceId == null || packagePriceId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("packagePriceId", packagePriceId);
            TraceUtils.recordError("INVALID_PACKAGE_PRICE_ID", errorDetails);
            throw ValidationException.invalidParameter("packagePriceId", packagePriceId);
        }

        // 如果有优惠码，验证格式
        if (StrUtil.isNotBlank(discountCode)) {
            discountCode = discountCode.trim().toUpperCase();
            if (discountCode.length() < 3 || discountCode.length() > 50) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCode", discountCode);
                errorDetails.put("length", discountCode.length());
                TraceUtils.recordError("INVALID_DISCOUNT_CODE_FORMAT", errorDetails);
                throw ValidationException.invalidParameter("discountCode", "优惠码长度必须在3-50个字符之间");
            }
        }

        try {
            ApiResponse<OrderDTO> result = orderService.createOrder(userId, packagePriceId, discountCode);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("packagePriceId", packagePriceId);
                // 不记录orderId，使用orderNo作为标识
                successDetails.put("orderNo", result.getData().getOrderNo());
                successDetails.put("finalAmount", result.getData().getFinalAmount());
                if (StrUtil.isNotBlank(discountCode)) {
                    successDetails.put("discountCode", discountCode);
                    successDetails.put("discountAmount", result.getData().getDiscountAmount());
                }
                TraceUtils.recordBusinessEvent("ORDER_CREATE_API_SUCCESS", successDetails);
            } else {
                Map<String, Object> failDetails = new HashMap<>();
                failDetails.put("userId", userId);
                failDetails.put("packagePriceId", packagePriceId);
                failDetails.put("errorCode", result.getCode());
                failDetails.put("errorMessage", result.getMessage());
                if (StrUtil.isNotBlank(discountCode)) {
                    failDetails.put("discountCode", discountCode);
                }
                TraceUtils.recordError("ORDER_CREATE_API_FAILED", failDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("packagePriceId", packagePriceId);
            if (StrUtil.isNotBlank(discountCode)) {
                errorDetails.put("discountCode", discountCode);
            }
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_CREATE_EXCEPTION", errorDetails);
            log.error("创建订单失败", e);
            throw e;
        }
    }

    /**
     * 根据订单号查询订单详情
     */
    @GetMapping("/order-no/{orderNo}")
    @SaCheckLogin
    public ApiResponse<OrderDTO> getOrderByOrderNo(@PathVariable String orderNo) {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("orderNo", orderNo);
        TraceUtils.recordBusinessEvent("ORDER_QUERY_BY_NO_API_REQUEST", requestDetails);

        if (StrUtil.isBlank(orderNo)) {
            throw ValidationException.invalidParameter("orderNo", orderNo);
        }

        ApiResponse<OrderDTO> result = orderService.getOrderByOrderNo(orderNo);

        // 检查订单所有权 - 通过orderNo查询的订单已经包含了用户验证
        // 因为getOrderByOrderNo内部会验证订单属于当前用户
        // 所以这里不需要额外的所有权检查

        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 根据订单ID查询订单详情
     */
    @GetMapping("/{orderId}")
    @SaCheckLogin
    public ApiResponse<OrderDTO> getOrderById(@PathVariable Long orderId) {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("orderId", orderId);
        TraceUtils.recordBusinessEvent("ORDER_QUERY_BY_ID_API_REQUEST", requestDetails);

        if (orderId == null || orderId <= 0) {
            throw ValidationException.invalidParameter("orderId", orderId);
        }

        // 检查订单所有权
        if (!orderService.checkOrderOwnership(orderId, userId)) {
            throw new AuthorizationException("无权查看此订单");
        }

        return orderService.getOrderById(orderId).withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取当前用户的订单列表
     */
    @GetMapping
    @SaCheckLogin
    public ApiResponse<List<OrderDTO>> getUserOrders(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("status", status);
        requestDetails.put("page", page);
        requestDetails.put("size", size);
        TraceUtils.recordBusinessEvent("USER_ORDERS_LIST_API_REQUEST", requestDetails);

        // 参数验证
        if (page != null && page < 1) {
            throw ValidationException.invalidParameter("page", page);
        }
        if (size != null && (size < 1 || size > 100)) {
            throw ValidationException.invalidParameter("size", size);
        }

        return orderService.getUserOrders(userId, status, page, size)
                .withTraceId(TraceContext.getTraceId());
    }

    /**
     * 取消订单
     */
    @PutMapping("/{orderId}/cancel")
    @SaCheckLogin
    public ApiResponse<Void> cancelOrder(@PathVariable Long orderId) {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("orderId", orderId);
        TraceUtils.recordBusinessEvent("ORDER_CANCEL_API_REQUEST", requestDetails);

        if (orderId == null || orderId <= 0) {
            throw ValidationException.invalidParameter("orderId", orderId);
        }

        try {
            ApiResponse<Void> result = orderService.cancelOrder(orderId, userId);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("orderId", orderId);
                TraceUtils.recordBusinessEvent("ORDER_CANCEL_API_SUCCESS", successDetails);
            } else {
                Map<String, Object> failDetails = new HashMap<>();
                failDetails.put("userId", userId);
                failDetails.put("orderId", orderId);
                failDetails.put("errorCode", result.getCode());
                TraceUtils.recordError("ORDER_CANCEL_API_FAILED", failDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("orderId", orderId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_CANCEL_EXCEPTION", errorDetails);
            log.error("取消订单失败", e);
            throw e;
        }
    }

    /**
     * 删除订单（逻辑删除）
     */
    @DeleteMapping("/{orderId}")
    @SaCheckLogin
    public ApiResponse<Void> deleteOrder(@PathVariable Long orderId) {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("orderId", orderId);
        TraceUtils.recordBusinessEvent("ORDER_DELETE_API_REQUEST", requestDetails);

        if (orderId == null || orderId <= 0) {
            throw ValidationException.invalidParameter("orderId", orderId);
        }

        try {
            ApiResponse<Void> result = orderService.deleteOrder(orderId, userId);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("orderId", orderId);
                TraceUtils.recordBusinessEvent("ORDER_DELETE_API_SUCCESS", successDetails);
            } else {
                Map<String, Object> failDetails = new HashMap<>();
                failDetails.put("userId", userId);
                failDetails.put("orderId", orderId);
                failDetails.put("errorCode", result.getCode());
                TraceUtils.recordError("ORDER_DELETE_API_FAILED", failDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("orderId", orderId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_DELETE_EXCEPTION", errorDetails);
            log.error("删除订单失败", e);
            throw e;
        }
    }

    /**
     * 获取订单优惠码使用详情
     */
    @GetMapping("/{orderId}/discount-info")
    @SaCheckLogin
    public ApiResponse<Map<String, Object>> getOrderDiscountInfo(@PathVariable Long orderId) {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("orderId", orderId);
        TraceUtils.recordBusinessEvent("ORDER_DISCOUNT_INFO_API_REQUEST", requestDetails);

        if (orderId == null || orderId <= 0) {
            throw ValidationException.invalidParameter("orderId", orderId);
        }

        // 检查订单所有权
        if (!orderService.checkOrderOwnership(orderId, userId)) {
            throw new AuthorizationException("无权查看此订单");
        }

        try {
            ApiResponse<Map<String, Object>> result = orderService.getOrderDiscountInfo(orderId);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("orderId", orderId);
                TraceUtils.recordBusinessEvent("ORDER_DISCOUNT_INFO_API_SUCCESS", successDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("orderId", orderId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_DISCOUNT_INFO_EXCEPTION", errorDetails);
            log.error("获取订单优惠码信息失败", e);
            throw e;
        }
    }

    /**
     * 预览续费价格
     */
    @PostMapping("/preview-renewal")
    @SaCheckLogin
    public ApiResponse<RenewalPreviewDTO> previewRenewal(
            @RequestParam Long packagePriceId,
            @RequestParam(required = false) String discountCode) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("packagePriceId", packagePriceId);
        requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
        TraceUtils.recordBusinessEvent("ORDER_PREVIEW_RENEWAL_API_REQUEST", requestDetails);

        if (packagePriceId == null || packagePriceId <= 0) {
            throw ValidationException.invalidParameter("packagePriceId", packagePriceId);
        }

        try {
            ApiResponse<RenewalPreviewDTO> result = orderService.previewRenewal(userId, packagePriceId, discountCode);
            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("packagePriceId", packagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_PREVIEW_RENEWAL_EXCEPTION", errorDetails);
            log.error("预览续费价格失败", e);
            throw e;
        }
    }

    /**
     * 创建续费订单
     */
    @PostMapping("/create-renewal")
    @SaCheckLogin
    public ApiResponse<OrderDTO> createRenewalOrder(
            @RequestParam Long packagePriceId,
            @RequestParam(required = false) String discountCode) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("packagePriceId", packagePriceId);
        requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
        TraceUtils.recordBusinessEvent("ORDER_CREATE_RENEWAL_API_REQUEST", requestDetails);

        if (packagePriceId == null || packagePriceId <= 0) {
            throw ValidationException.invalidParameter("packagePriceId", packagePriceId);
        }

        try {
            ApiResponse<OrderDTO> result = orderService.createRenewalOrder(userId, packagePriceId, discountCode);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("packagePriceId", packagePriceId);
                TraceUtils.recordBusinessEvent("ORDER_CREATE_RENEWAL_API_SUCCESS", successDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("packagePriceId", packagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_CREATE_RENEWAL_EXCEPTION", errorDetails);
            log.error("创建续费订单失败", e);
            throw e;
        }
    }

    /**
     * 预览升级价格
     */
    @PostMapping("/preview-upgrade")
    @SaCheckLogin
    public ApiResponse<UpgradePreviewDTO> previewUpgrade(
            @RequestParam Long targetPackagePriceId,
            @RequestParam(required = false) String discountCode) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("targetPackagePriceId", targetPackagePriceId);
        requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
        TraceUtils.recordBusinessEvent("ORDER_PREVIEW_UPGRADE_API_REQUEST", requestDetails);

        if (targetPackagePriceId == null || targetPackagePriceId <= 0) {
            throw ValidationException.invalidParameter("targetPackagePriceId", targetPackagePriceId);
        }

        try {
            ApiResponse<UpgradePreviewDTO> result = orderService.previewUpgrade(userId, targetPackagePriceId, discountCode);
            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("targetPackagePriceId", targetPackagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_PREVIEW_UPGRADE_EXCEPTION", errorDetails);
            log.error("预览升级价格失败", e);
            throw e;
        }
    }

    /**
     * 创建升级订单
     */
    @PostMapping("/create-upgrade")
    @SaCheckLogin
    public ApiResponse<OrderDTO> createUpgradeOrder(
            @RequestParam Long targetPackagePriceId,
            @RequestParam(required = false) String discountCode) {

        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("targetPackagePriceId", targetPackagePriceId);
        requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
        TraceUtils.recordBusinessEvent("ORDER_CREATE_UPGRADE_API_REQUEST", requestDetails);

        if (targetPackagePriceId == null || targetPackagePriceId <= 0) {
            throw ValidationException.invalidParameter("targetPackagePriceId", targetPackagePriceId);
        }

        try {
            ApiResponse<OrderDTO> result = orderService.createUpgradeOrder(userId, targetPackagePriceId, discountCode);

            if (result.getSuccess()) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("userId", userId);
                successDetails.put("targetPackagePriceId", targetPackagePriceId);
                TraceUtils.recordBusinessEvent("ORDER_CREATE_UPGRADE_API_SUCCESS", successDetails);
            }

            return result.withTraceId(TraceContext.getTraceId());

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("targetPackagePriceId", targetPackagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_CREATE_UPGRADE_EXCEPTION", errorDetails);
            log.error("创建升级订单失败", e);
            throw e;
        }
    }

}