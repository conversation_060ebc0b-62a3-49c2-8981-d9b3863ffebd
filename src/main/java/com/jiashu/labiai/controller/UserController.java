package com.jiashu.labiai.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.request.user.ChangePasswordRequest;
import com.jiashu.labiai.dto.response.user.ChangePasswordResponse;
import com.jiashu.labiai.dto.response.user.UserInfoResponse;
import com.jiashu.labiai.dto.response.user.UserSubscriptionInfoResponse;
import com.jiashu.labiai.entity.Users;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.enums.UserStatus;
import com.jiashu.labiai.exception.AuthenticationException;
import com.jiashu.labiai.exception.BusinessException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.EnhancedAuthService;
import com.jiashu.labiai.service.IUserSubscriptionService;
import com.jiashu.labiai.service.IUsersService;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户管理控制器
 * 处理用户相关的操作，如修改密码、个人信息等
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@Slf4j
@Validated
@RequiredArgsConstructor
public class UserController {

    private final EnhancedAuthService enhancedAuthService;
    private final IUsersService usersService;
    private final IUserSubscriptionService userSubscriptionService;

    /**
     * 获取当前用户信息（通过cookie验证）
     */
    @GetMapping("/me")
    public ApiResponse<UserInfoResponse> getCurrentUserInfo() {
        try {
            // 通过cookie检查登录状态
            StpUtil.checkLogin();
            Long currentUserId = StpUtil.getLoginIdAsLong();

            // 获取用户信息
            Users user = usersService.getById(currentUserId);
            if (user == null) {
                throw new AuthenticationException("用户不存在");
            }

            // 构建用户信息响应
            UserInfoResponse userInfo = buildUserInfoResponse(user);

            return ApiResponse.success(userInfo, "获取用户信息成功");

        } catch (AuthenticationException e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取用户信息过程发生异常", e);
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "获取用户信息失败");
        }
    }

    /**
     * 获取用户订阅信息（包含当前订阅和历史记录）
     */
    @GetMapping("/subscription")
    public ApiResponse<UserSubscriptionInfoResponse> getUserSubscriptionInfo() {
        try {
            // 验证用户是否已登录
            StpUtil.checkLogin();
            Long currentUserId = StpUtil.getLoginIdAsLong();
            
            // 获取用户订阅信息
            UserSubscriptionInfoResponse subscriptionInfo = userSubscriptionService.getUserSubscriptionInfo(currentUserId);
            
            TraceUtils.recordBusinessEvent("get_user_subscription_info", subscriptionInfo);
            return ApiResponse.success(subscriptionInfo, "获取订阅信息成功");
            
        } catch (Exception e) {
            log.error("获取用户订阅信息失败，userId: {}", StpUtil.getLoginIdAsLong(), e);
            TraceUtils.recordBusinessEvent("get_user_subscription_info_failed", e.getMessage());
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "获取订阅信息失败：" + e.getMessage());
        }
    }

    /**
     * 修改密码（必须登录）
     */
    @PostMapping("/change-password")
    public ApiResponse<ChangePasswordResponse> changePassword(
            @Valid @RequestBody ChangePasswordRequest request) {

        try {
            // 验证用户是否已登录
            StpUtil.checkLogin();
            Long currentUserId = StpUtil.getLoginIdAsLong();

            // 获取当前登录用户信息
            Users currentUser = usersService.getById(currentUserId);
            if (currentUser == null) {
                throw new AuthenticationException("用户不存在");
            }

            if (currentUser.getStatus() == UserStatus.LOCKED) {
                throw new AuthenticationException("账号已被锁定");
            }

            if (currentUser.getStatus() == UserStatus.DISABLED) {
                throw new AuthenticationException("账号已被禁用");
            }

            if (currentUser.getStatus() == UserStatus.DELETED) {
                throw new AuthenticationException("账号已注销");
            }

            // 执行密码修改（会自动下线其他客户端）
            ChangePasswordResponse response = enhancedAuthService.changePassword(request, currentUser);

            TraceUtils.recordBusinessEvent("password_changed_success", response);
            return ApiResponse.success(response, "密码修改成功，已下线其他客户端，请重新登录");
        } catch (AuthenticationException | ValidationException e) {
            TraceUtils.recordBusinessEvent("password_changed_failed", e.getMessage());
            throw e;
        } catch (Exception e) {
            TraceUtils.recordBusinessEvent("password_changed_failed", e.getMessage());
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "密码修改失败，请稍后重试");
        }
    }

    /**
     * 构建用户信息响应
     */
    private UserInfoResponse buildUserInfoResponse(Users user) {
        return UserInfoResponse.builder()
                .email(user.getEmail())
                .nickname(user.getNickname())
                .build();
    }

    /**
     * 获取用户类型描述
     */
    private String getUserTypeDescription(com.jiashu.labiai.enums.UserType userType) {
        if (userType == null) return "未知";
        switch (userType) {
            case NORMAL:
                return "普通用户";
            case THIRD_PARTY:
                return "第三方用户";
            case HYBRID:
                return "混合账号";
            default:
                return "未知";
        }
    }

    /**
     * 获取用户状态描述
     */
    private String getUserStatusDescription(com.jiashu.labiai.enums.UserStatus status) {
        if (status == null) return "未知";
        switch (status) {
            case NORMAL:
                return "正常";
            case LOCKED:
                return "锁定";
            case DISABLED:
                return "禁用";
            case DELETED:
                return "已注销";
            default:
                return "未知";
        }
    }

    /**
     * 获取安全等级描述
     */
    private String getSecurityLevelDescription(com.jiashu.labiai.enums.SecurityLevel securityLevel) {
        if (securityLevel == null) return "未知";
        switch (securityLevel) {
            case NORMAL:
                return "普通";
            case SENSITIVE:
                return "敏感";
            case HIGH_RISK:
                return "高危";
            default:
                return "未知";
        }
    }
} 