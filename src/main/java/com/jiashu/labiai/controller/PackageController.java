package com.jiashu.labiai.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.IPackageService;

import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 套餐控制器
 */
@RestController
@RequestMapping("/packages")
@RequiredArgsConstructor
@Slf4j
public class PackageController {

    private final IPackageService packageService;

    /**
     * 获取所有可用套餐及价格
     */
    @GetMapping
    public ApiResponse<List<PackageDTO>> getPackages() {
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("action", "list");
        TraceUtils.recordBusinessEvent("PACKAGES_LIST_API_REQUEST", requestDetails);
        
        ApiResponse<List<PackageDTO>> result = packageService.getAvailablePackages();
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取套餐详情
     */
    @GetMapping("/{id}")
    public ApiResponse<PackageDTO> getPackageDetail(@PathVariable Long id) {
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("packageId", id);
        TraceUtils.recordBusinessEvent("PACKAGE_DETAIL_API_REQUEST", requestDetails);
        
        if (id == null || id <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("packageId", id);
            TraceUtils.recordError("INVALID_PACKAGE_ID", errorDetails);
            throw ValidationException.invalidParameter("id", id);
//            return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "套餐ID无效");
        }
        
        ApiResponse<PackageDTO> result = packageService.getPackageDetail(id);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取套餐价格选项
     */
    @GetMapping("/{id}/prices")
    public ApiResponse<List<PackagePriceDTO>> getPackagePrices(@PathVariable Long id) {
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("packageId", id);
        TraceUtils.recordBusinessEvent("PACKAGE_PRICES_API_REQUEST", requestDetails);
        
        if (id == null || id <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("packageId", id);
            TraceUtils.recordError("INVALID_PACKAGE_ID", errorDetails);
            throw ValidationException.invalidParameter("id", id);
//            return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "套餐ID无效");
        }
        
        ApiResponse<List<PackagePriceDTO>> result = packageService.getPackagePrices(id);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取套餐价格详情
     */
    @GetMapping("/prices/{priceId}")
    public ApiResponse<PackagePriceDTO> getPackagePriceDetail(@PathVariable Long priceId) {
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("priceId", priceId);
        TraceUtils.recordBusinessEvent("PACKAGE_PRICE_DETAIL_API_REQUEST", requestDetails);

        if (priceId == null || priceId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("priceId", priceId);
            TraceUtils.recordError("INVALID_PRICE_ID", errorDetails);
           throw  ValidationException.invalidParameter("priceId", priceId);
//            return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "价格ID无效");
        }

        ApiResponse<PackagePriceDTO> result = packageService.getPackagePriceDetail(priceId);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取续费选项
     */
    @GetMapping("/{packageId}/renewal-options")
    @SaCheckLogin
    public ApiResponse<RenewalOptionsDTO> getRenewalOptions(@PathVariable Long packageId) {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("packageId", packageId);
        TraceUtils.recordBusinessEvent("PACKAGE_RENEWAL_OPTIONS_API_REQUEST", requestDetails);

        if (packageId == null || packageId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("packageId", packageId);
            TraceUtils.recordError("INVALID_PACKAGE_ID", errorDetails);
            throw ValidationException.invalidParameter("packageId", packageId);
        }

        ApiResponse<RenewalOptionsDTO> result = packageService.getRenewalOptions(userId, packageId);
        return result.withTraceId(TraceContext.getTraceId());
    }

    /**
     * 获取升级选项
     */
    @GetMapping("/upgrade-options")
    @SaCheckLogin
    public ApiResponse<UpgradeOptionsDTO> getUpgradeOptions() {
        Long userId = StpUtil.getLoginIdAsLong();

        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        TraceUtils.recordBusinessEvent("PACKAGE_UPGRADE_OPTIONS_API_REQUEST", requestDetails);

        ApiResponse<UpgradeOptionsDTO> result = packageService.getUpgradeOptions(userId);
        return result.withTraceId(TraceContext.getTraceId());
    }

}