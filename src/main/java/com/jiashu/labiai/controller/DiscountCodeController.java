package com.jiashu.labiai.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.DiscountCodeValidationDTO;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.IDiscountCodeService;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 优惠码控制器
 */
@RestController
@RequestMapping("/discount-codes")
@RequiredArgsConstructor
@Slf4j
public class DiscountCodeController {

    private final IDiscountCodeService discountCodeService;

    /**
     * 验证优惠码
     */
    @PostMapping("/validate")
    @SaCheckLogin
    public ApiResponse<DiscountCodeValidationDTO> validateDiscountCode(
            @RequestParam String discountCode,
            @RequestParam Long packageId,
            @RequestParam BigDecimal amount) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        Map<String, Object> requestDetails = new HashMap<>();
        requestDetails.put("userId", userId);
        requestDetails.put("discountCode", discountCode);
        requestDetails.put("packageId", packageId);
        requestDetails.put("amount", amount);
        TraceUtils.recordBusinessEvent("DISCOUNT_CODE_VALIDATE_API_REQUEST", requestDetails);
        
        // 参数验证
        if (StrUtil.isBlank(discountCode)) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("discountCode", discountCode);
            TraceUtils.recordError("MISSING_DISCOUNT_CODE", errorDetails);
            throw ValidationException.missingParameter("discountCode");
        }
        
        if (packageId == null || packageId <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("packageId", packageId);
            TraceUtils.recordError("INVALID_PACKAGE_ID", errorDetails);
            throw ValidationException.invalidParameter("packageId", packageId);
        }
        
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("amount", amount);
            TraceUtils.recordError("INVALID_AMOUNT", errorDetails);
            throw ValidationException.invalidParameter("amount", amount);
        }
        
        ApiResponse<DiscountCodeValidationDTO> result = discountCodeService.validateDiscountCode(
            discountCode, packageId, amount, userId);
        
        if (result.getSuccess()) {
            Map<String, Object> successDetails = new HashMap<>();
            successDetails.put("userId", userId);
            successDetails.put("discountCode", discountCode);
            TraceUtils.recordBusinessEvent("DISCOUNT_CODE_VALIDATE_API_SUCCESS", successDetails);
        } else {
            Map<String, Object> failDetails = new HashMap<>();
            failDetails.put("userId", userId);
            failDetails.put("discountCode", discountCode);
            failDetails.put("errorCode", result.getCode());
            failDetails.put("errorMessage", result.getMessage());
            TraceUtils.recordError("DISCOUNT_CODE_VALIDATE_API_FAILED", failDetails);
        }
        
        return result;
    }
} 