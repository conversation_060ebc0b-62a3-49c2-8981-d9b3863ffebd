package com.jiashu.labiai.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.request.auth.SendVerificationCodeRequest;
import com.jiashu.labiai.dto.request.auth.UserRegisterRequest;
import com.jiashu.labiai.dto.response.auth.SendVerificationCodeResponse;
import com.jiashu.labiai.dto.response.auth.SessionInitResponse;
import com.jiashu.labiai.dto.response.auth.UserRegisterResponse;
import com.jiashu.labiai.entity.Users;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.AuthenticationException;
import com.jiashu.labiai.exception.BusinessException;
import com.jiashu.labiai.exception.RateLimitException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.*;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * 用户注册控制器
 * 基于新的响应模板和错误码体系
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/register")
@RequiredArgsConstructor
@Validated
public class RegisterController {

    private final SessionService sessionService;
    private final MailGunService mailGunService;
    private final IUsersService usersService;
    private final UserRegisterService userRegisterService;
    private final IUserDevicesService userDevicesService;

    /**
     * 初始化会话
     */
    @PostMapping("/init-session")
    public ApiResponse<SessionInitResponse> initSession() {
        try {
            // 记录业务事件
            TraceUtils.recordBusinessEvent("session_init_start",
                    MapUtil.builder().put("deviceInfo", TraceContext.getDeviceSummary()).build());

            SessionInitResponse response = sessionService.initSession();

            // 记录成功事件
            TraceUtils.recordBusinessEvent("session_init_success",
                    MapUtil.builder().put("sessionToken", response.getSessionToken()).build());

            return ApiResponse.success(response, "会话初始化成功");

        } catch (Exception e) {
            // 记录错误信息
            TraceUtils.recordError("session_init_error", e.getMessage(), e);
            log.error("会话初始化失败", e);
            throw e;
        }
    }

    /**
     * 发送验证码
     */
    @PostMapping("/send-verification-code")
    public ApiResponse<SendVerificationCodeResponse> sendVerificationCode(
            @Valid @RequestBody SendVerificationCodeRequest request,
            HttpServletRequest httpRequest) {

        try {
            // 1. 从HTTP头获取会话令牌和nonce
            String authorization = httpRequest.getHeader("Authorization");
            String nonce = httpRequest.getHeader("X-Session-Nonce");

            if (!StringUtils.hasText(authorization) || !authorization.startsWith("Bearer ")) {
                throw AuthenticationException.tokenMissing();
            }

            String sessionToken = authorization.substring(7);

            if (!StringUtils.hasText(nonce)) {
                throw ValidationException.missingParameter("X-Session-Nonce header");
            }

            // 记录业务事件开始（包含设备信息）
            TraceUtils.recordBusinessEvent("send_code_start",
                    MapUtil.builder()
                            .put("email", request.getEmail())
                            .put("deviceInfo", TraceContext.getDeviceSummary())
                            .build());

            // 2. 参数验证
            if (StrUtil.isBlank(request.getEmail())) {
                throw ValidationException.missingParameter("email");
            }

            // 邮箱格式校验
            if (!request.getEmail().matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                throw ValidationException.invalidParameter("email", request.getEmail());
            }

            TraceUtils.recordBusinessEvent("parameter_validation",
                    MapUtil.builder()
                            .put("status", "success")
                            .put("email", request.getEmail())
                            .build());

            // 3. 检查邮箱是否已被注册
            boolean exists = userRegisterService.isEmailExists(request.getEmail());
            if (exists) {
                throw BusinessException.of(ResponseCode.EMAIL_ALREADY_EXISTS);
            }

            TraceUtils.recordBusinessEvent("email_check",
                    MapUtil.builder()
                            .put("email", request.getEmail())
                            .put("exists", false)
                            .build());

            // 4. 验证会话并生成新的nonce
            String newNonce = null;
            try {
                // 从MDC获取设备信息
                String deviceHash = TraceContext.getDeviceHash();
                String clientIP = TraceContext.getClientIPFromMDC();
                String userAgent = TraceContext.getUserAgent();

                // 验证会话
                newNonce = sessionService.validateSessionAndNonce(
                        sessionToken, nonce, deviceHash, clientIP, userAgent);

                TraceUtils.recordBusinessEvent("session_validation_success",
                        MapUtil.builder()
                                .put("hasNewNonce", StrUtil.isNotBlank(newNonce))
                                .build());
            } catch (Exception e) {
                TraceUtils.recordBusinessEvent("session_validation_failed",
                        MapUtil.builder()
                                .put("reason", e.getMessage())
                                .build());
                log.error("会话验证失败 - 原因: {}", e.getMessage());
                throw e; // 发送验证码时会话验证失败应该直接抛出异常
            }

            // 5. 发送验证码
            SendVerificationCodeResponse response;
            try {
                // 从MDC获取设备信息
                String visitorId = TraceContext.getVisitorId();

                // 发送验证码邮件（使用visitorId作为设备指纹）
                mailGunService.sendVerificationCodeWithFingerprint(request.getEmail(), visitorId);

                // 构建响应
                response = SendVerificationCodeResponse.builder()
                        .message("验证码发送成功")
                        .nonce(newNonce)
                        .nextAllowedAt(null) // 可以根据限流策略设置下次允许发送的时间
                        .remainingAttempts(null) // 可以根据业务需要设置剩余尝试次数
                        .build();

                // 记录成功事件
                TraceUtils.recordBusinessEvent("send_code_success",
                        MapUtil.builder()
                                .put("email", request.getEmail())
                                .put("visitorId", visitorId)
                                .put("newNonce", newNonce)
                                .build());

            } catch (Exception e) {
                TraceUtils.recordError("send_code_service_error", e.getMessage(), e);
                throw e;
            }

            return ApiResponse.success(response, "验证码发送成功");

        } catch (RateLimitException e) {
            TraceUtils.recordError("rate_limit_error", e.getMessage(), e);
            log.warn("验证码发送被限流 - 邮箱: {}", request.getEmail(), e);
            throw e; // 让全局异常处理器处理
        } catch (Exception e) {
            TraceUtils.recordError("send_code_error", e.getMessage(), e);
            log.error("发送验证码失败 - 邮箱: {}", request.getEmail(), e);
            throw e;
        }
    }

    /**
     * 用户注册（包含验证码验证）
     */
    @PostMapping("/submit")
    public ApiResponse<UserRegisterResponse> register(
            @Valid @RequestBody UserRegisterRequest request,
            HttpServletRequest httpRequest) {

        log.info("收到用户注册请求 - 邮箱: {}", request.getEmail());

        try {
            // 1. 从HTTP头获取会话令牌和nonce
            String authorization = httpRequest.getHeader("Authorization");
            String nonce = httpRequest.getHeader("X-Session-Nonce");

            if (!StringUtils.hasText(authorization) || !authorization.startsWith("Bearer ")) {
                throw AuthenticationException.tokenMissing();
            }

            String sessionToken = authorization.substring(7);

            if (!StringUtils.hasText(nonce)) {
                throw ValidationException.missingParameter("X-Session-Nonce header");
            }

            // 记录业务事件开始（包含设备信息）
            TraceUtils.recordBusinessEvent("register_start",
                    MapUtil.builder()
                            .put("email", request.getEmail())
                            .put("deviceInfo", TraceContext.getDeviceSummary())
                            .build());

            // 2. 参数验证
            if (StrUtil.isBlank(request.getEmail())) {
                throw ValidationException.missingParameter("email");
            }
            if (StrUtil.isBlank(request.getPassword())) {
                throw ValidationException.missingParameter("password");
            }
            if (StrUtil.isBlank(request.getConfirmPassword())) {
                throw ValidationException.missingParameter("confirmPassword");
            }
            if (StrUtil.isBlank(request.getCode())) {
                throw ValidationException.missingParameter("verificationCode");
            }

            // 密码确认校验
            if (!request.getPassword().equals(request.getConfirmPassword())) {
                throw new ValidationException(ResponseCode.PASSWORD_NOT_MATCH);
            }

            // 邮箱格式校验
            if (!request.getEmail().matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                throw ValidationException.invalidParameter("email", request.getEmail());
            }

            TraceUtils.recordBusinessEvent("register_validation",
                    MapUtil.builder()
                            .put("status", "success")
                            .put("email", request.getEmail())
                            .build());

            // 3. 检查邮箱是否已被注册
            boolean exists = usersService.lambdaQuery().eq(Users::getEmail, request.getEmail()).exists();
            if (exists) {
                throw BusinessException.of(ResponseCode.EMAIL_ALREADY_EXISTS);
            }

            TraceUtils.recordBusinessEvent("duplicate_check",
                    MapUtil.builder()
                            .put("email", request.getEmail())
                            .put("duplicate", false)
                            .build());

            // 4. 验证会话并消费nonce
            try {
                // 从MDC获取设备信息
                String deviceHash = TraceContext.getDeviceHash();
                String clientIP = TraceContext.getClientIPFromMDC();
                String userAgent = TraceContext.getUserAgent();

                // 验证会话并消费nonce
                Map<String, Object> sessionInfo = sessionService.validateSessionWithoutConsumingNonce(sessionToken, nonce, deviceHash, clientIP, userAgent);
                String jti = (String) sessionInfo.get("jti");

                // 检查验证码尝试是否超限
                if (sessionService.isVerificationCodeAttemptsExceeded(jti)) {
                    // 验证码错误次数过多，强制消费nonce
                    sessionService.generateNonce(jti);
                    throw new ValidationException(ResponseCode.NONCE_INVALID);
                }

                // 验证验证码
                try {
                    mailGunService.verifyCode(request.getEmail(), request.getCode());
                    TraceUtils.recordBusinessEvent("code_verification",
                            MapUtil.builder()
                                    .put("status", "success")
                                    .put("email", request.getEmail())
                                    .build());

                    TraceUtils.recordBusinessEvent("session_validation_register_success",
                            MapUtil.builder()
                                    .put("deviceHash", deviceHash)
                                    .put("nonce", nonce)
                                    .build());
                } catch (ValidationException e) {
                    // 记录验证码失败，但不立即消费nonce
                    sessionService.recordVerificationCodeFailure(jti);
                    throw e;
                }

            } catch (Exception e) {
                TraceUtils.recordBusinessEvent("session_validation_register_failed",
                        MapUtil.builder()
                                .put("reason", e.getMessage())
                                .build());
                log.error("注册流程会话验证失败 - 原因: {}", e.getMessage());
                // 注册流程中会话验证失败应该直接抛出异常，不能继续
                throw e;
            }

            // 5. 执行用户注册
            UserRegisterResponse response;
            try {
                // 调用用户注册服务
                response = userRegisterService.registerUser(request);

                // 记录用户创建事件
                TraceUtils.recordBusinessEvent("user_created",
                        MapUtil.builder()
                                .put("email", request.getEmail())
                                .put("userId", response.getUserId())
                                .build());

                // 异步记录设备信息（不影响注册流程）
                try {
                    String deviceHash = TraceContext.getDeviceHash();
                    String visitorId = TraceContext.getVisitorId();
                    String clientIP = TraceContext.getClientIPFromMDC();
                    String userAgent = TraceContext.getUserAgent();

                    userDevicesService.recordUserDevice(
                        Long.valueOf(response.getUserId()),
                        visitorId,
                        deviceHash,
                        clientIP,
                        userAgent
                    );
                } catch (Exception e) {
                    log.warn("记录设备信息失败，不影响注册流程 - userId: {}, error: {}",
                            response.getUserId(), e.getMessage());
                }

            } catch (Exception e) {
                TraceUtils.recordError("user_registration_error", e.getMessage(), e);
                throw e;
            }

            // 记录注册成功事件
            TraceUtils.recordBusinessEvent("register_success",
                    MapUtil.builder()
                            .put("email", request.getEmail())
                            .put("userId", response.getUserId())
                            .build());

            return ApiResponse.success(response, "注册成功");

        } catch (Exception e) {
            // 记录注册失败错误
            TraceUtils.recordError("register_error", e.getMessage(), e);
            log.error("用户注册失败 - 邮箱: {}", request.getEmail(), e);
            throw e; // 让全局异常处理器处理
        }
    }
} 