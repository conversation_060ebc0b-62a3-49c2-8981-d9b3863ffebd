package com.jiashu.labiai.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.request.auth.LoginRequest;
import com.jiashu.labiai.dto.response.auth.LoginResponse;
import com.jiashu.labiai.entity.LoginContext;
import com.jiashu.labiai.enums.LoginType;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.AuthenticationException;
import com.jiashu.labiai.exception.BusinessException;
import com.jiashu.labiai.service.EnhancedAuthService;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.DeviceInfo;
import com.jiashu.labiai.util.DeviceUtil;
import com.jiashu.labiai.util.IpUtil;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 增强认证控制器
 */
@RestController
@RequestMapping("/auth")
@Slf4j
@Validated
@RequiredArgsConstructor
public class AuthController {

    private final EnhancedAuthService enhancedAuthService;
    private final IpUtil ipUtil;

    /**
     * 用户登录 - 集成设备指纹和安全验证
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(
            @Valid @RequestBody LoginRequest request) {

        String deviceId = TraceContext.getVisitorId();
        String deviceHash = TraceContext.getDeviceHash();
        String clientIp = TraceContext.getClientIPFromMDC();
        String userAgent = TraceContext.getUserAgent();

        DeviceInfo deviceInfo = DeviceUtil.parseUserAgent(userAgent);

        // 构建登录上下文
        LoginContext context = LoginContext.builder()
                .email(request.getEmail())
                .deviceId(deviceId)
                .deviceHash(deviceHash)
                .ipAddress(clientIp)
                .deviceInfo(deviceInfo)
                .loginTime(new Date())
                .rememberMe(request.isRememberMe())
                .loginType(LoginType.PASSWORD)
                .build();

        try {
            // 执行增强登录
            LoginResponse response = enhancedAuthService.login(request, context);

            TraceUtils.recordBusinessEvent("login_success", context);

            return ApiResponse.success(response, "登录成功");

        } catch (AuthenticationException e) {
            TraceUtils.recordBusinessEvent("login_failed", context);
            throw e;
        } catch (Exception e) {
            TraceUtils.recordBusinessEvent("login_failed", context);
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * 用户注销
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        try {
            String tokenValue = null;
            try {
                tokenValue = StpUtil.getTokenValue();
            } catch (Exception e) {
                // Token可能已经过期，忽略异常
            }

            // Sa-Token注销
            StpUtil.logout();

            log.info("用户注销成功: tokenValue={}, ip={}",
                    tokenValue, TraceContext.getClientIPFromMDC());

            return ApiResponse.success(null, "注销成功");

        } catch (Exception e) {
            log.error("注销过程发生异常", e);
            return ApiResponse.success(null, "注销成功"); // 注销总是返回成功
        }
    }

    /**
     * 强制下线所有其他会话
     */
    @PostMapping("/logout-others")
    public ResponseEntity<ApiResponse<Void>> logoutOtherSessions() {
        try {
            StpUtil.checkLogin();
            Long userId = StpUtil.getLoginIdAsLong();
            String currentToken = StpUtil.getTokenValue();

            // 获取所有Token并踢掉除当前Token外的其他Token
            List<String> tokenList = StpUtil.getTokenValueListByLoginId(userId);
            int loggedOutCount = 0;

            for (String token : tokenList) {
                if (!token.equals(currentToken)) {
                    StpUtil.kickoutByTokenValue(token);
                    loggedOutCount++;
                }
            }

            log.info("用户强制下线其他会话: userId={}, loggedOutCount={}", userId, loggedOutCount);

            String message = String.format("已强制下线%d个其他会话", loggedOutCount);
            return ResponseEntity.ok(ApiResponse.success(null, message));

        } catch (Exception e) {
            log.error("强制下线其他会话失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(500, "操作失败"));
        }
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/check")
    public ApiResponse<Void> checkLoginStatus() {
        try {
            StpUtil.checkLogin();
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("检查登录状态失败", e);
            throw AuthenticationException.loginRequired();
        }
    }

    // 辅助方法
    private String getLocationFromIP(String ip) {
        // 简化实现：可以集成第三方IP定位服务
        if (ip.startsWith("192.168.") || ip.startsWith("10.") || ip.startsWith("172.")) {
            return "内网IP";
        }
        return "未知位置";
    }
} 