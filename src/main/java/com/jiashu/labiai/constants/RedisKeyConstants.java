package com.jiashu.labiai.constants;

/**
 * Redis Key 常量类
 * 统一管理所有Redis key前缀，便于维护和管理
 * 
 * <AUTHOR>
 */
public class RedisKeyConstants {

    // ==================== 会话相关 ====================
    
    /**
     * 会话nonce前缀
     */
    public static final String SESSION_NONCE_PREFIX = "session_nonce:";
    
    /**
     * 验证码失败次数前缀
     */
    public static final String VERIFY_CODE_FAILURES_PREFIX = "verify_code_failures:";

    // ==================== 黑名单相关 ====================
    
    /**
     * 设备黑名单前缀
     */
    public static final String BLACKLIST_DEVICE_PREFIX = "blacklist:device:";
    
    /**
     * 会话黑名单前缀
     */
    public static final String BLACKLIST_SESSION_PREFIX = "blacklist:session:";

    // ==================== 限流相关 ====================
    
    /**
     * 通用限流前缀
     */
    public static final String RATE_LIMIT_PREFIX = "rate_limit:";
    
    /**
     * 邮箱登录限流前缀
     */
    public static final String USER_LOGIN_LIMIT_PREFIX = "user_login:";
    
    /**
     * IP登录限流前缀
     */
    public static final String IP_LOGIN_LIMIT_PREFIX = "ip_login:";
    
    /**
     * 设备登录限流前缀
     */
    public static final String DEVICE_LOGIN_LIMIT_PREFIX = "device_login:";
    
    /**
     * 初始化IP限流前缀
     */
    public static final String INIT_IP_LIMIT_PREFIX = "init:ip:";
    
    /**
     * 初始化设备限流前缀
     */
    public static final String INIT_DEVICE_LIMIT_PREFIX = "init:device:";
    
    // ==================== 登录失败相关 ====================
    
    /**
     * 用户登录失败前缀
     */
    public static final String USER_LOGIN_FAILURE_PREFIX = "login_failure:user:";
    
    /**
     * IP登录失败前缀
     */
    public static final String IP_LOGIN_FAILURE_PREFIX = "login_failure:ip:";
    
    /**
     * 设备登录失败前缀
     */
    public static final String DEVICE_LOGIN_FAILURE_PREFIX = "login_failure:device:";

    // ==================== 邮件服务相关 ====================
    
    /**
     * 验证码前缀
     */
    public static final String VERIFICATION_CODE_PREFIX = "verification_code:";
    
    /**
     * 邮件发送间隔限制前缀
     */
    public static final String EMAIL_INTERVAL_PREFIX = "email_interval:";
    
    /**
     * 设备指纹发送间隔限制前缀
     */
    public static final String FINGERPRINT_INTERVAL_PREFIX = "fingerprint_interval:";
    
    /**
     * 邮件小时限制前缀
     */
    public static final String EMAIL_HOURLY_PREFIX = "email_hourly:";
    
    /**
     * 设备指纹小时限制前缀
     */
    public static final String FINGERPRINT_HOURLY_PREFIX = "fingerprint_hourly:";
    
    /**
     * 邮件日限制前缀
     */
    public static final String EMAIL_DAILY_PREFIX = "email_daily:";
    
    /**
     * 设备指纹日限制前缀
     */
    public static final String FINGERPRINT_DAILY_PREFIX = "fingerprint_daily:";

    // ==================== 工具方法 ====================
    
    /**
     * 构建会话nonce key
     * @param jti JWT ID
     * @return Redis key
     */
    public static String buildSessionNonceKey(String jti) {
        return SESSION_NONCE_PREFIX + jti;
    }
    
    /**
     * 构建设备黑名单key
     * @param deviceId 设备ID
     * @return Redis key
     */
    public static String buildBlacklistDeviceKey(String deviceId) {
        return BLACKLIST_DEVICE_PREFIX + deviceId;
    }
    
    /**
     * 构建会话黑名单key
     * @param sessionId 会话ID
     * @return Redis key
     */
    public static String buildBlacklistSessionKey(String sessionId) {
        return BLACKLIST_SESSION_PREFIX + sessionId;
    }
    
    /**
     * 构建限流key
     * @param key 限流标识
     * @return Redis key
     */
    public static String buildRateLimitKey(String key) {
        return RATE_LIMIT_PREFIX + key;
    }
    
    /**
     * 构建用户登录限流key
     * @param userId 用户ID
     * @return Redis key
     */
    public static String buildUserLoginLimitKey(Long userId) {
        return USER_LOGIN_LIMIT_PREFIX + userId;
    }
    
    /**
     * 构建IP登录限流key
     * @param ip IP地址
     * @return Redis key
     */
    public static String buildIpLoginLimitKey(String ip) {
        return IP_LOGIN_LIMIT_PREFIX + ip;
    }
    
    /**
     * 构建设备登录限流key
     * @param deviceId 设备ID
     * @return Redis key
     */
    public static String buildDeviceLoginLimitKey(String deviceId) {
        return DEVICE_LOGIN_LIMIT_PREFIX + deviceId;
    }
    
    /**
     * 构建用户登录失败key
     * @param userId 用户ID
     * @return Redis key
     */
    public static String buildUserLoginFailureKey(Long userId) {
        return USER_LOGIN_FAILURE_PREFIX + userId;
    }
    
    /**
     * 构建IP登录失败key
     * @param ip IP地址
     * @return Redis key
     */
    public static String buildIpLoginFailureKey(String ip) {
        return IP_LOGIN_FAILURE_PREFIX + ip;
    }
    
    /**
     * 构建设备登录失败key
     * @param deviceId 设备ID
     * @return Redis key
     */
    public static String buildDeviceLoginFailureKey(String deviceId) {
        return DEVICE_LOGIN_FAILURE_PREFIX + deviceId;
    }
    
    /**
     * 构建初始化IP限流key
     * @param ip IP地址
     * @return Redis key
     */
    public static String buildInitIpLimitKey(String ip) {
        return INIT_IP_LIMIT_PREFIX + ip;
    }
    
    /**
     * 构建初始化设备限流key
     * @param deviceId 设备ID
     * @return Redis key
     */
    public static String buildInitDeviceLimitKey(String deviceId) {
        return INIT_DEVICE_LIMIT_PREFIX + deviceId;
    }
    
    /**
     * 构建验证码key
     * @param email 邮箱
     * @return Redis key
     */
    public static String buildVerificationCodeKey(String email) {
        return VERIFICATION_CODE_PREFIX + email;
    }
    
    /**
     * 构建邮件发送间隔限制key
     * @param email 邮箱
     * @return Redis key
     */
    public static String buildEmailIntervalKey(String email) {
        return EMAIL_INTERVAL_PREFIX + email;
    }
    
    /**
     * 构建设备指纹发送间隔限制key
     * @param fingerprint 设备指纹
     * @return Redis key
     */
    public static String buildFingerprintIntervalKey(String fingerprint) {
        return FINGERPRINT_INTERVAL_PREFIX + fingerprint;
    }
    
    /**
     * 构建邮件小时限制key
     * @param email 邮箱
     * @param hour 小时
     * @return Redis key
     */
    public static String buildEmailHourlyKey(String email, String hour) {
        return EMAIL_HOURLY_PREFIX + email + ":" + hour;
    }
    
    /**
     * 构建设备指纹小时限制key
     * @param fingerprint 设备指纹
     * @param hour 小时
     * @return Redis key
     */
    public static String buildFingerprintHourlyKey(String fingerprint, String hour) {
        return FINGERPRINT_HOURLY_PREFIX + fingerprint + ":" + hour;
    }
    
    /**
     * 构建邮件日限制key
     * @param email 邮箱
     * @param date 日期
     * @return Redis key
     */
    public static String buildEmailDailyKey(String email, String date) {
        return EMAIL_DAILY_PREFIX + email + ":" + date;
    }
    
    /**
     * 构建设备指纹日限制key
     * @param fingerprint 设备指纹
     * @param date 日期
     * @return Redis key
     */
    public static String buildFingerprintDailyKey(String fingerprint, String date) {
        return FINGERPRINT_DAILY_PREFIX + fingerprint + ":" + date;
    }
    
    /**
     * 构建验证码失败次数key
     * @param jti JWT ID
     * @return Redis key
     */
    public static String buildVerifyCodeFailuresKey(String jti) {
        return VERIFY_CODE_FAILURES_PREFIX + jti;
    }
} 