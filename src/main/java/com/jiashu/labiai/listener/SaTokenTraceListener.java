package com.jiashu.labiai.listener;

import cn.dev33.satoken.listener.SaTokenListener;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import com.jiashu.labiai.trace.TraceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Sa-Token事件监听器 - 集成链路追踪
 * 增强Sa-Token的登录和注销事件
 */
//@Component
public class SaTokenTraceListener implements SaTokenListener {
    
    private static final Logger log = LoggerFactory.getLogger(SaTokenTraceListener.class);
    
    @Override
    public void doLogin(String loginType, Object loginId, String tokenValue, SaLoginParameter saLoginParameter) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token login success - LoginId: {}, TokenValue: {}, Device: {}, Timeout: {}s, TraceId: {}", 
            loginId, tokenValue, SaLoginParameter.create().getDeviceId(), SaLoginParameter.create().getTimeout(), traceId);
        
        // 可以将TraceId存储到Sa-Token的扩展信息中
        try {
            StpUtil.getSession().set("currentTraceId", traceId);
        } catch (Exception e) {
            log.warn("Failed to set traceId to Sa-Token session", e);
        }
    }
    
    @Override
    public void doLogout(String loginType, Object loginId, String tokenValue) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token logout - LoginId: {}, TokenValue: {}, TraceId: {}", 
            loginId, tokenValue, traceId);
    }
    
    @Override
    public void doKickout(String loginType, Object loginId, String tokenValue) {
        String traceId = TraceContext.getTraceId();
        
        log.warn("Sa-Token kickout - LoginId: {}, TokenValue: {}, TraceId: {}", 
            loginId, tokenValue, traceId);
    }
    
    @Override
    public void doReplaced(String loginType, Object loginId, String tokenValue) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token replaced - LoginId: {}, TokenValue: {}, TraceId: {}", 
            loginId, tokenValue, traceId);
    }
    
    @Override
    public void doDisable(String loginType, Object loginId, String service, int level, long disableTime) {
        String traceId = TraceContext.getTraceId();
        
        log.warn("Sa-Token disable - LoginId: {}, Service: {}, Level: {}, DisableTime: {}s, TraceId: {}", 
            loginId, service, level, disableTime, traceId);
    }
    
    @Override
    public void doUntieDisable(String loginType, Object loginId, String service) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token untie disable - LoginId: {}, Service: {}, TraceId: {}", 
            loginId, service, traceId);
    }
    
    @Override
    public void doOpenSafe(String loginType, String tokenValue, String service, long safeTime) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token open safe - TokenValue: {}, Service: {}, SafeTime: {}s, TraceId: {}", 
            tokenValue, service, safeTime, traceId);
    }
    
    @Override
    public void doCloseSafe(String loginType, String tokenValue, String service) {
        String traceId = TraceContext.getTraceId();
        
        log.info("Sa-Token close safe - TokenValue: {}, Service: {}, TraceId: {}", 
            tokenValue, service, traceId);
    }
    
    @Override
    public void doCreateSession(String id) {
        String traceId = TraceContext.getTraceId();
        
        log.debug("Sa-Token create session - Id: {}, TraceId: {}", id, traceId);
    }
    
    @Override
    public void doLogoutSession(String id) {
        String traceId = TraceContext.getTraceId();
        
        log.debug("Sa-Token logout session - Id: {}, TraceId: {}", id, traceId);
    }
    
    @Override
    public void doRenewTimeout(String tokenValue, Object loginId, long timeout) {
        String traceId = TraceContext.getTraceId();
        
        log.debug("Sa-Token renew timeout - TokenValue: {}, LoginId: {}, Timeout: {}s, TraceId: {}", 
            tokenValue, loginId, timeout, traceId);
    }
} 