package com.jiashu.labiai.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jiashu.labiai.entity.Order;
import com.jiashu.labiai.enums.OrderStatusEnum;
import com.jiashu.labiai.service.IDiscountCodeService;
import com.jiashu.labiai.service.IOrderService;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单超时处理定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderTimeoutTask {

    private final IOrderService orderService;
    private final IDiscountCodeService discountCodeService;

    /**
     * 每分钟执行一次，处理超时订单
     */
    @Scheduled(fixedRate = 60000)
    public void handleTimeoutOrders() {
        TraceUtils.executeWithTrace("handleTimeoutOrders", () -> {
            try {
                // 查询所有超时的待支付订单
                List<Order> timeoutOrders = orderService.list(
                    new QueryWrapper<Order>()
                        .eq("status", OrderStatusEnum.PENDING.getCode())
                        .lt("expired_at", LocalDateTime.now())
                );

                if (timeoutOrders.isEmpty()) {
                    return null;
                }

                Map<String, Object> taskDetails = new HashMap<>();
                taskDetails.put("timeoutOrderCount", timeoutOrders.size());
                TraceUtils.recordBusinessEvent("ORDER_TIMEOUT_TASK_START", taskDetails);

                int processedCount = 0;
                for (Order order : timeoutOrders) {
                    try {
                        // 1. 更新订单状态为已取消
                        order.setStatus(OrderStatusEnum.CANCELLED.getCode());
                        orderService.updateById(order);

                        // 2. 如果使用了优惠码，取消优惠码使用
                        if (order.getDiscountCodeId() != null) {
                            discountCodeService.cancelDiscountCodeUsage(order.getId());
                        }

                        Map<String, Object> orderDetails = new HashMap<>();
                        orderDetails.put("orderId", order.getId());
                        orderDetails.put("orderNo", order.getOrderNo());
                        orderDetails.put("hasDiscountCode", order.getDiscountCodeId() != null);
                        TraceUtils.recordBusinessEvent("ORDER_TIMEOUT_PROCESSED", orderDetails);

                        processedCount++;
                        
                    } catch (Exception e) {
                        Map<String, Object> errorDetails = new HashMap<>();
                        errorDetails.put("orderId", order.getId());
                        errorDetails.put("error", e.getMessage());
                        TraceUtils.recordError("ORDER_TIMEOUT_PROCESS_FAILED", errorDetails);
                        log.error("处理超时订单失败: orderId={}", order.getId(), e);
                    }
                }

                Map<String, Object> resultDetails = new HashMap<>();
                resultDetails.put("totalCount", timeoutOrders.size());
                resultDetails.put("processedCount", processedCount);
                TraceUtils.recordBusinessEvent("ORDER_TIMEOUT_TASK_COMPLETED", resultDetails);

                log.info("订单超时处理完成: 总数={}, 处理成功={}", timeoutOrders.size(), processedCount);

                return null;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("ORDER_TIMEOUT_TASK_FAILED", errorDetails);
                log.error("订单超时处理任务执行失败", e);
                return null;
            }
        });
    }
} 