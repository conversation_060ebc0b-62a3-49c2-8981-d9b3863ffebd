package com.jiashu.labiai.trace;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 链路追踪上下文管理器
 * 基于Hutool工具类的TraceId管理器
 */
@Component
public class TraceContext {
    
    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String TRACE_ID_MDC_KEY = "traceId";
    
    // 设备信息相关常量
    private static final String VISITOR_ID_HEADER = "X-Visitor-ID";
    private static final String DEVICE_HASH_HEADER = "X-Device-Hash";
    private static final String VISITOR_ID_MDC_KEY = "visitorId";
    private static final String DEVICE_HASH_MDC_KEY = "deviceHash";
    private static final String CLIENT_IP_MDC_KEY = "clientIP";
    private static final String USER_AGENT_MDC_KEY = "userAgent";
    
    /**
     * 生成TraceId (使用Hutool的IdUtil)
     */
    public static String generateTraceId() {
        return IdUtil.fastSimpleUUID();
    }
    
    /**
     * 设置TraceId到MDC
     */
    public static void setTraceId(String traceId) {
        if (StrUtil.isNotBlank(traceId)) {
            MDC.put(TRACE_ID_MDC_KEY, traceId);
        }
    }
    
    /**
     * 获取当前TraceId
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_MDC_KEY);
    }
    
    /**
     * 从请求头获取TraceId
     */
    public static String extractTraceId(HttpServletRequest request) {
        String traceId = request.getHeader(TRACE_ID_HEADER);
        if (StrUtil.isBlank(traceId)) {
            traceId = request.getHeader("traceid");
        }
        if (StrUtil.isBlank(traceId)) {
            traceId = request.getHeader("trace-id");
        }
        return traceId;
    }
    
    /**
     * 获取客户端真实IP (使用Hutool)
     */
    public static String getClientIP(HttpServletRequest request) {
        return ServletUtil.getClientIP(request);
    }
    
    /**
     * 设置访客ID到MDC
     */
    public static void setVisitorId(String visitorId) {
        if (StrUtil.isNotBlank(visitorId)) {
            MDC.put(VISITOR_ID_MDC_KEY, visitorId);
        }
    }
    
    /**
     * 设置设备哈希到MDC
     */
    public static void setDeviceHash(String deviceHash) {
        if (StrUtil.isNotBlank(deviceHash)) {
            MDC.put(DEVICE_HASH_MDC_KEY, deviceHash);
        }
    }
    
    /**
     * 设置客户端IP到MDC
     */
    public static void setClientIP(String clientIP) {
        if (StrUtil.isNotBlank(clientIP)) {
            MDC.put(CLIENT_IP_MDC_KEY, clientIP);
        }
    }
    
    /**
     * 设置用户代理到MDC
     */
    public static void setUserAgent(String userAgent) {
        if (StrUtil.isNotBlank(userAgent)) {
            MDC.put(USER_AGENT_MDC_KEY, userAgent);
        }
    }
    
    /**
     * 获取访客ID
     */
    public static String getVisitorId() {
        return MDC.get(VISITOR_ID_MDC_KEY);
    }
    
    /**
     * 获取设备哈希
     */
    public static String getDeviceHash() {
        return MDC.get(DEVICE_HASH_MDC_KEY);
    }
    
    /**
     * 获取客户端IP（从MDC）
     */
    public static String getClientIPFromMDC() {
        return MDC.get(CLIENT_IP_MDC_KEY);
    }
    
    /**
     * 获取用户代理
     */
    public static String getUserAgent() {
        return MDC.get(USER_AGENT_MDC_KEY);
    }
    
    /**
     * 从请求头提取访客ID
     */
    public static String extractVisitorId(HttpServletRequest request) {
        return request.getHeader(VISITOR_ID_HEADER);
    }
    
    /**
     * 从请求头提取设备哈希
     */
    public static String extractDeviceHash(HttpServletRequest request) {
        return request.getHeader(DEVICE_HASH_HEADER);
    }
    
    /**
     * 检查是否有访客ID
     */
    public static boolean hasVisitorId() {
        return StrUtil.isNotBlank(getVisitorId());
    }
    
    /**
     * 检查是否有设备哈希
     */
    public static boolean hasDeviceHash() {
        return StrUtil.isNotBlank(getDeviceHash());
    }
    
    /**
     * 获取设备信息摘要
     */
    public static String getDeviceSummary() {
        String visitorId = getVisitorId();
        String deviceHash = getDeviceHash();
        String clientIP = getClientIPFromMDC();
        
        StringBuilder summary = new StringBuilder();
        if (StrUtil.isNotBlank(visitorId)) {
            summary.append("VisitorId:").append(visitorId);
        }
        if (StrUtil.isNotBlank(deviceHash)) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("DeviceHash:").append(deviceHash);
        }
        if (StrUtil.isNotBlank(clientIP)) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("IP:").append(clientIP);
        }
        
        return summary.length() > 0 ? summary.toString() : "Unknown";
    }
    
    /**
     * 清理MDC
     */
    public static void clear() {
        // 清理链路追踪信息
        MDC.remove(TRACE_ID_MDC_KEY);
        
        // 清理设备信息
        MDC.remove(VISITOR_ID_MDC_KEY);
        MDC.remove(DEVICE_HASH_MDC_KEY);
        MDC.remove(CLIENT_IP_MDC_KEY);
        MDC.remove(USER_AGENT_MDC_KEY);
    }
    
    /**
     * 获取TraceId Header名称
     */
    public static String getTraceIdHeader() {
        return TRACE_ID_HEADER;
    }
} 