package com.jiashu.labiai.interceptor;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.jiashu.labiai.entity.LoginContext;
import com.jiashu.labiai.entity.Users;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.enums.SecurityEventType;
import com.jiashu.labiai.enums.UserStatus;
import com.jiashu.labiai.exception.AuthenticationException;
import com.jiashu.labiai.exception.SecurityException;
import com.jiashu.labiai.service.ISecurityEventsService;
import com.jiashu.labiai.service.IUserDevicesService;
import com.jiashu.labiai.service.IUsersService;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.IpUtil;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 设备指纹安全拦截器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DeviceSecurityInterceptor implements HandlerInterceptor {

    private final IUsersService usersService;
    private final ISecurityEventsService securityEventsService;
    private final IUserDevicesService userDevicesService;
    private final IpUtil ipUtil;
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 不需要设备验证的接口 - 支持Ant风格路径匹配
    private static final Set<String> SKIP_DEVICE_CHECK_PATHS = new HashSet<String>() {{
        add("/register/**");
        add("/auth/**");
        add("/health");
        add("/actuator/**");
        add("/payment/notify/xunhupay");
        add("/error");           // Spring Boot 错误处理页面
        add("/favicon.ico");     // 浏览器图标请求
        add("/static/**");       // 静态资源
        add("/public/**");       // 公共资源
    }};

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestPath = request.getRequestURI();

        // 跳过不需要验证的接口
        if (shouldSkipDeviceCheck(requestPath)) {
            log.debug("跳过设备验证: {}", requestPath);
            return true;
        }

        log.debug("执行设备验证: {}", requestPath);

        try {
            // Sa-Token基础登录验证
            if (!StpUtil.isLogin()) {
                throw AuthenticationException.loginRequired();
            }

            // 获取当前用户和Token信息
            Long userId = StpUtil.getLoginIdAsLong();
            String tokenValue = StpUtil.getTokenValue();

            // 1. 简单的设备拉黑检查
            try {
                checkDeviceBlacklist(request, userId);
                // 2. 设备安全验证
                validateDeviceSecurity(request, userId, tokenValue);
            } catch (SecurityException e) {
                handleSecurityViolation(userId, tokenValue);
                throw e;
            }
            // 3. 更新会话活动时间
            updateSessionActivity(tokenValue, request);
            return true;
        } catch (NotLoginException e) {
            throw e;
        } catch (Exception e) {
            TraceUtils.recordError("device_security_interceptor_error", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 简单的设备拉黑检查
     */
    private void checkDeviceBlacklist(HttpServletRequest request, Long userId) throws IOException {
        String deviceId = TraceContext.extractVisitorId(request);

        if (StringUtils.hasText(deviceId)) {
            // 检查设备是否被拉黑
            if (userDevicesService.isDeviceBlacklisted(userId, deviceId)) {
                log.warn("设备已被拉黑: userId={}, deviceId={}", userId, deviceId);
                
                // 记录拉黑命中事件
                Map<String, Object> eventData = new HashMap<>();
                eventData.put("deviceId", deviceId);
                eventData.put("requestPath", request.getRequestURI());
                
                securityEventsService.recordSecurityEvent(
                    SecurityEventType.BLACKLISTED_DEVICE_ACCESS,
                    "拉黑设备访问尝试",
                    "被拉黑的设备尝试访问系统",
                    userId,
                    deviceId,
                    null,
                    TraceContext.getClientIPFromMDC(),
                    null,
                    eventData
                );
                
                throw new SecurityException(ResponseCode.DEVICE_BLACKLISTED, "设备已被拉黑");
            }
        }
    }

    /**
     * 设备安全验证
     */
    private void validateDeviceSecurity(HttpServletRequest request, Long userId, String tokenValue) {
        try {
            // 获取Token中的设备信息

            Object loginContextObj = StpUtil.getTokenSession().get("loginContext");
            LoginContext loginContext = (LoginContext) loginContextObj;



            String tokenDeviceId = loginContext.getDeviceId();
            String tokenDeviceHash = loginContext.getDeviceHash();
//            Integer tokenTrustLevel = (Integer) StpUtil.getExtra("trustLevel");
            String tokenIpAddress = loginContext.getIpAddress();
//            String loginMethod = loginContext.getLoginType().name();

            // 获取当前请求的设备信息
            String currentDeviceId = TraceContext.getVisitorId();
            String currentDeviceHash = TraceContext.getDeviceHash();
            String currentIpAddress = TraceContext.getClientIPFromMDC();


            // 1. 设备ID验证 严格模式，设备ID不一致禁用
            if (StringUtils.hasText(tokenDeviceId) && !Objects.equals(tokenDeviceId, currentDeviceId)) {
                log.warn("设备ID不匹配，可能存在Token盗用风险. userId={}, tokenDeviceId={}, currentDeviceId={}",
                        userId, tokenDeviceId, currentDeviceId);
                recordSecurityEvent(userId, tokenValue, SecurityEventType.DEVICE_ID_MISMATCH,
                        String.format("设备ID不匹配: token=%s, current=%s", tokenDeviceId, currentDeviceId));
                throw new SecurityException(ResponseCode.SESSION_KICKED_OUT, "设备指纹不匹配");
            }

            // 2. 设备指纹验证(只记录日志，不阻断)
            if (StringUtils.hasText(tokenDeviceHash) && StringUtils.hasText(currentDeviceHash)) {
                if (!Objects.equals(tokenDeviceHash, currentDeviceHash)) {
                    log.warn("设备指纹不匹配. userId={}, tokenDeviceHash={}, currentDeviceHash={}",
                            userId, tokenDeviceHash, currentDeviceHash);
                    recordSecurityEvent(userId, tokenValue, SecurityEventType.DEVICE_HASH_MISMATCH,
                            "设备指纹不匹配，可能存在环境篡改");
                }
            }

            // 3. IP地址验证（网段级别） 只记录日志，不阻断
            if (StringUtils.hasText(tokenIpAddress)) {
                if (!isSameIPNetwork(tokenIpAddress, currentIpAddress)) {
                    log.warn("设备IP地址发生变化. userId={}, tokenIP={}, currentIP={}",
                            userId, tokenIpAddress, currentIpAddress);

                    
                    // 根据设备信任级别选择不同的事件类型
//                    SecurityEventType ipChangeEventType = (tokenTrustLevel != null && tokenTrustLevel >= 70)
//                        ? SecurityEventType.IP_CHANGE_TRUSTED_DEVICE
//                        : SecurityEventType.IP_CHANGE_UNTRUSTED_DEVICE;
//
//                    recordSecurityEvent(userId, tokenValue, ipChangeEventType,
//                            String.format("设备IP变化: %s -> %s", tokenIpAddress, currentIpAddress));
                }
            }

            // 4. 用户状态验证
            validateUserState(userId);

            // 5. 登录方式验证（预留第三方登录验证）
//            validateLoginMethod(userId, loginMethod);

        } catch (Exception e) {
            log.error("设备安全验证过程中发生异常. userId={}, tokenValue={}", userId, tokenValue, e);
            throw e;
        }
    }

    /**
     * 验证用户状态
     */
    private void validateUserState(Long userId) {
        Users user = usersService.getById(userId);
        if (user == null) {
            throw new SecurityException(ResponseCode.USER_NOT_FOUND, "用户不存在");
        }

        if (user.getStatus() == null) {
            user.setStatus(UserStatus.NORMAL);
            usersService.updateById(user);
        } else {
            if (user.getStatus() == UserStatus.DISABLED) {
                // 记录账户禁用事件
                recordSecurityEvent(userId, null, SecurityEventType.ACCOUNT_DISABLED, "账号已被禁用");
                throw new SecurityException(ResponseCode.ACCOUNT_DISABLED, "账号已被禁用");
            }
            if (user.getStatus() == UserStatus.LOCKED) {
                LocalDateTime lockedUntil = user.getLockedUntil();
                if (lockedUntil == null || lockedUntil.isAfter(LocalDateTime.now())) {
                    // 记录账户锁定事件
                    recordSecurityEvent(userId, null, SecurityEventType.ACCOUNT_LOCKED, "账号已被锁定");
                    throw new SecurityException(ResponseCode.ACCOUNT_LOCKED, "用户已被停用");
                } else {
                    user.setStatus(UserStatus.NORMAL);
                    usersService.updateById(user);
                }
            }
        }
    }

    /**
     * 验证登录方式（预留第三方登录扩展）
     */
    private void validateLoginMethod(Long userId, String loginMethod) {
        if (!StringUtils.hasText(loginMethod)) {
//            return true; // 旧版本兼容
        }

        // 目前只支持密码登录，后续可扩展第三方登录验证
        if ("password".equals(loginMethod)) {
//            return true;
        }

        // 预留第三方登录验证逻辑
        if ("wechat".equals(loginMethod) || "github".equals(loginMethod) || "google".equals(loginMethod)) {
            log.warn("第三方登录验证暂未实现: userId={}, loginMethod={}", userId, loginMethod);
//            return true; // 临时通过，待实现
        }

        log.warn("不支持的登录方式: userId={}, loginMethod={}", userId, loginMethod);
    }

    /**
     * 更新会话活动时间
     */
    private void updateSessionActivity(String tokenValue, HttpServletRequest request) {
        try {
            // 更新扩展信息
            StpUtil.getSession().set("lastRequestPath", request.getRequestURI());
            StpUtil.getSession().set("lastRequestTime", System.currentTimeMillis());
        } catch (Exception e) {
            log.error("更新会话活动时间失败: tokenValue={}", tokenValue, e);
        }
    }

    /**
     * 处理安全违规
     */
    private void handleSecurityViolation(Long userId, String tokenValue) {
        // 强制下线当前Token
        StpUtil.kickoutByTokenValue(tokenValue);

        // 记录高级别安全事件
        recordSecurityEvent(userId, tokenValue, SecurityEventType.DEVICE_SECURITY_VIOLATION,
                "设备安全验证失败，已强制下线");
        TraceUtils.recordError("device_security_violation", "设备安全验证失败，已强制下线", null);
    }

    /**
     * 处理安全异常
     */
    private void handleSecurityException(HttpServletRequest request, HttpServletResponse response, Exception e) throws IOException {
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        String apiResponse = "{\"success\":false,\"code\":500,\"message\":\"安全验证异常，请稍后重试\"}";
        response.getWriter().write(apiResponse);
    }

    /**
     * 记录安全事件 (使用枚举类型)
     */
    private void recordSecurityEvent(Long userId, String tokenValue, SecurityEventType eventType, String description) {
        try {
            LoginContext loginContent = (LoginContext) StpUtil.getTokenSession().get("loginContext");
            String deviceId = loginContent.getDeviceId();
            String ipAddress = loginContent.getIpAddress();
            String loginMethod = loginContent.getLoginType().name();
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("description", description);
            if (tokenValue != null) {
                eventData.put("tokenValue", tokenValue);
            }

            // 使用安全事件服务记录事件
            securityEventsService.recordSecurityEvent(
                    eventType,
                    eventType.getName(),
                    description,
                    userId,
                    deviceId,
                    tokenValue,
                    ipAddress,
                    loginMethod,
                    eventData);

            log.warn("安全事件已记录: userId={}, eventType={}, description={}",
                    userId, eventType.getCode(), description);
        } catch (Exception e) {
            log.error("记录安全事件失败", e);
        }
    }

    /**
     * 判断是否为同一IP网段
     */
    private boolean isSameIPNetwork(String ip1, String ip2) {
        try {
            // IPv4简单网段判断（前3段相同）
            String[] parts1 = ip1.split("\\.");
            String[] parts2 = ip2.split("\\.");

            if (parts1.length >= 3 && parts2.length >= 3) {
                return parts1[0].equals(parts2[0]) &&
                        parts1[1].equals(parts2[1]) &&
                        parts1[2].equals(parts2[2]);
            }

            return Objects.equals(ip1, ip2);
        } catch (Exception e) {
            return Objects.equals(ip1, ip2);
        }
    }

    private boolean shouldSkipDeviceCheck(String path) {
        boolean shouldSkip = SKIP_DEVICE_CHECK_PATHS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
        log.debug("路径匹配检查: path={}, shouldSkip={}", path, shouldSkip);
        return shouldSkip;
    }
} 