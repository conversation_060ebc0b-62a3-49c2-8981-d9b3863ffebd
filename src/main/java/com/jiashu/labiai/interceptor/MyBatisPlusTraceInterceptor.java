package com.jiashu.labiai.interceptor;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * MyBatis Plus拦截器 - SQL执行链路追踪
 */
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", 
               args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
@Component
public class MyBatisPlusTraceInterceptor implements Interceptor {
    
    private static final Logger log = LoggerFactory.getLogger(MyBatisPlusTraceInterceptor.class);
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sqlId = mappedStatement.getId();
        String sqlCommandType = mappedStatement.getSqlCommandType().toString();
        
        // 提取简化的Mapper方法名
        String shortSqlId = extractShortSqlId(sqlId);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = invocation.proceed();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录SQL执行日志（TraceId会自动包含）
            log.info("SQL executed - Type: {}, Method: {}, Duration: {}ms", 
                sqlCommandType, shortSqlId, duration);
            
            // 如果是查询，记录结果数量
            if ("SELECT".equals(sqlCommandType) && result != null) {
                int count = getResultCount(result);
                if (count >= 0) {
                    log.debug("SQL query result count: {}", count);
                }
            }
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("SQL execution failed - Type: {}, Method: {}, Duration: {}ms, Error: {}", 
                sqlCommandType, shortSqlId, duration, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 提取简化的SQL ID (去掉包名，只保留类名和方法名)
     */
    private String extractShortSqlId(String sqlId) {
        if (sqlId == null) {
            return "unknown";
        }
        
        int lastDotIndex = sqlId.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < sqlId.length() - 1) {
            String className = sqlId.substring(0, lastDotIndex);
            String methodName = sqlId.substring(lastDotIndex + 1);
            
            // 提取类名（去掉包名）
            int classLastDotIndex = className.lastIndexOf('.');
            if (classLastDotIndex > 0 && classLastDotIndex < className.length() - 1) {
                className = className.substring(classLastDotIndex + 1);
            }
            
            return className + "." + methodName;
        }
        
        return sqlId;
    }
    
    /**
     * 获取结果数量
     */
    private int getResultCount(Object result) {
        try {
            if (result instanceof java.util.Collection) {
                return ((java.util.Collection<?>) result).size();
            } else if (result instanceof Object[]) {
                return ((Object[]) result).length;
            } else if (result != null) {
                return 1;
            }
        } catch (Exception e) {
            // 忽略获取数量失败的情况
        }
        return -1;
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件读取属性
    }
} 