package com.jiashu.labiai.interceptor;

import cn.hutool.core.util.StrUtil;
import com.jiashu.labiai.trace.TraceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Web请求链路追踪拦截器
 * 集成Spring Boot 2.7的WebMvcConfigurer
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TraceInterceptor implements HandlerInterceptor {
    
    private static final Logger log = LoggerFactory.getLogger(TraceInterceptor.class);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 1. 从请求头获取或生成TraceId
            String traceId = TraceContext.extractTraceId(request);
            if (StrUtil.isBlank(traceId)) {
                traceId = TraceContext.generateTraceId();
            }
            
            // 2. 从请求头获取或生成SpanId
//            String spanId = TraceContext.extractSpanId(request);
//            if (StrUtil.isBlank(spanId)) {
//                spanId = TraceContext.generateSpanId();
//            }
            
            // 3. 设置到MDC
            TraceContext.setTraceId(traceId);
//            TraceContext.setSpanId(spanId);
            
            // 4. 设置到响应头
            response.setHeader(TraceContext.getTraceIdHeader(), traceId);
//            response.setHeader(TraceContext.getSpanIdHeader(), spanId);
            
            // 5. 提取设备信息并设置到MDC（使用扩展的TraceContext）
            String visitorId = TraceContext.extractVisitorId(request);
            String deviceHash = TraceContext.extractDeviceHash(request);
            String clientIP = TraceContext.getClientIP(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 使用TraceContext设置设备信息到MDC
            TraceContext.setVisitorId(visitorId);
            TraceContext.setDeviceHash(deviceHash);
            TraceContext.setClientIP(clientIP);
            TraceContext.setUserAgent(userAgent);
            
            // 6. 记录请求开始日志
            String queryString = request.getQueryString();
            String fullUrl = queryString != null ? request.getRequestURI() + "?" + queryString : request.getRequestURI();
            
            log.info("Request started - Method: {}, URI: {}, ClientIP: {}, UserAgent: {}, VisitorID: {}, DeviceHash: {}", 
                request.getMethod(), fullUrl, clientIP, userAgent, visitorId, deviceHash);
            
            // 7. 设置请求开始时间
            request.setAttribute("requestStartTime", System.currentTimeMillis());
            
            return true;
            
        } catch (Exception e) {
            log.error("链路追踪拦截器异常", e);
            return true; // 不影响正常请求
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        try {
            // 计算请求耗时
            Long startTime = (Long) request.getAttribute("requestStartTime");
            long duration = startTime != null ? System.currentTimeMillis() - startTime : 0;
            
            // 记录请求完成日志
            String queryString = request.getQueryString();
            String fullUrl = queryString != null ? request.getRequestURI() + "?" + queryString : request.getRequestURI();
            
            if (ex != null) {
                log.error("Request completed with exception - Method: {}, URI: {}, Status: {}, Duration: {}ms, Exception: {}", 
                    request.getMethod(), fullUrl, response.getStatus(), duration, ex.getClass().getSimpleName());
            } else {
                log.info("Request completed - Method: {}, URI: {}, Status: {}, Duration: {}ms", 
                    request.getMethod(), fullUrl, response.getStatus(), duration);
            }
            
        } catch (Exception e) {
            log.error("链路追踪拦截器完成处理异常", e);
        } finally {
            // 清理MDC（TraceContext.clear()已包含设备信息清理）
            TraceContext.clear();
        }
    }
} 