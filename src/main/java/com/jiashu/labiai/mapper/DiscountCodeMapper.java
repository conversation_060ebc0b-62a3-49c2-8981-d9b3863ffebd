package com.jiashu.labiai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiashu.labiai.entity.DiscountCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 优惠码Mapper接口
 */
@Mapper
public interface DiscountCodeMapper extends BaseMapper<DiscountCode> {
    
    /**
     * 原子性增加优惠码使用次数（防超售）
     * 
     * @param discountCodeId 优惠码ID
     * @param maxUsage 最大使用次数
     * @return 影响的行数，0表示更新失败（已用完或状态异常）
     */
    @Update("UPDATE discount_codes SET used_count = used_count + 1, updated_at = NOW() " +
            "WHERE id = #{discountCodeId} AND used_count < #{maxUsage} AND status = 1")
    int incrementUsedCountWithCheck(@Param("discountCodeId") Long discountCodeId, 
                                  @Param("maxUsage") Integer maxUsage);
    
    /**
     * 原子性减少优惠码使用次数（订单取消时恢复）
     * 
     * @param discountCodeId 优惠码ID
     * @return 影响的行数，0表示使用次数已经为0
     */
    @Update("UPDATE discount_codes SET used_count = GREATEST(used_count - 1, 0), updated_at = NOW() " +
            "WHERE id = #{discountCodeId} AND used_count > 0")
    int decrementUsedCount(@Param("discountCodeId") Long discountCodeId);
} 