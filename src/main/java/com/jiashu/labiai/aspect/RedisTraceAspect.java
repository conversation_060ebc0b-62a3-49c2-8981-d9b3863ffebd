package com.jiashu.labiai.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Redis操作链路追踪切面
 * 基于Spring AOP的Redis追踪
 */
@Aspect
@Component
public class RedisTraceAspect {
    
    private static final Logger log = LoggerFactory.getLogger(RedisTraceAspect.class);
    
    /**
     * 拦截RedisTemplate的操作方法
     */
    @Around("execution(* org.springframework.data.redis.core.RedisTemplate.*(..))")
    public Object traceRedisOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 获取Redis key (如果是字符串参数)
        String redisKey = extractRedisKey(args);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录Redis操作日志
            log.debug("Redis operation - Method: {}, Key: {}, Duration: {}ms", 
                methodName, redisKey, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Redis operation failed - Method: {}, Key: {}, Duration: {}ms, Error: {}", 
                methodName, redisKey, duration, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 拦截StringRedisTemplate的操作方法
     */
    @Around("execution(* org.springframework.data.redis.core.StringRedisTemplate.*(..))")
    public Object traceStringRedisOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 获取Redis key
        String redisKey = extractRedisKey(args);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录Redis操作日志
            log.debug("StringRedis operation - Method: {}, Key: {}, Duration: {}ms", 
                methodName, redisKey, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("StringRedis operation failed - Method: {}, Key: {}, Duration: {}ms, Error: {}", 
                methodName, redisKey, duration, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 拦截RedisTemplate的ValueOperations操作
     */
    @Around("execution(* org.springframework.data.redis.core.ValueOperations.*(..))")
    public Object traceValueOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        return traceRedisDetailOperation(joinPoint, "ValueOps");
    }
    
    /**
     * 拦截RedisTemplate的HashOperations操作
     */
    @Around("execution(* org.springframework.data.redis.core.HashOperations.*(..))")
    public Object traceHashOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        return traceRedisDetailOperation(joinPoint, "HashOps");
    }
    
    /**
     * 拦截RedisTemplate的ListOperations操作
     */
    @Around("execution(* org.springframework.data.redis.core.ListOperations.*(..))")
    public Object traceListOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        return traceRedisDetailOperation(joinPoint, "ListOps");
    }
    
    /**
     * 拦截RedisTemplate的SetOperations操作
     */
    @Around("execution(* org.springframework.data.redis.core.SetOperations.*(..))")
    public Object traceSetOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        return traceRedisDetailOperation(joinPoint, "SetOps");
    }
    
    /**
     * 拦截RedisTemplate的ZSetOperations操作
     */
    @Around("execution(* org.springframework.data.redis.core.ZSetOperations.*(..))")
    public Object traceZSetOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        return traceRedisDetailOperation(joinPoint, "ZSetOps");
    }
    
    /**
     * 通用的Redis详细操作追踪
     */
    private Object traceRedisDetailOperation(ProceedingJoinPoint joinPoint, String operationType) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 获取Redis key
        String redisKey = extractRedisKey(args);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录Redis操作日志
            log.debug("Redis {} - Method: {}, Key: {}, Duration: {}ms", 
                operationType, methodName, redisKey, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Redis {} failed - Method: {}, Key: {}, Duration: {}ms, Error: {}", 
                operationType, methodName, redisKey, duration, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 提取Redis Key
     */
    private String extractRedisKey(Object[] args) {
        if (args != null && args.length > 0) {
            // 第一个参数通常是key
            Object firstArg = args[0];
            if (firstArg instanceof String) {
                return (String) firstArg;
            } else if (firstArg != null) {
                return firstArg.toString();
            }
        }
        return "unknown";
    }
} 