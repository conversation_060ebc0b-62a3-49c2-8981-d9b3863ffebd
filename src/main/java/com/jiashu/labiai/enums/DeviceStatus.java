package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 设备状态枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum DeviceStatus {
    NORMAL(1, "正常"),
    ABNORMAL(2, "异常"),
    LOCKED(3, "锁定"),
    BLACKLISTED(4, "拉黑"),
    DELETED(5, "删除");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    DeviceStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DeviceStatus fromCode(int code) {
        for (DeviceStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown device status code: " + code);
    }
} 