package com.jiashu.labiai.enums;

/**
 * 响应码枚举
 * 采用5位数字编码系统，便于分类管理和扩展
 */
public enum ResponseCode {
    
    // ================== 成功状态码 (200xx) ==================
    SUCCESS(20000, "操作成功"),
    CREATED(20001, "创建成功"),
    UPDATED(20002, "更新成功"),
    DELETED(20003, "删除成功"),
    
    // ================== 客户端错误 (400xx) ==================
    BAD_REQUEST(40000, "请求参数错误"),
    VALIDATION_ERROR(40001, "参数校验失败"),
    MISSING_PARAMETER(40002, "缺少必要参数"),
    INVALID_PARAMETER(40003, "参数格式不正确"),
    REQUEST_TOO_LARGE(40004, "请求体过大"),
    UNSUPPORTED_MEDIA_TYPE(40005, "不支持的媒体类型"),
    
    // ================== 认证错误 (401xx) ==================
    UNAUTHORIZED(40100, "未授权访问"),
    TOKEN_EXPIRED(40101, "Token已过期"),
    TOKEN_INVALID(40102, "Token无效"),
    TOKEN_MISSING(40103, "缺少Token"),
    LOGIN_REQUIRED(40104, "请先登录"),
    
    // ================== 授权错误 (403xx) ==================
    FORBIDDEN(40300, "访问被禁止"),
    INSUFFICIENT_PERMISSIONS(40301, "权限不足"),
    ACCOUNT_DISABLED(40302, "账号已被禁用"),
    ACCOUNT_LOCKED(40303, "账号已被锁定"),
    
    // ================== 资源错误 (404xx) ==================
    NOT_FOUND(40400, "资源不存在"),
    USER_NOT_FOUND(40401, "用户不存在"),
    DEVICE_NOT_FOUND(40402, "设备不存在"),
    ORDER_NOT_FOUND(40403, "订单不存在"),
    
    // ================== 冲突错误 (409xx) ==================
    CONFLICT(40900, "资源冲突"),
    EMAIL_ALREADY_EXISTS(40901, "邮箱已存在"),
    USERNAME_ALREADY_EXISTS(40902, "用户名已存在"),
    PHONE_ALREADY_EXISTS(40903, "手机号已存在"),
    
    // ================== 限流错误 (429xx) ==================
    TOO_MANY_REQUESTS(42900, "请求过于频繁"),
    LOGIN_ATTEMPTS_EXCEEDED(42901, "登录尝试次数过多"),
    SMS_SEND_LIMIT_EXCEEDED(42902, "短信发送次数超限"),
    API_RATE_LIMIT_EXCEEDED(42903, "API调用频率超限"),
    
    // ================== 服务端错误 (500xx) ==================
    INTERNAL_SERVER_ERROR(50000, "服务器内部错误"),
    DATABASE_ERROR(50001, "数据库错误"),
    CACHE_ERROR(50002, "缓存错误"),
    MESSAGE_QUEUE_ERROR(50003, "消息队列错误"),
    FILE_UPLOAD_ERROR(50004, "文件上传错误"),
    
    // ================== 业务错误 (600xx) ==================
    BUSINESS_ERROR(60000, "业务逻辑错误"),
    
    // 用户相关 (601xx)
    INVALID_CREDENTIALS(60101, "用户名或密码错误"),
    PASSWORD_TOO_WEAK(60102, "密码强度不足"),
    OLD_PASSWORD_INCORRECT(60103, "原密码不正确"),
    PASSWORD_NOT_MATCH(60104, "两次输入的密码不一致"),
    EMAIL_NOT_VERIFIED(60105, "邮箱未验证"),
    PHONE_NOT_VERIFIED(60106, "手机号未验证"),
    
    // 认证相关 (602xx)
    VERIFICATION_CODE_EXPIRED(60201, "验证码已过期"),
    VERIFICATION_CODE_INVALID(60202, "验证码错误"),
    VERIFICATION_CODE_USED(60203, "验证码已使用"),
    TWO_FACTOR_REQUIRED(60204, "需要双因子验证"),
    
    // 设备相关 (603xx)
    DEVICE_NOT_TRUSTED(60301, "设备未受信任"),
    DEVICE_LIMIT_EXCEEDED(60302, "设备数量超限"),
    DEVICE_ALREADY_TRUSTED(60303, "设备已受信任"),
    DEVICE_FINGERPRINT_MISMATCH(60304, "设备指纹不匹配"),
    DEVICE_FINGERPRINT_REQUIRED(60305, "缺少设备指纹"),
    DEVICE_BLACKLISTED(60306, "设备已被拉黑"),
    DEVICE_MISMATCH(60307, "设备不匹配"),
    
    // 会话相关 (604xx)
    SESSION_EXPIRED(60401, "会话已过期"),
    SESSION_INVALID(60402, "会话无效"),
    CONCURRENT_LOGIN_LIMIT_EXCEEDED(60403, "并发登录数量超限"),
    SESSION_KICKED_OUT(60404, "会话被强制下线"),
    NONCE_INVALID(60405, "nonce无效"),
    RATE_LIMIT_EXCEEDED(60406, "请求频率超限"),
    
    // ================== 安全错误 (700xx) ==================
    SECURITY_VIOLATION(70000, "安全违规"),
    SUSPICIOUS_ACTIVITY(70101, "检测到可疑活动"),
    LOCATION_CHANGE_DETECTED(70102, "检测到异地登录"),
    UNUSUAL_LOGIN_TIME(70103, "异常登录时间"),
    HIGH_RISK_LOGIN(70104, "高风险登录"),
    IP_BLACKLISTED(70105, "IP地址已被黑名单"),
    BRUTE_FORCE_ATTACK(70106, "检测到暴力破解攻击"),
    
    // ================== 第三方服务错误 (800xx) ==================
    THIRD_PARTY_SERVICE_ERROR(80000, "第三方服务错误"),
    OAUTH_SERVICE_ERROR(80001, "OAuth服务错误"),
    SMS_SERVICE_ERROR(80002, "短信服务错误"),
    EMAIL_SERVICE_ERROR(80003, "邮件服务错误"),
    PAYMENT_SERVICE_ERROR(80004, "支付服务错误"),
    
    // ================== 支付相关错误 (610xx) ==================
    // 套餐相关 (6101x)
    PACKAGE_NOT_FOUND(61010, "套餐不存在"),
    PACKAGE_DISABLED(61011, "套餐已禁用"),
    PACKAGE_PRICE_NOT_FOUND(61012, "套餐价格不存在"),
    
    // 订单相关 (6102x)
    ORDER_CREATE_FAILED(61020, "订单创建失败"),
    ORDER_STATUS_INVALID(61021, "订单状态无效"),
    ORDER_EXPIRED(61022, "订单已过期"),
    ORDER_AMOUNT_MISMATCH(61023, "订单金额不匹配"),
    ORDER_ALREADY_PAID(61024, "订单已支付"),
    
    // 支付相关 (6103x)
    PAYMENT_NOT_FOUND(61030, "支付记录不存在"),
    PAYMENT_CREATE_FAILED(61031, "支付创建失败"),
    PAYMENT_METHOD_NOT_FOUND(61032, "支付方式不存在"),
    PAYMENT_METHOD_DISABLED(61033, "支付方式已禁用"),
    PAYMENT_AMOUNT_INVALID(61034, "支付金额无效"),
    PAYMENT_AMOUNT_TOO_LOW(61035, "支付金额过低"),
    PAYMENT_AMOUNT_TOO_HIGH(61036, "支付金额过高"),
    PAYMENT_AMOUNT_EXCEEDS_LIMIT(61037, "支付金额超出限制"),
    PAYMENT_ALREADY_SUCCESS(61038, "支付已成功"),
    PAYMENT_CALLBACK_VERIFY_FAILED(61039, "支付回调验证失败"),
    PAYMENT_SIGNATURE_VERIFICATION_FAILED(61040, "支付签名验证失败"),
    PAYMENT_CALLBACK_INVALID(61041, "支付回调数据无效"),
    PAYMENT_CALLBACK_PROCESSING_FAILED(61042, "支付回调处理失败"),
    PAYMENT_PLATFORM_ERROR(61043, "支付平台错误"),
    
    // 优惠码相关 (6105x)
    DISCOUNT_CODE_NOT_FOUND(61050, "优惠码不存在"),
    DISCOUNT_CODE_EXPIRED(61051, "优惠码已过期"),
    DISCOUNT_CODE_USED_UP(61052, "优惠码已用完"),
    DISCOUNT_CODE_NOT_APPLICABLE(61053, "优惠码不适用"),
    DISCOUNT_CODE_MINIMUM_AMOUNT_NOT_MET(61054, "未达到优惠码最低使用金额"),
    
    // ================== 系统维护错误 (900xx) ==================
    SYSTEM_MAINTENANCE(90000, "系统维护中"),
    SERVICE_UNAVAILABLE(90001, "服务暂不可用"),
    SCHEDULED_MAINTENANCE(90002, "计划维护中");
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    ResponseCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据错误码获取枚举
     * @param code 错误码
     * @return ResponseCode枚举，如果不存在则返回null
     */
    public static ResponseCode fromCode(Integer code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为成功状态码
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return this.code >= 20000 && this.code < 30000;
    }
    
    /**
     * 判断是否为客户端错误
     * @return true表示客户端错误
     */
    public boolean isClientError() {
        return this.code >= 40000 && this.code < 50000;
    }
    
    /**
     * 判断是否为服务端错误
     * @return true表示服务端错误
     */
    public boolean isServerError() {
        return this.code >= 50000 && this.code < 60000;
    }
    
    /**
     * 判断是否为业务错误
     * @return true表示业务错误
     */
    public boolean isBusinessError() {
        return this.code >= 60000 && this.code < 70000;
    }
    
    /**
     * 判断是否为安全错误
     * @return true表示安全错误
     */
    public boolean isSecurityError() {
        return this.code >= 70000 && this.code < 80000;
    }
    
    @Override
    public String toString() {
        return "ResponseCode{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
} 