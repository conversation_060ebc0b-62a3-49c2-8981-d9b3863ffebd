package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 安全事件类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum SecurityEventType {
    
    // ==================== 设备相关事件 ====================
    
    /**
     * 设备ID不匹配
     */
    DEVICE_ID_MISMATCH("DEVICE_ID_MISMATCH", "设备ID不匹配", 3, "设备ID与登录时不一致，可能存在Token盗用风险"),
    
    /**
     * 设备指纹变化
     */
    DEVICE_HASH_MISMATCH("DEVICE_HASH_MISMATCH", "设备指纹变化", 2, "设备指纹与登录时不一致，可能存在环境篡改"),
    
    /**
     * 设备安全验证失败
     */
    DEVICE_SECURITY_VIOLATION("DEVICE_SECURITY_VIOLATION", "设备安全验证失败", 3, "设备安全验证失败，已强制下线"),
    
    /**
     * 拉黑设备访问尝试
     */
    BLACKLISTED_DEVICE_ACCESS("BLACKLISTED_DEVICE_ACCESS", "拉黑设备访问", 4, "被拉黑的设备尝试访问系统"),
    
    /**
     * 设备异常
     */
    DEVICE_ANOMALY("DEVICE_ANOMALY", "设备异常", 2, "设备行为异常"),
    
    // ==================== IP相关事件 ====================
    
    /**
     * IP地址变化（信任设备）
     */
    IP_CHANGE_TRUSTED_DEVICE("IP_CHANGE_TRUSTED_DEVICE", "IP变化(信任设备)", 1, "信任设备的IP地址发生变化"),
    
    /**
     * IP地址变化（非信任设备）
     */
    IP_CHANGE_UNTRUSTED_DEVICE("IP_CHANGE_UNTRUSTED_DEVICE", "IP变化(非信任设备)", 2, "非信任设备的IP地址发生变化"),
    
    /**
     * 可疑IP访问
     */
    SUSPICIOUS_IP_ACCESS("SUSPICIOUS_IP_ACCESS", "可疑IP访问", 3, "来自可疑IP地址的访问"),
    
    // ==================== 账户相关事件 ====================
    
    /**
     * 登录失败
     */
    LOGIN_FAILED("LOGIN_FAILED", "登录失败", 2, "用户登录失败"),
    
    /**
     * 多次登录失败
     */
    MULTIPLE_LOGIN_FAILURES("MULTIPLE_LOGIN_FAILURES", "多次登录失败", 3, "用户多次登录失败"),
    
    /**
     * 账户锁定
     */
    ACCOUNT_LOCKED("ACCOUNT_LOCKED", "账户锁定", 3, "用户账户被锁定"),
    
    /**
     * 账户解锁
     */
    ACCOUNT_UNLOCKED("ACCOUNT_UNLOCKED", "账户解锁", 2, "用户账户被解锁"),
    
    /**
     * 账户禁用
     */
    ACCOUNT_DISABLED("ACCOUNT_DISABLED", "账户禁用", 4, "用户账户被禁用"),
    
    /**
     * 密码重置
     */
    PASSWORD_RESET("PASSWORD_RESET", "密码重置", 2, "用户密码被重置"),
    
    /**
     * 密码修改
     */
    PASSWORD_CHANGED("PASSWORD_CHANGED", "密码修改", 2, "用户主动修改密码"),
    
    /**
     * 敏感操作
     */
    SENSITIVE_OPERATION("SENSITIVE_OPERATION", "敏感操作", 3, "用户执行敏感操作"),
    
    // ==================== 权限相关事件 ====================
    
    /**
     * 越权访问
     */
    UNAUTHORIZED_ACCESS("UNAUTHORIZED_ACCESS", "越权访问", 4, "尝试访问无权限的资源"),
    
    /**
     * 权限变更
     */
    PERMISSION_CHANGE("PERMISSION_CHANGE", "权限变更", 3, "用户权限发生变更"),
    
    // ==================== 其他事件 ====================
    
    /**
     * API滥用
     */
    API_ABUSE("API_ABUSE", "API滥用", 3, "检测到API滥用行为"),
    
    /**
     * 系统异常
     */
    SYSTEM_ANOMALY("SYSTEM_ANOMALY", "系统异常", 3, "检测到系统异常行为"),
    
    /**
     * 其他安全事件
     */
    OTHER_SECURITY_EVENT("OTHER_SECURITY_EVENT", "其他安全事件", 2, "其他类型的安全事件");
    
    /**
     * 事件代码
     */
    @EnumValue
    private final String code;
    
    /**
     * 事件名称
     */
    private final String name;
    
    /**
     * 事件级别: 1-信息 2-警告 3-危险 4-严重
     */
    private final int level;
    
    /**
     * 事件描述
     */
    private final String description;
    
    SecurityEventType(String code, String name, int level, String description) {
        this.code = code;
        this.name = name;
        this.level = level;
        this.description = description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static SecurityEventType fromCode(String code) {
        for (SecurityEventType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER_SECURITY_EVENT;
    }
} 