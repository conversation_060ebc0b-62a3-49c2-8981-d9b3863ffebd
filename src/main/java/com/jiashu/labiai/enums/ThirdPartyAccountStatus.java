package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 第三方账号状态枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ThirdPartyAccountStatus {
    NORMAL(1, "正常"),
    UNBOUND(2, "解绑"),
    ABNORMAL(3, "异常");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    ThirdPartyAccountStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ThirdPartyAccountStatus fromCode(int code) {
        for (ThirdPartyAccountStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown third party account status code: " + code);
    }
} 