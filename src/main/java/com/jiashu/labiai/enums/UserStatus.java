package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 用户状态枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum UserStatus {
    NORMAL(1, "正常"),
    LOCKED(2, "锁定"), 
    DISABLED(3, "禁用"),
    DELETED(4, "注销");
    
    @EnumValue  // MyBatis-Plus序列化到数据库的值
    private final int code;
    
    private final String description;
    
    UserStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserStatus fromCode(int code) {
        for (UserStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown user status code: " + code);
    }
} 