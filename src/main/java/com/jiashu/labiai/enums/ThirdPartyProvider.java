package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 第三方提供商枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ThirdPartyProvider {
    WECHAT("wechat", "微信"),
    GITHUB("github", "GitHub"),
    GOOGLE("google", "Google"),
    QQ("qq", "QQ"),
    WEIBO("weibo", "微博"),
    FACEBOOK("facebook", "Facebook"),
    TWITTER("twitter", "Twitter"),
    LINKEDIN("linkedin", "LinkedIn"),
    OTHER("other", "其他");
    
    @EnumValue
    private final String code;
    
    private final String description;
    
    ThirdPartyProvider(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @JsonValue
    public String getCode() {
        return code;
    }
    
    public static ThirdPartyProvider fromCode(String code) {
        for (ThirdPartyProvider provider : values()) {
            if (provider.code.equals(code)) {
                return provider;
            }
        }
        return OTHER;
    }
} 