package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 用户类型枚举
 */
@Getter
public enum UserType {
    NORMAL(1, "普通注册"),
    THIRD_PARTY(2, "第三方登录"),
    HYBRID(3, "混合账号");
    
    @EnumValue  // MyBatis-Plus序列化到数据库的值
    private final int code;
    
    private final String description;
    
    UserType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserType fromCode(int code) {
        for (UserType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown user type code: " + code);
    }
} 