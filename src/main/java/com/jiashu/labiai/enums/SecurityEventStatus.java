package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 安全事件状态枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum SecurityEventStatus {
    PENDING(1, "待处理"),
    HANDLED(2, "已处理"),
    IGNORED(3, "已忽略"),
    AUTO_RESOLVED(4, "自动解决");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    SecurityEventStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @JsonValue
    public int getCode() {
        return code;
    }
    
    public static SecurityEventStatus fromCode(int code) {
        for (SecurityEventStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PENDING;
    }
} 