package com.jiashu.labiai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    
    PENDING("PENDING", "待支付"),
    PAID("PAID", "已支付"),
    CANCELLED("CANC<PERSON>LED", "已取消"),
    REFUNDED("REFUNDED", "已退款"),
    PARTIAL_REFUNDED("PARTIAL_REFUNDED", "部分退款");
    
    private final String code;
    private final String description;
    
    public static OrderStatusEnum fromCode(String code) {
        for (OrderStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown order status code: " + code);
    }
} 