package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 安全事件级别枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum SecurityEventLevel {
    INFO(1, "信息"),
    WARNING(2, "警告"),
    DANGER(3, "危险"),
    CRITICAL(4, "严重");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    SecurityEventLevel(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @JsonValue
    public int getCode() {
        return code;
    }
    
    public static SecurityEventLevel fromCode(int code) {
        for (SecurityEventLevel level : values()) {
            if (level.code == code) {
                return level;
            }
        }
        return INFO;
    }
} 