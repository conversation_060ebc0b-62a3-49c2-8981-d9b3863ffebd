package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 登录类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum LoginType {
    PASSWORD(1, "密码登录"),
    WECHAT_SCAN(3, "微信扫码"),
    GITHUB_OAUTH(4, "GitHub OAuth"),
    GOOGLE_OAUTH(5, "Google OAuth"),
    TWO_FACTOR(6, "双因子登录");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    LoginType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static LoginType fromCode(int code) {
        for (LoginType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown login type code: " + code);
    }
} 