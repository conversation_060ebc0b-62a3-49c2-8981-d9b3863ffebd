package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 安全级别枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum SecurityLevel {
    NORMAL(1, "普通"),
    SENSITIVE(2, "敏感"),
    HIGH_RISK(3, "高危");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    SecurityLevel(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SecurityLevel fromCode(int code) {
        for (SecurityLevel level : values()) {
            if (level.code == code) {
                return level;
            }
        }
        throw new IllegalArgumentException("Unknown security level code: " + code);
    }
} 