package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 登录方式枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum LoginMethod {
    PASSWORD("password", "密码登录"),
    WECHAT("wechat", "微信登录"),
    GITHUB("github", "GitHub登录"),
    GOOGLE("google", "Google登录"),
    QQ("qq", "QQ登录"),
    WEIBO("weibo", "微博登录"),
    REMEMBER_ME("remember_me", "记住我登录"),
    TWO_FACTOR("two_factor", "双因子登录"),
    OTHER("other", "其他方式");
    
    @EnumValue
    private final String code;
    
    private final String description;
    
    LoginMethod(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @JsonValue
    public String getCode() {
        return code;
    }
    
    public static LoginMethod fromCode(String code) {
        for (LoginMethod method : values()) {
            if (method.code.equals(code)) {
                return method;
            }
        }
        return OTHER;
    }
} 