package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 会话状态枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum SessionStatus {
    ACTIVE(1, "活跃"),
    EXPIRED(2, "过期"),
    MANUAL_LOGOUT(3, "手动下线"),
    ABNORMAL_LOGOUT(4, "异常下线");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    SessionStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SessionStatus fromCode(int code) {
        for (SessionStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown session status code: " + code);
    }
} 