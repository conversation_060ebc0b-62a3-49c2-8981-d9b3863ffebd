package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 登录结果枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum LoginResult {
    SUCCESS(1, "成功"),
    PASSWORD_ERROR(2, "密码错误"),
    ACCOUNT_LOCKED(3, "账号锁定"),
    DEVICE_EXCEPTION(4, "设备异常"),
    LOCATION_EXCEPTION(5, "地域异常"),
    RATE_LIMITED(6, "频率限制"),
    THIRD_PARTY_AUTH_FAILED(7, "第三方授权失败"),
    ACCOUNT_NOT_BOUND(8, "账号未绑定"),
    ACCOUNT_NOT_FOUND(9, "账号不存在"),
    FAILED(10, "登录失败");
    
    @EnumValue
    private final int code;
    
    private final String description;
    
    LoginResult(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static LoginResult fromCode(int code) {
        for (LoginResult result : values()) {
            if (result.code == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("Unknown login result code: " + code);
    }
} 