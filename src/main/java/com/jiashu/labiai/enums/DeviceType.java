package com.jiashu.labiai.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 设备类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum DeviceType {
    DESKTOP("desktop", "桌面端"),
    MOBILE("mobile", "手机"),
    TABLET("tablet", "平板"),
    UNKNOWN("unknown", "未知设备");
    
    @EnumValue
    private final String code;
    
    private final String description;
    
    DeviceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DeviceType fromCode(String code) {
        for (DeviceType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown device type code: " + code);
    }
} 