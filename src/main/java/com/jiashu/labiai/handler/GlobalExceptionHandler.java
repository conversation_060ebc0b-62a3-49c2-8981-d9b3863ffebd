package com.jiashu.labiai.handler;

import cn.dev33.satoken.exception.NotLoginException;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.*;
import com.jiashu.labiai.exception.SecurityException;
import com.jiashu.labiai.trace.TraceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常，返回标准化的响应格式
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    // ================== 业务异常处理 ==================
    
    /**
     * 业务异常处理
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException e, HttpServletRequest request) {
        logException(e, request, "业务异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok(response);
    }

    @ExceptionHandler(NotLoginException.class)
    public ResponseEntity<ApiResponse<String>> handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        logException(e, request, "未登录异常");

        ApiResponse<String> response = ApiResponse.error(ResponseCode.LOGIN_REQUIRED, "请先登录")
                .withTraceId(getTraceId());
        return ResponseEntity.ok(response);
    }
    
    // ================== 支付系统异常处理 ==================
    
    /**
     * 支付异常处理
     */
    @ExceptionHandler(PaymentException.class)
    public ResponseEntity<ApiResponse<Object>> handlePaymentException(PaymentException e, HttpServletRequest request) {
        logException(e, request, "支付异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 支付方式异常处理
     */
    @ExceptionHandler(PaymentMethodException.class)
    public ResponseEntity<ApiResponse<Object>> handlePaymentMethodException(PaymentMethodException e, HttpServletRequest request) {
        logException(e, request, "支付方式异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok().body(response);
    }
    
    /**
     * 订单异常处理
     */
    @ExceptionHandler(OrderException.class)
    public ResponseEntity<ApiResponse<Object>> handleOrderException(OrderException e, HttpServletRequest request) {
        logException(e, request, "订单异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok().body(response);
    }
    
    /**
     * 套餐异常处理
     */
    @ExceptionHandler(PackageException.class)
    public ResponseEntity<ApiResponse<Object>> handlePackageException(PackageException e, HttpServletRequest request) {
        logException(e, request, "套餐异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 优惠码异常处理
     */
    @ExceptionHandler(DiscountCodeException.class)
    public ResponseEntity<ApiResponse<Object>> handleDiscountCodeException(DiscountCodeException e, HttpServletRequest request) {
        logException(e, request, "优惠码异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok().body(response);
    }
    
    /**
     * 支付回调异常处理
     */
    @ExceptionHandler(PaymentCallbackException.class)
    public ResponseEntity<ApiResponse<Object>> handlePaymentCallbackException(PaymentCallbackException e, HttpServletRequest request) {
        logException(e, request, "支付回调异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok().body(response);
    }
    
    /**
     * 订阅异常处理
     */
    @ExceptionHandler(SubscriptionException.class)
    public ResponseEntity<ApiResponse<Object>> handleSubscriptionException(SubscriptionException e, HttpServletRequest request) {
        logException(e, request, "订阅异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok().body(response);
    }
    
    /**
     * 认证异常处理
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        logException(e, request, "认证异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
    
    /**
     * 授权异常处理
     */
    @ExceptionHandler(AuthorizationException.class)
    public ResponseEntity<ApiResponse<Object>> handleAuthorizationException(AuthorizationException e, HttpServletRequest request) {
        logException(e, request, "授权异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
    
    /**
     * 验证异常处理
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(ValidationException e, HttpServletRequest request) {
        logException(e, request, "验证异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 资源不存在异常处理
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleResourceNotFoundException(ResourceNotFoundException e, HttpServletRequest request) {
        logException(e, request, "资源不存在异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    /**
     * 安全异常处理
     */
    @ExceptionHandler(SecurityException.class)
    public ResponseEntity<ApiResponse<Object>> handleSecurityException(SecurityException e, HttpServletRequest request) {
        logException(e, request, "安全异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
    
    /**
     * 限流异常处理
     */
    @ExceptionHandler(RateLimitException.class)
    public ResponseEntity<ApiResponse<Object>> handleRateLimitException(RateLimitException e, HttpServletRequest request) {
        logException(e, request, "限流异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body(response);
    }
    
    /**
     * 第三方服务异常处理
     */
    @ExceptionHandler(ThirdPartyServiceException.class)
    public ResponseEntity<ApiResponse<Object>> handleThirdPartyServiceException(ThirdPartyServiceException e, HttpServletRequest request) {
        logException(e, request, "第三方服务异常");
        
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData())
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(response);
    }
    
    // ================== Spring 框架异常处理 ==================
    
    /**
     * 参数校验异常处理（@Valid注解）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException e, HttpServletRequest request) {
        
        logException(e, request, "参数校验异常");
        
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        ApiResponse<Map<String, String>> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, errors)
                .withTraceId(getTraceId());
        
        return ResponseEntity.ok().body(response);
    }
    
    /**
     * 参数绑定异常处理
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleBindException(
            BindException e, HttpServletRequest request) {
        
        logException(e, request, "参数绑定异常");
        
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        ApiResponse<Map<String, String>> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, errors)
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 约束违反异常处理
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleConstraintViolationException(
            ConstraintViolationException e, HttpServletRequest request) {
        
        logException(e, request, "约束违反异常");
        
        Map<String, String> errors = new HashMap<>();
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            String fieldName = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            errors.put(fieldName, errorMessage);
        }
        
        ApiResponse<Map<String, String>> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, errors)
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 缺少请求参数异常处理
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException e, HttpServletRequest request) {
        
        logException(e, request, "缺少请求参数异常");
        
        String message = "缺少必要参数：" + e.getParameterName();
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.MISSING_PARAMETER.getCode(), message)
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 参数类型转换异常处理
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        
        logException(e, request, "参数类型转换异常");
        
        String message = "参数格式不正确：" + e.getName();
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.INVALID_PARAMETER.getCode(), message)
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * HTTP消息不可读异常处理
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException e, HttpServletRequest request) {
        
        logException(e, request, "HTTP消息不可读异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.BAD_REQUEST)
                .withTraceId(getTraceId());
        
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 不支持的HTTP方法异常处理
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpRequestMethodNotSupportedException(
            HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        
        logException(e, request, "不支持的HTTP方法异常");
        
        String message = "不支持的请求方法：" + e.getMethod();
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.BAD_REQUEST.getCode(), message)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }
    
    /**
     * 不支持的媒体类型异常处理
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpMediaTypeNotSupportedException(
            HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        
        logException(e, request, "不支持的媒体类型异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.UNSUPPORTED_MEDIA_TYPE)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(response);
    }
    
    /**
     * 文件上传大小超限异常处理
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<Object>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException e, HttpServletRequest request) {
        
        logException(e, request, "文件上传大小超限异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.REQUEST_TOO_LARGE)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }
    
    /**
     * 404异常处理
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleNoHandlerFoundException(
            NoHandlerFoundException e, HttpServletRequest request) {
        
        logException(e, request, "404异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.NOT_FOUND)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    // ================== 数据库异常处理 ==================
    
    /**
     * 数据库访问异常处理
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataAccessException(
            DataAccessException e, HttpServletRequest request) {
        
        logException(e, request, "数据库访问异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.DATABASE_ERROR)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    // ================== 通用异常处理 ==================
    
    /**
     * 运行时异常处理
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(
            RuntimeException e, HttpServletRequest request) {
        
        logException(e, request, "运行时异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 全局异常处理
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleException(
            Exception e, HttpServletRequest request) {
        
        logException(e, request, "未知异常");
        
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR)
                .withTraceId(getTraceId());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    // ================== 私有方法 ==================
    
    /**
     * 记录异常日志
     */
    private void logException(Exception e, HttpServletRequest request, String exceptionType) {
        String traceId = getTraceId();
        String uri = request.getRequestURI();
        String method = request.getMethod();
        String queryString = request.getQueryString();
        String url = queryString != null ? uri + "?" + queryString : uri;
        
        logger.error("{}发生：{} {} - TraceId: {} - Exception: {}", 
                exceptionType, method, url, traceId, e.getClass().getSimpleName(), e);
    }
    
    /**
     * 获取链路追踪ID
     */
    private String getTraceId() {
        String traceId = TraceContext.getTraceId();
        return traceId != null ? traceId : "unknown";
    }
} 