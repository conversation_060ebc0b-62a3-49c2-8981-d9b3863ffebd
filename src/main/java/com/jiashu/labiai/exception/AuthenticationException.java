package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 认证异常
 * 用于处理用户身份认证相关的异常
 */
public class AuthenticationException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public AuthenticationException(String message) {
        super(ResponseCode.UNAUTHORIZED.getCode(), message);
    }
    
    public AuthenticationException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public AuthenticationException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public AuthenticationException(String message, Throwable cause) {
        super(ResponseCode.UNAUTHORIZED.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 无效的登录凭据
     */
    public static AuthenticationException invalidCredentials() {
        return new AuthenticationException(ResponseCode.INVALID_CREDENTIALS);
    }
    
    /**
     * Token已过期
     */
    public static AuthenticationException tokenExpired() {
        return new AuthenticationException(ResponseCode.TOKEN_EXPIRED);
    }
    
    /**
     * Token无效
     */
    public static AuthenticationException tokenInvalid() {
        return new AuthenticationException(ResponseCode.TOKEN_INVALID);
    }
    
    /**
     * 缺少Token
     */
    public static AuthenticationException tokenMissing() {
        return new AuthenticationException(ResponseCode.TOKEN_MISSING);
    }
    
    /**
     * 需要登录
     */
    public static AuthenticationException loginRequired() {
        return new AuthenticationException(ResponseCode.LOGIN_REQUIRED);
    }
    
    /**
     * 未授权访问
     */
    public static AuthenticationException unauthorized() {
        return new AuthenticationException(ResponseCode.UNAUTHORIZED);
    }
    
    /**
     * 验证码错误
     */
    public static AuthenticationException verificationCodeInvalid() {
        return new AuthenticationException(ResponseCode.VERIFICATION_CODE_INVALID);
    }
    
    /**
     * 验证码已过期
     */
    public static AuthenticationException verificationCodeExpired() {
        return new AuthenticationException(ResponseCode.VERIFICATION_CODE_EXPIRED);
    }
    
    /**
     * 需要双因子验证
     */
    public static AuthenticationException twoFactorRequired() {
        return new AuthenticationException(ResponseCode.TWO_FACTOR_REQUIRED);
    }
} 