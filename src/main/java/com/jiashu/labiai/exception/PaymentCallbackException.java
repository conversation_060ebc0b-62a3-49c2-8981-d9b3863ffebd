package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.util.TraceUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付回调异常
 * 当支付回调处理失败时抛出
 */
public class PaymentCallbackException extends PaymentException {
    
    private static final long serialVersionUID = 1L;
    
    public PaymentCallbackException(String message) {
        super(message);
    }
    
    public PaymentCallbackException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public PaymentCallbackException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public PaymentCallbackException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    public static PaymentCallbackException of(ResponseCode responseCode) {
        return new PaymentCallbackException(responseCode);
    }
    
    public static PaymentCallbackException signatureVerificationFailed(String provider) {
        return new PaymentCallbackException(ResponseCode.PAYMENT_SIGNATURE_VERIFICATION_FAILED, provider);
    }
    
    public static PaymentCallbackException invalidCallbackData(String provider, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("provider", provider);
        details.put("reason", reason);
        return new PaymentCallbackException(ResponseCode.PAYMENT_CALLBACK_INVALID, details);
    }
    
    public static PaymentCallbackException callbackProcessingFailed(String tradeOrderId, String reason) {
        Map<String, Object> details = new HashMap<>();
        details.put("tradeOrderId", tradeOrderId);
        details.put("reason", reason);
        return new PaymentCallbackException(ResponseCode.PAYMENT_CALLBACK_PROCESSING_FAILED, details);
    }

    /**
     * 验证失败
     */
    public static PaymentCallbackException verifyFailed(String provider, String reason) {
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("provider", provider);
        errorDetails.put("reason", reason);
        
        PaymentCallbackException exception = new PaymentCallbackException("支付回调验证失败: " + reason);
        
        TraceUtils.recordError("PAYMENT_CALLBACK_VERIFY_FAILED", errorDetails);
        return exception;
    }

    /**
     * 支付记录未找到
     */
    public static PaymentCallbackException paymentNotFound(String tradeOrderId, String reason) {
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("tradeOrderId", tradeOrderId);
        errorDetails.put("reason", reason);
        
        PaymentCallbackException exception = new PaymentCallbackException("支付记录未找到: " + tradeOrderId);
        
        TraceUtils.recordError("PAYMENT_CALLBACK_NOT_FOUND", errorDetails);
        return exception;
    }

    /**
     * 支付记录未找到 - 重载方法
     */
    public static PaymentCallbackException paymentNotFound(String tradeOrderId) {
        return paymentNotFound(tradeOrderId, "未找到对应的支付记录");
    }
} 