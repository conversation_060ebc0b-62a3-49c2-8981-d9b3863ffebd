package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务异常基类
 * 所有业务相关的异常都应该继承此类
 */
@Setter
@Getter
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误数据（可选）
     */
    private Object data;
    
    // ================== 构造函数 ==================
    
    public BusinessException(String message) {
        super(message);
        this.code = ResponseCode.INTERNAL_SERVER_ERROR.getCode();
    }
    
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }
    
    public BusinessException(ResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
    }
    
    public BusinessException(ResponseCode responseCode, Object data) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.data = data;
    }
    
    public BusinessException(Integer code, String message, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }
    
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResponseCode.INTERNAL_SERVER_ERROR.getCode();
    }
    
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    public BusinessException(ResponseCode responseCode, Throwable cause) {
        super(responseCode.getMessage(), cause);
        this.code = responseCode.getCode();
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 创建业务异常
     * @param responseCode 响应码枚举
     * @return BusinessException实例
     */
    public static BusinessException of(ResponseCode responseCode) {
        return new BusinessException(responseCode);
    }
    
    /**
     * 创建业务异常
     * @param responseCode 响应码枚举
     * @param data 错误数据
     * @return BusinessException实例
     */
    public static BusinessException of(ResponseCode responseCode, Object data) {
        return new BusinessException(responseCode, data);
    }
    
    /**
     * 创建业务异常
     * @param code 错误码
     * @param message 错误消息
     * @return BusinessException实例
     */
    public static BusinessException of(Integer code, String message) {
        return new BusinessException(code, message);
    }
    
    /**
     * 创建业务异常
     * @param code 错误码
     * @param message 错误消息
     * @param data 错误数据
     * @return BusinessException实例
     */
    public static BusinessException of(Integer code, String message, Object data) {
        return new BusinessException(code, message, data);
    }
    
    // ================== Getter/Setter ==================

    @Override
    public String toString() {
        return "BusinessException{" +
                "code=" + code +
                ", message='" + getMessage() + '\'' +
                ", data=" + data +
                '}';
    }
} 