package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 订阅异常类
 * 处理用户订阅相关的业务异常
 */
public class SubscriptionException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    // ================== 构造函数 ==================
    
    public SubscriptionException(String message) {
        super(message);
    }
    
    public SubscriptionException(Integer code, String message) {
        super(code, message);
    }
    
    public SubscriptionException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public SubscriptionException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public SubscriptionException(Integer code, String message, Object data) {
        super(code, message, data);
    }
    
    public SubscriptionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public SubscriptionException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    public SubscriptionException(ResponseCode responseCode, Throwable cause) {
        super(responseCode, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 创建订阅异常
     * @param message 错误消息
     * @return SubscriptionException实例
     */
    public static SubscriptionException of(String message) {
        return new SubscriptionException(message);
    }
    
    /**
     * 创建订阅异常
     * @param responseCode 响应码枚举
     * @return SubscriptionException实例
     */
    public static SubscriptionException of(ResponseCode responseCode) {
        return new SubscriptionException(responseCode);
    }
    
    /**
     * 创建订阅异常
     * @param responseCode 响应码枚举
     * @param data 错误数据
     * @return SubscriptionException实例
     */
    public static SubscriptionException of(ResponseCode responseCode, Object data) {
        return new SubscriptionException(responseCode, data);
    }
    
    /**
     * 创建订阅异常
     * @param code 错误码
     * @param message 错误消息
     * @return SubscriptionException实例
     */
    public static SubscriptionException of(Integer code, String message) {
        return new SubscriptionException(code, message);
    }
    
    /**
     * 创建订阅异常
     * @param code 错误码
     * @param message 错误消息
     * @param data 错误数据
     * @return SubscriptionException实例
     */
    public static SubscriptionException of(Integer code, String message, Object data) {
        return new SubscriptionException(code, message, data);
    }
    
    // ================== 常用订阅异常静态方法 ==================
    
    /**
     * 套餐价格不存在异常
     */
    public static SubscriptionException packagePriceNotFound() {
        return SubscriptionException.of(404, "套餐价格信息不存在");
    }
    
    /**
     * 套餐不存在异常
     */
    public static SubscriptionException packageNotFound() {
        return SubscriptionException.of(404, "套餐信息不存在");
    }
    
    /**
     * 订阅不存在异常
     */
    public static SubscriptionException subscriptionNotFound() {
        return SubscriptionException.of(404, "用户订阅不存在");
    }
    
    /**
     * 创建订阅失败异常
     */
    public static SubscriptionException createFailed(String reason) {
        return SubscriptionException.of(500, "创建订阅失败：" + reason);
    }
    
    /**
     * 续费订阅失败异常
     */
    public static SubscriptionException renewFailed(String reason) {
        return SubscriptionException.of(500, "续费订阅失败：" + reason);
    }
    
    /**
     * 升级订阅失败异常
     */
    public static SubscriptionException upgradeFailed(String reason) {
        return SubscriptionException.of(500, "升级订阅失败：" + reason);
    }
    
    /**
     * 取消订阅失败异常
     */
    public static SubscriptionException cancelFailed(String reason) {
        return SubscriptionException.of(500, "取消订阅失败：" + reason);
    }
    
    /**
     * 订阅已过期异常
     */
    public static SubscriptionException expired() {
        return SubscriptionException.of(400, "订阅已过期");
    }
    
    /**
     * 订阅状态异常
     */
    public static SubscriptionException invalidStatus(String currentStatus) {
        return SubscriptionException.of(400, "订阅状态异常：" + currentStatus);
    }
} 