package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

import java.util.Map;

/**
 * 验证异常
 * 用于处理参数校验和数据验证相关的异常
 */
public class ValidationException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public ValidationException(String message) {
        super(ResponseCode.VALIDATION_ERROR.getCode(), message);
    }
    
    public ValidationException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public ValidationException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public ValidationException(String message, Object data) {
        super(ResponseCode.VALIDATION_ERROR.getCode(), message, data);
    }
    
    public ValidationException(String message, Throwable cause) {
        super(ResponseCode.VALIDATION_ERROR.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 参数校验失败
     */
    public static ValidationException validationError() {
        return new ValidationException(ResponseCode.VALIDATION_ERROR);
    }
    
    /**
     * 参数校验失败（带错误详情）
     * @param errors 校验错误详情
     */
    public static ValidationException validationError(Map<String, String> errors) {
        return new ValidationException(ResponseCode.VALIDATION_ERROR, errors);
    }
    
    /**
     * 缺少必要参数
     * @param parameterName 参数名
     */
    public static ValidationException missingParameter(String parameterName) {
        return new ValidationException("缺少必要参数：" + parameterName);
    }
    
    /**
     * 参数格式不正确
     * @param parameterName 参数名
     */
    public static ValidationException invalidParameter(String parameterName) {
        return new ValidationException("参数格式不正确：" + parameterName);
    }
    
    /**
     * 参数格式不正确（带具体值）
     * @param parameterName 参数名
     * @param value 参数值
     */
    public static ValidationException invalidParameter(String parameterName, Object value) {
        return new ValidationException("参数格式不正确：" + parameterName + "=" + value);
    }
    
    /**
     * 请求体过大
     */
    public static ValidationException requestTooLarge() {
        return new ValidationException(ResponseCode.REQUEST_TOO_LARGE);
    }
    
    /**
     * 不支持的媒体类型
     */
    public static ValidationException unsupportedMediaType() {
        return new ValidationException(ResponseCode.UNSUPPORTED_MEDIA_TYPE);
    }
    
    /**
     * 自定义验证错误
     * @param message 错误消息
     * @param fieldErrors 字段错误详情
     */
    public static ValidationException of(String message, Map<String, String> fieldErrors) {
        return new ValidationException(message, fieldErrors);
    }
} 