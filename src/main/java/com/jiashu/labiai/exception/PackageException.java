package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 套餐异常
 * 当套餐不存在、套餐价格不存在等情况时抛出
 */
public class PackageException extends PaymentException {
    
    private static final long serialVersionUID = 1L;
    
    public PackageException(String message) {
        super(message);
    }
    
    public PackageException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public PackageException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public PackageException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    public static PackageException of(ResponseCode responseCode) {
        return new PackageException(responseCode);
    }
    
    public static PackageException packageNotFound(Long packageId) {
        return new PackageException(ResponseCode.PACKAGE_NOT_FOUND, packageId);
    }
    
    public static PackageException packageDisabled(Long packageId) {
        return new PackageException(ResponseCode.PACKAGE_DISABLED, packageId);
    }
    
    public static PackageException priceNotFound(Long priceId) {
        return new PackageException(ResponseCode.PACKAGE_PRICE_NOT_FOUND, priceId);
    }
} 