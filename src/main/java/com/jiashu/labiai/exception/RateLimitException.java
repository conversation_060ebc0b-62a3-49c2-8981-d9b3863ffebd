package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 限流异常
 * 用于处理频率限制相关的异常
 */
public class RateLimitException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public RateLimitException(String message) {
        super(ResponseCode.TOO_MANY_REQUESTS.getCode(), message);
    }
    
    public RateLimitException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public RateLimitException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public RateLimitException(String message, Throwable cause) {
        super(ResponseCode.TOO_MANY_REQUESTS.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 请求过于频繁
     */
    public static RateLimitException tooManyRequests() {
        return new RateLimitException(ResponseCode.TOO_MANY_REQUESTS);
    }
    
    /**
     * 登录尝试次数过多
     */
    public static RateLimitException loginAttemptsExceeded() {
        return new RateLimitException(ResponseCode.LOGIN_ATTEMPTS_EXCEEDED);
    }
    
    /**
     * 短信发送次数超限
     */
    public static RateLimitException smsSendLimitExceeded() {
        return new RateLimitException(ResponseCode.SMS_SEND_LIMIT_EXCEEDED);
    }
    
    /**
     * API调用频率超限
     */
    public static RateLimitException apiRateLimitExceeded() {
        return new RateLimitException(ResponseCode.API_RATE_LIMIT_EXCEEDED);
    }
    
    /**
     * 自定义限流异常
     * @param operation 操作类型
     * @param retryAfter 重试间隔（秒）
     */
    public static RateLimitException of(String operation, Integer retryAfter) {
        RateLimitException exception = new RateLimitException(operation + "操作过于频繁，请稍后再试");
        if (retryAfter != null) {
            exception.setData(retryAfter);
        }
        return exception;
    }
} 