package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 资源不存在异常
 * 用于处理资源未找到的异常
 */
public class ResourceNotFoundException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public ResourceNotFoundException(String message) {
        super(ResponseCode.NOT_FOUND.getCode(), message);
    }
    
    public ResourceNotFoundException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public ResourceNotFoundException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public ResourceNotFoundException(String message, Throwable cause) {
        super(ResponseCode.NOT_FOUND.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 资源不存在（通用）
     */
    public static ResourceNotFoundException notFound() {
        return new ResourceNotFoundException(ResponseCode.NOT_FOUND);
    }
    
    /**
     * 用户不存在
     * @param userId 用户ID
     */
    public static ResourceNotFoundException user(Object userId) {
        return new ResourceNotFoundException("用户不存在，ID：" + userId);
    }
    
    /**
     * 设备不存在
     * @param deviceId 设备ID
     */
    public static ResourceNotFoundException device(Object deviceId) {
        return new ResourceNotFoundException("设备不存在，ID：" + deviceId);
    }
    
    /**
     * 订单不存在
     * @param orderId 订单ID
     */
    public static ResourceNotFoundException order(Object orderId) {
        return new ResourceNotFoundException("订单不存在，ID：" + orderId);
    }
    
    /**
     * 自定义资源不存在
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     */
    public static ResourceNotFoundException resource(String resourceType, Object resourceId) {
        return new ResourceNotFoundException(resourceType + "不存在，ID：" + resourceId);
    }
    
    /**
     * 自定义资源不存在（仅消息）
     * @param message 自定义消息
     */
    public static ResourceNotFoundException of(String message) {
        return new ResourceNotFoundException(message);
    }
} 