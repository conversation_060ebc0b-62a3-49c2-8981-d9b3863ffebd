package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 授权异常
 * 用于处理权限和访问控制相关的异常
 */
public class AuthorizationException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public AuthorizationException(String message) {
        super(ResponseCode.FORBIDDEN.getCode(), message);
    }
    
    public AuthorizationException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public AuthorizationException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public AuthorizationException(String message, Throwable cause) {
        super(ResponseCode.FORBIDDEN.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 访问被禁止
     */
    public static AuthorizationException forbidden() {
        return new AuthorizationException(ResponseCode.FORBIDDEN);
    }
    
    /**
     * 权限不足
     */
    public static AuthorizationException insufficientPermissions() {
        return new AuthorizationException(ResponseCode.INSUFFICIENT_PERMISSIONS);
    }
    
    /**
     * 账号已被禁用
     */
    public static AuthorizationException accountDisabled() {
        return new AuthorizationException(ResponseCode.ACCOUNT_DISABLED);
    }
    
    /**
     * 账号已被锁定
     */
    public static AuthorizationException accountLocked() {
        return new AuthorizationException(ResponseCode.ACCOUNT_LOCKED);
    }
    
    /**
     * 自定义权限不足异常
     * @param permission 缺少的权限
     */
    public static AuthorizationException insufficientPermissions(String permission) {
        return new AuthorizationException("权限不足，需要权限：" + permission);
    }
} 