package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

import java.util.HashMap;
import java.util.Map;

/**
 * 优惠码异常
 * 当优惠码不存在、已过期、已达到使用上限等情况时抛出
 */
public class DiscountCodeException extends PaymentException {
    
    private static final long serialVersionUID = 1L;
    
    public DiscountCodeException(String message) {
        super(message);
    }
    
    public DiscountCodeException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public DiscountCodeException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public DiscountCodeException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    public static DiscountCodeException of(ResponseCode responseCode) {
        return new DiscountCodeException(responseCode);
    }
    
    public static DiscountCodeException codeNotFound(String discountCode) {
        return new DiscountCodeException(ResponseCode.DISCOUNT_CODE_NOT_FOUND, discountCode);
    }
    
    public static DiscountCodeException codeExpired(String discountCode) {
        return new DiscountCodeException(ResponseCode.DISCOUNT_CODE_EXPIRED, discountCode);
    }
    
    public static DiscountCodeException codeUsedUp(String discountCode) {
        return new DiscountCodeException(ResponseCode.DISCOUNT_CODE_USED_UP, discountCode);
    }
    
    public static DiscountCodeException minimumAmountNotMet(String discountCode, java.math.BigDecimal minAmount) {
        Map<String, Object> amountDetails = new HashMap<>();
        amountDetails.put("discountCode", discountCode);
        amountDetails.put("minAmount", minAmount);
        return new DiscountCodeException(ResponseCode.DISCOUNT_CODE_MINIMUM_AMOUNT_NOT_MET, amountDetails);
    }
    
    public static DiscountCodeException notApplicableToPackage(String discountCode, Long packageId) {
        Map<String, Object> packageDetails = new HashMap<>();
        packageDetails.put("discountCode", discountCode);
        packageDetails.put("packageId", packageId);
        return new DiscountCodeException(ResponseCode.DISCOUNT_CODE_NOT_APPLICABLE, packageDetails);
    }
} 