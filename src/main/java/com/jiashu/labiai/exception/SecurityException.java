package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 安全异常
 * 用于处理安全相关的异常
 */
public class SecurityException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public SecurityException(String message) {
        super(ResponseCode.SECURITY_VIOLATION.getCode(), message);
    }
    
    public SecurityException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public SecurityException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public SecurityException(String message, Throwable cause) {
        super(ResponseCode.SECURITY_VIOLATION.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 安全违规
     */
    public static SecurityException securityViolation() {
        return new SecurityException(ResponseCode.SECURITY_VIOLATION);
    }
    
    /**
     * 可疑活动
     * @param activity 活动描述
     */
    public static SecurityException suspiciousActivity(String activity) {
        return new SecurityException("检测到可疑活动：" + activity);
    }
    
    /**
     * 检测到异地登录
     */
    public static SecurityException locationChangeDetected() {
        return new SecurityException(ResponseCode.LOCATION_CHANGE_DETECTED);
    }
    
    /**
     * 高风险登录
     * @param reason 风险原因
     */
    public static SecurityException highRiskLogin(String reason) {
        return new SecurityException("高风险登录：" + reason);
    }
    
    /**
     * IP地址已被黑名单
     */
    public static SecurityException ipBlacklisted() {
        return new SecurityException(ResponseCode.IP_BLACKLISTED);
    }
    
    /**
     * 设备指纹不匹配
     */
    public static SecurityException deviceFingerprintMismatch() {
        return new SecurityException(ResponseCode.DEVICE_FINGERPRINT_MISMATCH);
    }
    
    /**
     * 检测到暴力破解攻击
     */
    public static SecurityException bruteForceAttack() {
        return new SecurityException(ResponseCode.BRUTE_FORCE_ATTACK);
    }
    
    /**
     * 异常登录时间
     */
    public static SecurityException unusualLoginTime() {
        return new SecurityException(ResponseCode.UNUSUAL_LOGIN_TIME);
    }
} 