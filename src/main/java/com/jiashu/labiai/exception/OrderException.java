package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单异常
 * 当订单状态不正确、订单不存在等情况时抛出
 */
public class OrderException extends PaymentException {
    
    private static final long serialVersionUID = 1L;
    
    public OrderException(String message) {
        super(message);
    }
    
    public OrderException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public OrderException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public OrderException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    public static OrderException of(ResponseCode responseCode) {
        return new OrderException(responseCode);
    }
    
    public static OrderException orderNotFound(Object orderId) {
        return new OrderException(ResponseCode.ORDER_NOT_FOUND, orderId);
    }
    
    public static OrderException orderStatusInvalid(String orderNo, String currentStatus) {
        Map<String, Object> statusDetails = new HashMap<>();
        statusDetails.put("orderNo", orderNo);
        statusDetails.put("currentStatus", currentStatus);
        return new OrderException(ResponseCode.ORDER_STATUS_INVALID, statusDetails);
    }
    
    public static OrderException orderExpired(String orderNo) {
        return new OrderException(ResponseCode.ORDER_EXPIRED, orderNo);
    }
    
    public static OrderException orderAlreadyPaid(String orderNo) {
        return new OrderException(ResponseCode.ORDER_ALREADY_PAID, orderNo);
    }
} 