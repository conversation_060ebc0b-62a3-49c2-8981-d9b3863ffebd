package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 支付方式异常
 * 当支付方式不可用、配置错误等情况时抛出
 */
public class PaymentMethodException extends PaymentException {
    
    private static final long serialVersionUID = 1L;
    
    public PaymentMethodException(String message) {
        super(message);
    }
    
    public PaymentMethodException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public PaymentMethodException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public PaymentMethodException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    public static PaymentMethodException of(ResponseCode responseCode) {
        return new PaymentMethodException(responseCode);
    }
    
    public static PaymentMethodException methodNotFound(String methodCode) {
        return new PaymentMethodException(ResponseCode.PAYMENT_METHOD_NOT_FOUND, methodCode);
    }
    
    public static PaymentMethodException methodDisabled(String methodCode) {
        return new PaymentMethodException(ResponseCode.PAYMENT_METHOD_DISABLED, methodCode);
    }
    
    public static PaymentMethodException amountExceedsLimit(String methodCode, Object data) {
        return new PaymentMethodException(ResponseCode.PAYMENT_AMOUNT_EXCEEDS_LIMIT, data);
    }
} 