package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 支付异常基类
 * 所有支付相关的异常都应该继承此类
 */
public class PaymentException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public PaymentException(String message) {
        super(message);
    }
    
    public PaymentException(Integer code, String message) {
        super(code, message);
    }
    
    public PaymentException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public PaymentException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public PaymentException(Integer code, String message, Object data) {
        super(code, message, data);
    }
    
    public PaymentException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public PaymentException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    public PaymentException(ResponseCode responseCode, Throwable cause) {
        super(responseCode, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    public static PaymentException of(ResponseCode responseCode) {
        return new PaymentException(responseCode);
    }
    
    public static PaymentException of(ResponseCode responseCode, Object data) {
        return new PaymentException(responseCode, data);
    }
    
    public static PaymentException of(Integer code, String message) {
        return new PaymentException(code, message);
    }
    
    public static PaymentException of(Integer code, String message, Object data) {
        return new PaymentException(code, message, data);
    }
} 