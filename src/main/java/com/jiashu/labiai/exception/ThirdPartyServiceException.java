package com.jiashu.labiai.exception;

import com.jiashu.labiai.enums.ResponseCode;

/**
 * 第三方服务异常
 * 用于处理外部服务调用相关的异常
 */
public class ThirdPartyServiceException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    public ThirdPartyServiceException(String message) {
        super(ResponseCode.THIRD_PARTY_SERVICE_ERROR.getCode(), message);
    }
    
    public ThirdPartyServiceException(ResponseCode responseCode) {
        super(responseCode);
    }
    
    public ThirdPartyServiceException(ResponseCode responseCode, Object data) {
        super(responseCode, data);
    }
    
    public ThirdPartyServiceException(String message, Throwable cause) {
        super(ResponseCode.THIRD_PARTY_SERVICE_ERROR.getCode(), message, cause);
    }
    
    // ================== 静态工厂方法 ==================
    
    /**
     * 第三方服务错误（通用）
     */
    public static ThirdPartyServiceException serviceError() {
        return new ThirdPartyServiceException(ResponseCode.THIRD_PARTY_SERVICE_ERROR);
    }
    
    /**
     * OAuth服务错误
     */
    public static ThirdPartyServiceException oauthServiceError() {
        return new ThirdPartyServiceException(ResponseCode.OAUTH_SERVICE_ERROR);
    }
    
    /**
     * 短信服务错误
     */
    public static ThirdPartyServiceException smsServiceError() {
        return new ThirdPartyServiceException(ResponseCode.SMS_SERVICE_ERROR);
    }
    
    /**
     * 邮件服务错误
     */
    public static ThirdPartyServiceException emailServiceError() {
        return new ThirdPartyServiceException(ResponseCode.EMAIL_SERVICE_ERROR);
    }
    
    /**
     * 支付服务错误
     */
    public static ThirdPartyServiceException paymentServiceError() {
        return new ThirdPartyServiceException(ResponseCode.PAYMENT_SERVICE_ERROR);
    }
    
    /**
     * 自定义第三方服务错误
     * @param serviceName 服务名称
     * @param errorMessage 错误信息
     */
    public static ThirdPartyServiceException of(String serviceName, String errorMessage) {
        return new ThirdPartyServiceException(serviceName + "服务错误：" + errorMessage);
    }
    
    /**
     * 自定义第三方服务错误（带异常原因）
     * @param serviceName 服务名称
     * @param errorMessage 错误信息
     * @param cause 异常原因
     */
    public static ThirdPartyServiceException of(String serviceName, String errorMessage, Throwable cause) {
        return new ThirdPartyServiceException(serviceName + "服务错误：" + errorMessage, cause);
    }
} 