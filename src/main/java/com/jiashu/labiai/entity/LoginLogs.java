package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiashu.labiai.enums.LoginType;
import com.jiashu.labiai.enums.LoginResult;
import com.jiashu.labiai.enums.LoginMethod;
import com.jiashu.labiai.enums.ThirdPartyProvider;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@TableName("login_logs")
public class LoginLogs {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("email")
    private String email;

    @TableField("device_id")
    private String deviceId;

    @TableField("device_hash")
    private String deviceHash;

    @TableField("device_name")
    private String deviceName;

    @TableField("ip_address")
    private String ipAddress;

    @TableField("user_agent")
    private String userAgent;

    @TableField("location")
    private String location;

    /**
     * 网络运营商
     */
    @TableField("isp")
    private String isp;

    /**
     * 1:密码登录 2:记住我登录 3:微信扫码 4:GitHub OAuth 5:Google OAuth 6:双因子登录
     */
    @TableField("login_type")
    private LoginType loginType;

    /**
     * password/wechat/github/google/oauth
     */
    @TableField("login_method")
    private LoginMethod loginMethod;

    /**
     * 第三方登录提供商（预留）
     */
    @TableField("third_party_provider")
    private ThirdPartyProvider thirdPartyProvider;

    /**
     * 1:成功 2:密码错误 3:账号锁定 4:设备异常 5:地域异常 6:频率限制 7:第三方授权失败 8:账号未绑定
     */
    @TableField("login_result")
    private LoginResult loginResult;

    @TableField("failure_reason")
    private String failureReason;

    /**
     * Sa-Token值
     */
    @TableField("satoken_value")
    private String satokenValue;

    /**
     * 会话超时时间(秒)
     */
    @TableField("session_timeout")
    private Long sessionTimeout;

    /**
     * OAuth状态参数
     */
    @TableField("oauth_state")
    private String oauthState;

    /**
     * OAuth授权码
     */
    @TableField("oauth_code")
    private String oauthCode;

    /**
     * 第三方平台用户ID
     */
    @TableField("third_party_user_id")
    private String thirdPartyUserId;

    /**
     * 本次登录风险评分
     */
    @TableField("risk_score")
    private Integer riskScore;

    /**
     * 触发的安全动作
     */
    @TableField("security_actions")
    private String securityActions;

    /**
     * 客户端版本
     */
    @TableField("client_version")
    private String clientVersion;

    /**
     * API版本
     */
    @TableField("api_version")
    private String apiVersion;

    /**
     * 来源页面
     */
    @TableField("referrer")
    private String referrer;

    @TableField("created_at")
    private LocalDateTime createdAt;
}
