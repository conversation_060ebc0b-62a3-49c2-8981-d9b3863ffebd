package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiashu.labiai.enums.LoginMethod;
import com.jiashu.labiai.enums.SessionStatus;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@TableName("satoken_sessions")
public class SatokenSessions {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Sa-Token值
     */
    @TableField("token_value")
    private String tokenValue;

    /**
     * 登录ID(用户ID)
     */
    @TableField("login_id")
    private String loginId;

    /**
     * 登录类型
     */
    @TableField("login_type")
    private String loginType;

    /**
     * 设备类型标识
     */
    @TableField("device_type")
    private String deviceType;

    @TableField("user_id")
    private Long userId;

    @TableField("device_id")
    private String deviceId;

    @TableField("device_hash")
    private String deviceHash;

    @TableField("device_name")
    private String deviceName;

    @TableField("ip_address")
    private String ipAddress;

    @TableField("location")
    private String location;

    @TableField("user_agent")
    private String userAgent;

    /**
     * 登录方式：password/wechat/github/google
     */
    @TableField("login_method")
    private LoginMethod loginMethod;

    @TableField("login_time")
    private LocalDateTime loginTime;

    @TableField("last_activity_at")
    private LocalDateTime lastActivityAt;

    /**
     * Token超时时间(秒)
     */
    @TableField("timeout_seconds")
    private Long timeoutSeconds;

    @TableField("expires_at")
    private LocalDateTime expiresAt;

    @TableField("is_active")
    private Boolean isActive;

    /**
     * 1:活跃 2:过期 3:手动下线 4:异常下线
     */
    @TableField("status")
    private SessionStatus status;

    @TableField("trust_level")
    private Integer trustLevel;

    @TableField("risk_score")
    private Integer riskScore;

    /**
     * 安全标记位
     */
    @TableField("security_flags")
    private String securityFlags;

    /**
     * 扩展数据
     */
    @TableField("extra_data")
    private String extraData;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
