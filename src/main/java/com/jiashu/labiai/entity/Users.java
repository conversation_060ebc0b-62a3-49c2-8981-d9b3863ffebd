package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiashu.labiai.enums.LoginMethod;
import com.jiashu.labiai.enums.SecurityLevel;
import com.jiashu.labiai.enums.UserStatus;
import com.jiashu.labiai.enums.UserType;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@TableName("users")
public class Users {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邮箱，第三方登录用户可能为空
     */
    @TableField("email")
    private String email;

    @TableField("nickname")
    private String nickname;

    @TableField("avatar")
    private String avatar;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码哈希，第三方登录用户可为空
     */
    @TableField("password_hash")
    private String passwordHash;


    /**
     * 1:普通注册 2:第三方登录 3:混合账号
     */
    @TableField("user_type")
    private UserType userType;

    /**
     * 主要登录方式
     */
    @TableField("primary_login_type")
    private LoginMethod primaryLoginType;

    /**
     * 账号来源：registration/wechat/github/google等
     */
    @TableField("account_source")
    private String accountSource;

    /**
     * 1:正常 2:锁定 3:禁用 4:注销
     */
    @TableField("status")
    private UserStatus status;

    /**
     * 邮箱是否已验证
     */
    @TableField("email_verified")
    private Boolean emailVerified;

    /**
     * 手机是否已验证
     */
    @TableField("phone_verified")
    private Boolean phoneVerified;


    /**
     * 锁定截止时间
     */
    @TableField("locked_until")
    private LocalDateTime lockedUntil;

    @TableField("last_login_at")
    private LocalDateTime lastLoginAt;

    @TableField("last_login_ip")
    private String lastLoginIp;

    @TableField("last_login_device_id")
    private String lastLoginDeviceId;

    /**
     * 最后登录方式：password/wechat/github/google
     */
    @TableField("last_login_type")
    private LoginMethod lastLoginType;

    /**
     * 1:普通 2:敏感 3:高危
     */
    @TableField("security_level")
    private SecurityLevel securityLevel;

    @TableField("two_factor_enabled")
    private Boolean twoFactorEnabled;

    /**
     * 登录通知开关
     */
    @TableField("login_notification_enabled")
    private Boolean loginNotificationEnabled;

    /**
     * 个人资料是否公开
     */
    @TableField("profile_public")
    private Boolean profilePublic;

    /**
     * 是否允许绑定第三方账号
     */
    @TableField("allow_third_party_bind")
    private Boolean allowThirdPartyBind;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @TableField("created_by")
    private Long createdBy;

    @TableField("updated_by")
    private Long updatedBy;

    private Integer deleted;
}
