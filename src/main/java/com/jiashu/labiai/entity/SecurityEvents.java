package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiashu.labiai.enums.SecurityEventLevel;
import com.jiashu.labiai.enums.SecurityEventStatus;
import com.jiashu.labiai.enums.LoginMethod;
import com.jiashu.labiai.enums.ThirdPartyProvider;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 安全事件实体类
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@TableName("security_events")
public class SecurityEvents {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 事件类型代码
     */
    @TableField("event_type")
    private String eventType;
    
    /**
     * 事件类型名称
     */
    @TableField("event_type_name")
    private String eventTypeName;

    /**
     * 事件级别: INFO(1), WARNING(2), DANGER(3), CRITICAL(4)
     */
    @TableField("event_level")
    private SecurityEventLevel eventLevel;

    @TableField("event_title")
    private String eventTitle;

    @TableField("event_description")
    private String eventDescription;

    @TableField("user_id")
    private Long userId;

    @TableField("device_id")
    private String deviceId;

    @TableField("session_token")
    private String sessionToken;

    @TableField("ip_address")
    private String ipAddress;

    /**
     * 触发事件的登录方式
     */
    @TableField("login_method")
    private LoginMethod loginMethod;

    /**
     * 相关第三方提供商
     */
    @TableField("third_party_provider")
    private ThirdPartyProvider thirdPartyProvider;

    /**
     * 事件详细数据
     */
    @TableField("event_data")
    private String eventData;

    /**
     * 触发的规则
     */
    @TableField("triggered_rules")
    private String triggeredRules;

    /**
     * 事件状态: PENDING(1), HANDLED(2), IGNORED(3), AUTO_RESOLVED(4)
     */
    @TableField("status")
    private SecurityEventStatus status;

    /**
     * 处理人
     */
    @TableField("handled_by")
    private Long handledBy;

    @TableField("handled_at")
    private LocalDateTime handledAt;

    /**
     * 处理备注
     */
    @TableField("handle_notes")
    private String handleNotes;

    @TableField("created_at")
    private LocalDateTime createdAt;
}
