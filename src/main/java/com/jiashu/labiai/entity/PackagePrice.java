package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 套餐价格表
 */
@Data
@TableName("package_prices")
public class PackagePrice {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 套餐ID
     */
    private Long packageId;
    
    /**
     * 计费周期
     */
    private String billingCycle;
    
    /**
     * 周期数量，如3个月则为3
     */
    private Integer cycleCount;
    
    /**
     * 原价
     */
    private BigDecimal originalPrice;
    
    /**
     * 售价
     */
    private BigDecimal salePrice;
    
    /**
     * 货币类型
     */
    private String currency;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 