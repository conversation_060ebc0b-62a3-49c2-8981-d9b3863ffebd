package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiashu.labiai.enums.ThirdPartyProvider;
import com.jiashu.labiai.enums.ThirdPartyAccountStatus;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@TableName("user_third_party_accounts")
public class UserThirdPartyAccounts {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 第三方平台：wechat/github/google/qq等
     */
    @TableField("provider")
    private ThirdPartyProvider provider;

    /**
     * 第三方平台用户ID
     */
    @TableField("provider_user_id")
    private String providerUserId;

    /**
     * 第三方平台用户名
     */
    @TableField("provider_username")
    private String providerUsername;

    /**
     * 第三方平台邮箱
     */
    @TableField("provider_email")
    private String providerEmail;

    /**
     * 第三方平台头像
     */
    @TableField("provider_avatar")
    private String providerAvatar;

    /**
     * 绑定时间
     */
    @TableField("bind_time")
    private LocalDateTime bindTime;

    /**
     * 最后使用该方式登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 使用该方式登录次数
     */
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 第三方平台完整用户信息
     */
    @TableField("provider_profile")
    private String providerProfile;

    /**
     * 1:正常 2:解绑 3:异常
     */
    @TableField("status")
    private ThirdPartyAccountStatus status;

    /**
     * 是否为主要登录方式
     */
    @TableField("is_primary")
    private Boolean isPrimary;

    /**
     * 访问令牌
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @TableField("token_expires_at")
    private LocalDateTime tokenExpiresAt;

    /**
     * 授权范围
     */
    @TableField("scope")
    private String scope;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
