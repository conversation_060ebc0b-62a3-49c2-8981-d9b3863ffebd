package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠码实体类
 */
@Data
@TableName("discount_codes")
public class DiscountCode {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 优惠码
     */
    private String code;
    
    /**
     * 优惠码名称
     */
    private String name;
    
    /**
     * 优惠类型：FIXED-固定金额，PERCENTAGE-百分比
     */
    private String type;
    
    /**
     * 优惠值
     */
    private BigDecimal value;
    
    /**
     * 使用类型：SINGLE-一码一用，MULTIPLE-一码多用
     */
    private String usageType;
    
    /**
     * 最大使用次数
     */
    private Integer maxUsage;
    
    /**
     * 已使用次数
     */
    private Integer usedCount;
    
    /**
     * 最小使用金额
     */
    private BigDecimal minAmount;
    
    /**
     * 最大优惠金额(百分比折扣用)
     */
    private BigDecimal maxDiscount;
    
    /**
     * 适用套餐ID数组，null表示全部
     */
    @TableField(typeHandler = com.jiashu.labiai.handler.JsonListTypeHandler.class)
    private List<Long> applicablePackages;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 状态 1:启用 0:禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 