package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订阅历史表
 */
@Data
@TableName("subscription_history")
public class SubscriptionHistory {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 订阅ID
     */
    private Long subscriptionId;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 操作类型：CREATE-创建，RENEW-续费，UPGRADE-升级，DOWNGRADE-降级，CANCEL-取消
     */
    private String action;
    
    /**
     * 原套餐ID（升级降级时）
     */
    private Long fromPackageId;
    
    /**
     * 目标套餐ID
     */
    private Long toPackageId;
    
    /**
     * 原订阅结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fromEndTime;
    
    /**
     * 新订阅结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime toEndTime;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
} 