package com.jiashu.labiai.entity;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiashu.labiai.handler.JSONObjectTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付方式表
 */
@Data
@TableName("payment_methods")
public class PaymentMethod {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 支付方式代码
     */
    private String methodCode;
    
    /**
     * 支付方式名称
     */
    private String methodName;
    
    /**
     * 支付类型
     */
    private String methodType;
    
    /**
     * 支付提供商
     */
    private String provider;
    
    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * 支付API配置
     */
    @TableField(typeHandler = JSONObjectTypeHandler.class)
    private JSONObject apiConfig;
    
    /**
     * 回调配置
     */
    @TableField(typeHandler = JSONObjectTypeHandler.class)
    private JSONObject webhookConfig;
    
    /**
     * 额外配置参数
     */
    @TableField(typeHandler = JSONObjectTypeHandler.class)
    private JSONObject extraConfig;
    
    /**
     * 最小支付金额
     */
    private BigDecimal minAmount;
    
    /**
     * 最大支付金额
     */
    private BigDecimal maxAmount;
    
    /**
     * 状态 1:启用 0:禁用
     */
    private Integer status;
    
    /**
     * 优先级，数字越大优先级越高
     */
    private Integer priority;
    
    /**
     * 显示排序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 