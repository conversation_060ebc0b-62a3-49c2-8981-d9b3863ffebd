package com.jiashu.labiai.entity;

import com.jiashu.labiai.enums.LoginResult;
import com.jiashu.labiai.enums.LoginType;
import com.jiashu.labiai.util.DeviceInfo;
import lombok.*;

import java.util.Date;

/**
 * 登录上下文信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LoginContext {
    
    /**
     * 设备ID (来自FingerprintJS)
     */
    private String deviceId;

    private String email;
    
    /**
     * 设备指纹哈希
     */
    private String deviceHash;
    
    /**
     * IP地址
     */
    private String ipAddress;

    private DeviceInfo deviceInfo;

    /**
     * 登录时间
     */
    private Date loginTime;


    private boolean rememberMe;

    private LoginType loginType;

    private LoginResult loginResult;
} 