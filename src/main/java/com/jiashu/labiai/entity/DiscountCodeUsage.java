package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠码使用记录实体类
 */
@Data
@TableName("discount_code_usage")
public class DiscountCodeUsage {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 优惠码ID
     */
    private Long discountCodeId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 使用状态：USED-已使用，CANCELLED-已取消（订单超时）
     */
    private String status;
    
    /**
     * 使用时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime usedAt;
} 