package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付记录表
 */
@Data
@TableName("payments")
public class Payment {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 支付流水号
     */
    private String paymentNo;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 支付方式ID
     */
    private Long paymentMethodId;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 货币类型
     */
    private String currency;
    
    /**
     * 商户订单号(发给支付平台)
     */
    private String tradeOrderId;
    
    /**
     * 支付平台交易号
     */
    private String transactionId;
    
    /**
     * 第三方平台订单号
     */
    private String platformOrderId;
    
    /**
     * 支付跳转URL
     */
    private String paymentUrl;
    
    /**
     * 二维码URL
     */
    private String qrCodeUrl;
    
    /**
     * 支付状态
     */
    private String status;
    
    /**
     * 失败原因
     */
    private String failureReason;
    
    /**
     * 支付回调原始数据
     */
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private Map<String, Object> callbackData;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 支付完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidAt;
    
    /**
     * 支付过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiredAt;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 