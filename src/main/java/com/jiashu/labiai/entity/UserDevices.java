package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiashu.labiai.enums.DeviceType;
import com.jiashu.labiai.enums.DeviceStatus;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@TableName("user_devices")
public class UserDevices {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    /**
     * visitorId from FingerprintJS
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * SHA-256 of device components
     */
    @TableField("device_hash")
    private String deviceHash;

    /**
     * 用户自定义设备名称
     */
    @TableField("device_name")
    private String deviceName;

    @TableField("device_type")
    private DeviceType deviceType;

    /**
     * 操作系统
     */
    @TableField("platform")
    private String platform;

    /**
     * 浏览器
     */
    @TableField("browser")
    private String browser;


    /**
     * 是否为受信任设备
     */
    @TableField("is_trusted")
    private Boolean isTrusted;

    /**
     * 信任度评分: 0-100
     */
    @TableField("trust_level")
    private Integer trustLevel;

    /**
     * 信任原因
     */
    @TableField("trust_reason")
    private String trustReason;

    @TableField("first_login_at")
    private LocalDateTime firstLoginAt;

    @TableField("last_login_at")
    private LocalDateTime lastLoginAt;

    @TableField("last_login_ip")
    private String lastLoginIp;

    @TableField("login_count")
    private Integer loginCount;

    /**
     * 最后登录地理位置
     */
    @TableField("last_location")
    private String lastLocation;

    /**
     * 位置历史记录
     */
    @TableField("location_history")
    private String locationHistory;

    /**
     * 风险评分: 0-100
     */
    @TableField("risk_score")
    private Integer riskScore;

    /**
     * 风险因素列表
     */
    @TableField("risk_factors")
    private String riskFactors;

    /**
     * 1:正常 2:异常 3:锁定 4:删除
     */
    @TableField("status")
    private DeviceStatus status;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
