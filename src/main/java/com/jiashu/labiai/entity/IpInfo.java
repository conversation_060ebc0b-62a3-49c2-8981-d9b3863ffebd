package com.jiashu.labiai.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

/**
 * ip 地理位置
 */
@Getter
@Setter
public class IpInfo {


  /**
   * ip 地址
   */
  private String ip;

    /**
     * 大洲
     */
    private String continent;
  
  /**
   * 国家
   */
  private String country;
  
  /**
   * 省份
   */
  private String province;
  
  /**
   * 城市
   */
  private String city;
  
  /**
   * 区域
   */
  private String area;
  
  /**
   * 运营商
   */
  private String isp;

    /**
     * 所属机构
     */
    private String owner;

    /**
     * 网络类型（保留原有字段）
   */
  private String net;

    /**
     * 邮编
     */
    private String zipcode;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 精度
     */
    private String accuracy;

  @Override
  public String toString() {
   StringBuilder sb = new StringBuilder();

   if(StringUtils.hasText(country)){
     sb.append(country);
   }
      if (StringUtils.hasText(province)) {
          sb.append(province);
      }
      if (StringUtils.hasText(city)) {
          sb.append(city);
      }

      if (StringUtils.hasText(area)) {
          sb.append(area);
      }
      if (StringUtils.hasText(isp)) {
          sb.append(isp);
      }
      return sb.toString().trim();
  }
}
