package com.jiashu.labiai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 套餐表
 */
@Data
@TableName("packages")
public class Package {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 套餐名称(basic/plus/pro)
     */
    private String name;
    
    /**
     * 显示名称
     */
    private String displayName;
    
    /**
     * 套餐描述
     */
    private String description;
    
    /**
     * 功能特性列表
     */
    @TableField(typeHandler = com.jiashu.labiai.handler.JsonListTypeHandler.class)
    private List<String> features;
    
    /**
     * 状态 1:启用 0:禁用
     */
    private Integer status;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 