package com.jiashu.labiai.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.jiashu.labiai.entity.IpInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * IP工具类
 */
@Slf4j
@Component
public class IpUtil {
    private static final String IP_URL = "https://openapi.chinaz.net/v1/1007/district";
    private static final String API_KEY = "apiuser_quantity_c87f10e6394685d67a9401f5f5b97db5_07c1273dd8534fe283b851a3c5b8ee50";
    private static final String CHINAZ_VER = "1.0";
    /**
     * 业务级错误码
     */
    private static final int STATE_CODE_ERROR = -1;   // 系统异常
    private static final int STATE_CODE_INFO = 0;     // 获取数据状态信息
    private static final int STATE_CODE_SUCCESS = 1;  // 成功
    /**
     * 系统级错误码
     */
    private static final int HTTP_API_KEY_MISSING = 431;    // APIKey缺失
    private static final int HTTP_API_KEY_LENGTH_ERROR = 432;    // 错误的APIkey长度
    private static final int HTTP_API_KEY_ERROR = 433;    // 错误的请求key
    private static final int HTTP_API_KEY_TYPE_ERROR = 434;    // 错误的APIkey类型
    private static final int HTTP_API_KEY_INSUFFICIENT = 436;    // 额度不足或APIKey不存在
    private static final int HTTP_IP_NOT_WHITELIST = 437;    // 该IP未在白名单内
    private static final int HTTP_REQUEST_TOO_FREQUENT = 531;    // 请求过于频繁
    private static final int HTTP_UNKNOWN_ERROR = 532;    // 未知错误
    private static RedisTemplate<String, IpInfo> ipInfoRedisTemplate;


    
    /**
     * 验证IP地址是否有效
     */
    private boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) 
            && !"unknown".equalsIgnoreCase(ip)
            && !"0:0:0:0:0:0:0:1".equals(ip)
            && !"127.0.0.1".equals(ip);
    }
    
    /**
     * 判断是否为内网IP
     */
    public boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }
        
        // IPv6本地地址
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return true;
        }
        
        // IPv4本地地址
        if ("127.0.0.1".equals(ip) || "localhost".equals(ip)) {
            return true;
        }
        
        // 私有IP段
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);
            
            // 10.0.0.0/8
            if (first == 10) {
                return true;
            }
            
            // **********/12
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }
            
            // ***********/16
            if (first == 192 && second == 168) {
                return true;
            }
            
        } catch (NumberFormatException e) {
            return false;
        }
        
        return false;
    }

    public static IpInfo parseIp(String ip) {
        return parseIpFromChinaNetWeb(ip);
    }

    /**
     * 解析ip地址 从 <a href="https://www.chinaz.net">chinaz.net</a> 获取
     *
     * @param ip ip地址
     * @return IpInfo
     */
    private static IpInfo parseIpFromChinaNetWeb(String ip) {
        // 如果不是ipv4地址，直接返回
        if (!ip.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            return null;
        }

        // 从缓存中获取
        IpInfo ipInfoCache = (IpInfo) ipInfoRedisTemplate.opsForHash().get("ip_info_set", ip);
        if (ipInfoCache != null) {
            return ipInfoCache;
        }

        IpInfo ipInfo = new IpInfo();
        ipInfo.setIp(ip);

        try {
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("ip", ip);
            params.put("APIKey", API_KEY);
            params.put("ChinazVer", CHINAZ_VER);
            params.put("coordsys", "WGS84");
            params.put("area", "multi");

            HttpResponse response = HttpRequest.get(IP_URL)
                    .form(params)
                    .execute();

            if (response.isOk()) {
                String body = response.body();
                JSONObject jsonObject = new JSONObject(body);
                int stateCode = jsonObject.getInt("StateCode");
                String reason = jsonObject.getStr("Reason");

                // 处理业务级错误码
                if (stateCode != STATE_CODE_SUCCESS) {
                    log.warn("从 web 解析ip地址失败，ip 地址为：{} StateCode: {}, Reason: {}",
                            ip, stateCode, reason);

                    if (stateCode == STATE_CODE_ERROR) {
                        log.error("系统异常：ip={}, Reason={}", ip, reason);
                    } else if (stateCode == STATE_CODE_INFO) {
                        log.info("获取数据状态信息：ip={}, Reason={}", ip, reason);
                    }
                    return null;
                }

                try {
                    JSONObject result = jsonObject.getJSONObject("Result");
                    if (result == null) {
                        return null;
                    }

                    JSONObject data = result.getJSONObject("data");
                    if (data == null) {
                        return null;
                    }

                    String country = data.getStr("country");
                    String continent = data.getStr("continent");
                    String isp = data.getStr("isp");
                    String owner = data.getStr("owner");
                    String zipcode = data.getStr("zipcode");
                    String timezone = data.getStr("timezone");
                    String accuracy = data.getStr("accuracy");

                    ipInfo.setCountry(country);
                    ipInfo.setContinent(continent);
                    ipInfo.setIsp(isp);
                    ipInfo.setOwner(owner);
                    ipInfo.setZipcode(zipcode);
                    ipInfo.setTimezone(timezone);
                    ipInfo.setAccuracy(accuracy);

                    String province = data.getStr("prov");
                    String city = data.getStr("city");
                    String district = data.getStr("district");

                    ipInfo.setProvince(province);
                    ipInfo.setCity(city);
                    ipInfo.setArea(district);

                    // 解析多区域信息
                    JSONArray multiAreas = data.getJSONArray("multiAreas");
                    if (multiAreas != null && !multiAreas.isEmpty()) {
                        JSONObject areaInfo = multiAreas.getJSONObject(0);
                        province = areaInfo.getStr("prov");
                        city = areaInfo.getStr("city");
                        district = areaInfo.getStr("district");
                        ipInfo.setProvince(province);
                        ipInfo.setCity(city);
                        ipInfo.setArea(district);
                    }

                    // 存入Redis缓存
                    ipInfoRedisTemplate.opsForHash().put("ip_info_set", ip, ipInfo);
                    return ipInfo;
                } catch (Exception e) {
                    log.warn("从 web 解析ip地址失败，ip 地址为：{}, exception: {}", ip, e.getMessage());
                }
            } else {
                // 处理系统级错误码
                int statusCode = response.getStatus();
                log.warn("ip 解析请求发起失败，ip 地址为：{}, 状态码: {}", ip, statusCode);

                switch (statusCode) {
                    case HTTP_API_KEY_MISSING:
                        log.error("APIKey缺失，请附带APIKey参数");
                        break;
                    case HTTP_API_KEY_LENGTH_ERROR:
                        log.error("错误的APIkey长度");
                        break;
                    case HTTP_API_KEY_ERROR:
                        log.error("错误的请求key");
                        break;
                    case HTTP_API_KEY_TYPE_ERROR:
                        log.error("错误的APIkey类型");
                        break;
                    case HTTP_API_KEY_INSUFFICIENT:
                        log.error("额度不足，或者APIKey不存在");
                        break;
                    case HTTP_IP_NOT_WHITELIST:
                        log.error("该IP未在白名单内");
                        break;
                    case HTTP_REQUEST_TOO_FREQUENT:
                        log.error("请求过于频繁");
                        break;
                    case HTTP_UNKNOWN_ERROR:
                    case 533:
                        log.error("未知错误，请联系管理员处理");
                        break;
                    default:
                        log.error("未知HTTP错误，状态码：{}", statusCode);
                }
            }
        } catch (Exception e) {
            log.error("解析IP地址时发生异常，ip: {}, 错误: {}", ip, e.getMessage());
            return null;
        }
        return null;
    }

    @Autowired
    public void setIpInfoRedisTemplate(RedisTemplate<String, IpInfo> ipInfoRedisTemplate) {
        IpUtil.ipInfoRedisTemplate = ipInfoRedisTemplate;
    }
}
