package com.jiashu.labiai.util;

import cn.hutool.core.util.IdUtil;
import com.jiashu.labiai.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * 链路追踪工具类
 */
@Slf4j
@Component
public class TraceUtils {

    /**
     * 执行带链路追踪的操作
     */
    public static <T> T executeWithTrace(String operationName, Supplier<T> supplier) {
        String originalTraceId = TraceContext.getTraceId();
        String traceId = originalTraceId != null ? originalTraceId : generateTraceId();

        try {
            long startTime = System.currentTimeMillis();
            log.debug("开始执行操作: {} [traceId={}]", operationName, traceId);

            T result = supplier.get();

            long duration = System.currentTimeMillis() - startTime;
            log.debug("操作执行完成: {} [traceId={}, 耗时={}ms]", operationName, traceId, duration);

            return result;
        } catch (Exception e) {
            log.error("操作执行异常: {} [traceId={}]", operationName, traceId, e);
            throw e;
        }
    }

    /**
     * 记录业务事件
     */
    public static void recordBusinessEvent(String eventType, Object details) {
        String traceId = TraceContext.getTraceId();
        log.info("业务事件 [traceId={}, event={}, details={}]", traceId, eventType, details);
    }

    /**
     * 记录错误信息
     */
    public static void recordError(String errorCode, Object details) {
        String traceId = TraceContext.getTraceId();
        log.error("错误记录 [traceId={}, errorCode={}, details={}]", traceId, errorCode, details);
    }

    public static void recordError(String errorType, String errorMessage, Throwable throwable) {
        String traceId = TraceContext.getTraceId();
        log.error("Error occurred - Type: {}, Message: {}, TraceId: {}", errorType, errorMessage, traceId, throwable);
    }

    /**
     * 生成链路追踪ID
     */
    public static String generateTraceId() {
        return IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 检查是否在链路追踪上下文中
     */
    public static boolean isInTraceContext() {
        return TraceContext.getTraceId() != null;
    }
}