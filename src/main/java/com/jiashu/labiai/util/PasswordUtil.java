package com.jiashu.labiai.util;

import cn.hutool.crypto.digest.DigestUtil;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码工具类
 */
@Component
public class PasswordUtil {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    /**
     * 生成盐值
     */
    public String generateSalt() {
        byte[] salt = new byte[16];
        SECURE_RANDOM.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    public String hashPassword(String password) {
        return DigestUtil.md5Hex(password);
    }

    /**
     * 加密密码
     */
    public String hashPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(salt.getBytes(StandardCharsets.UTF_8));
            byte[] hashedPassword = md.digest(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to hash password", e);
        }
    }

    /**
     * 验证密码
     */
    public boolean verifyPassword(String password, String salt, String hashedPassword) {
        String hashToCheck = hashPassword(password, salt);
        return hashToCheck.equals(hashedPassword);
    }
} 