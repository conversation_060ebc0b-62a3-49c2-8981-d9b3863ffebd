package com.jiashu.labiai.util;

import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 虎皮椒支付工具类
 */
@Slf4j
public class XunhupayUtils {

    /**
     * 生成虎皮椒支付签名
     * 算法：参数按key的ASCII码排序，拼接成key1=value1&key2=value2格式，最后加上appsecret，MD5加密
     */
    public static String generateSign(Map<String, Object> params, String appSecret) {
        // 过滤掉hash参数和空值
//        String paramString = params.entrySet().stream()
//            .filter(entry -> !"hash".equals(entry.getKey()))
//            .filter(entry -> entry.getValue() != null && !entry.getValue().toString().isEmpty())
//            .sorted(Map.Entry.comparingByKey()) // 按key排序
//            .map(entry -> entry.getKey() + "=" + entry.getValue())
//            .collect(Collectors.joining("&"));
//
//        // 拼接appsecret
//        String stringToSign = paramString + appSecret;
//
//        // MD5加密，返回小写
//        String sign = DigestUtil.md5Hex(stringToSign).toLowerCase();
        StringBuilder sb = new StringBuilder();
        params.entrySet().stream().sorted((e1, e2) -> e1.getKey().compareTo(e2.getKey())).forEach(a -> {
            sb.append(a).append("&");
        });

        // 去除 最后一位的 &
        sb.deleteCharAt(sb.length() - 1);
        // 拼接上密钥
        sb.append(appSecret);

        // 调用 Hutool 的 加密工具 进行 MD5 加密

//        log.debug("虎皮椒签名计算: paramString={}, stringToSign={}, sign={}",
//            paramString, stringToSign, sign);

        return SecureUtil.md5(sb.toString());
    }

    /**
     * 验证虎皮椒回调签名
     */
    public static boolean verifySign(Map<String, String> params, String appSecret) {
        String receivedHash = params.get("hash");
        if (receivedHash == null || receivedHash.isEmpty()) {
            log.warn("虎皮椒回调缺少hash参数");
            return false;
        }

        // 重新计算签名
        Map<String, Object> signParams = new HashMap<>();
        params.forEach((key, value) -> {
            if (!"hash".equals(key) && value != null && !value.isEmpty()) {
                signParams.put(key, value);
            }
        });

        String calculatedHash = generateSign(signParams, appSecret);
        boolean isValid = receivedHash.equals(calculatedHash);

        if (!isValid) {
            log.warn("虎皮椒回调签名验证失败: received={}, calculated={}",
                    receivedHash, calculatedHash);
        }

        return isValid;
    }

    /**
     * 获取当前时间戳（秒）
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis() / 1000;
    }

    public static int getSecondTimestamp(Date date) {
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime());
        int length = timestamp.length();
        if (length > 3) {
            return Integer.valueOf(timestamp.substring(0, length - 3));
        } else {
            return 0;
        }
    }

    /**
     * 生成随机字符串
     */
    public static String generateNonceStr(int length) {
        String chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }

    /**
     * 生成随机数字字符串
     */
    public static String generateRandomNumber(int length) {
        String chars = "0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }
} 