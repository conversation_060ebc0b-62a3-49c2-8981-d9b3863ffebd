package com.jiashu.labiai.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 设备安全工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceSecurityUtil {
    
    // 可疑User-Agent模式
    private static final List<Pattern> SUSPICIOUS_USER_AGENTS = Arrays.asList(
        Pattern.compile("(?i)bot|crawler|spider|scraper"),
        Pattern.compile("(?i)python|curl|wget|http"),
        Pattern.compile("(?i)automated|script|test")
    );
    
    // 高风险IP范围（这里只是示例，实际应该使用更全面的威胁情报）
    private static final Set<String> HIGH_RISK_IP_RANGES = new HashSet<>(Arrays.asList(
        "10.0.0.0/8",
        "***********/16",
        "127.0.0.1/32"
    ));
    
    /**
     * 计算设备风险评分
     */
    public DeviceRiskAssessment assessDeviceRisk(DeviceRiskContext context) {
        DeviceRiskAssessment assessment = new DeviceRiskAssessment();
        List<RiskFactor> riskFactors = new ArrayList<>();
        int totalRiskScore = 0;
        
        // 1. 设备指纹一致性检查
        RiskFactor fingerprintRisk = assessFingerprintConsistency(context);
        if (fingerprintRisk != null) {
            riskFactors.add(fingerprintRisk);
            totalRiskScore += fingerprintRisk.getScore();
        }
        
        // 2. IP地址风险评估
        RiskFactor ipRisk = assessIPRisk(context);
        if (ipRisk != null) {
            riskFactors.add(ipRisk);
            totalRiskScore += ipRisk.getScore();
        }
        
        // 3. User-Agent异常检测
        RiskFactor userAgentRisk = assessUserAgentRisk(context);
        if (userAgentRisk != null) {
            riskFactors.add(userAgentRisk);
            totalRiskScore += userAgentRisk.getScore();
        }
        
        // 4. 登录行为模式分析
        RiskFactor behaviorRisk = assessLoginBehavior(context);
        if (behaviorRisk != null) {
            riskFactors.add(behaviorRisk);
            totalRiskScore += behaviorRisk.getScore();
        }
        
        // 5. 时间模式异常
        RiskFactor timeRisk = assessTimePattern(context);
        if (timeRisk != null) {
            riskFactors.add(timeRisk);
            totalRiskScore += timeRisk.getScore();
        }
        
        assessment.setRiskScore(Math.min(100, totalRiskScore));
        assessment.setRiskFactors(riskFactors);
        assessment.setRiskLevel(determineRiskLevel(assessment.getRiskScore()));
        assessment.setRecommendations(generateRecommendations(assessment));
        
        return assessment;
    }
    
    /**
     * 检测设备异常
     */
    public List<DeviceAnomaly> detectAnomalies(DeviceRiskContext context) {
        List<DeviceAnomaly> anomalies = new ArrayList<>();
        
        // 设备指纹突然变化
        if (hasDeviceFingerprintChanged(context)) {
            anomalies.add(new DeviceAnomaly(
                "FINGERPRINT_CHANGE",
                "设备指纹发生变化",
                "HIGH",
                "可能存在设备环境变化或恶意操作"
            ));
        }
        
        // 异常登录时间
        if (hasUnusualLoginTime(context)) {
            anomalies.add(new DeviceAnomaly(
                "UNUSUAL_TIME",
                "异常登录时间",
                "MEDIUM",
                "在非常规时间段进行登录"
            ));
        }
        
        // 地理位置异常跳跃
        if (hasUnusualLocationJump(context)) {
            anomalies.add(new DeviceAnomaly(
                "LOCATION_JUMP",
                "地理位置异常跳跃",
                "HIGH",
                "短时间内在不同地理位置登录"
            ));
        }
        
        // 登录频率异常
        if (hasUnusualLoginFrequency(context)) {
            anomalies.add(new DeviceAnomaly(
                "FREQUENCY_ANOMALY",
                "登录频率异常",
                "MEDIUM",
                "登录频率超出正常范围"
            ));
        }
        
        return anomalies;
    }
    
    /**
     * 验证设备信任度
     */
    public int calculateTrustScore(TrustContext context) {
        int trustScore = 50; // 基础信任度
        
        // 历史使用时长加分
        if (context.getAccountAge() != null) {
            long days = DateUtil.betweenDay(
                DateUtil.date(context.getAccountAge()),
                DateUtil.date(),
                false
            );
            if (days >= 365) {
                trustScore += 20;
            } else if (days >= 90) {
                trustScore += 15;
            } else if (days >= 30) {
                trustScore += 10;
            }
        }
        
        // 设备使用频率加分
        if (context.getLoginCount() != null) {
            if (context.getLoginCount() >= 100) {
                trustScore += 15;
            } else if (context.getLoginCount() >= 50) {
                trustScore += 10;
            } else if (context.getLoginCount() >= 10) {
                trustScore += 5;
            }
        }
        
        // IP稳定性加分
        if (context.getIpStability() != null && context.getIpStability() > 0.8) {
            trustScore += 10;
        }
        
        // 安全事件扣分
        if (context.getSecurityIncidents() != null) {
            trustScore -= context.getSecurityIncidents() * 5;
        }
        
        // 手动信任状态
        if (context.getManualTrust() != null && context.getManualTrust()) {
            trustScore += 15;
        }
        
        return Math.max(0, Math.min(100, trustScore));
    }
    
    /**
     * 生成设备安全策略
     */
    public SecurityPolicy generateSecurityPolicy(DeviceRiskAssessment assessment) {
        SecurityPolicy policy = new SecurityPolicy();
        
        switch (assessment.getRiskLevel()) {
            case "LOW":
                policy.setRequire2FA(false);
                policy.setRequireVerification(false);
                policy.setAllowAccess(true);
                policy.setSessionTimeout(24 * 60); // 24小时
                break;
                
            case "MEDIUM":
                policy.setRequire2FA(true);
                policy.setRequireVerification(false);
                policy.setAllowAccess(true);
                policy.setSessionTimeout(8 * 60); // 8小时
                break;
                
            case "HIGH":
                policy.setRequire2FA(true);
                policy.setRequireVerification(true);
                policy.setAllowAccess(true);
                policy.setSessionTimeout(2 * 60); // 2小时
                break;
                
            case "CRITICAL":
                policy.setRequire2FA(true);
                policy.setRequireVerification(true);
                policy.setAllowAccess(false); // 拒绝访问
                policy.setSessionTimeout(30); // 30分钟
                break;
                
            default:
                policy.setRequire2FA(true);
                policy.setRequireVerification(true);
                policy.setAllowAccess(true);
                policy.setSessionTimeout(4 * 60); // 4小时
        }
        
        return policy;
    }
    
    // ==================== 私有方法 ====================
    
    private RiskFactor assessFingerprintConsistency(DeviceRiskContext context) {
        if (StrUtil.isBlank(context.getCurrentFingerprint()) || 
            StrUtil.isBlank(context.getStoredFingerprint())) {
            return null;
        }
        
        if (!Objects.equals(context.getCurrentFingerprint(), context.getStoredFingerprint())) {
            return new RiskFactor(
                "FINGERPRINT_MISMATCH",
                "设备指纹不匹配",
                30,
                "当前设备指纹与存储的指纹不匹配"
            );
        }
        
        return null;
    }
    
    private RiskFactor assessIPRisk(DeviceRiskContext context) {
        if (StrUtil.isBlank(context.getCurrentIP())) {
            return null;
        }
        
        // 检查是否为可疑IP
        if (isSuspiciousIP(context.getCurrentIP())) {
            return new RiskFactor(
                "SUSPICIOUS_IP",
                "可疑IP地址",
                25,
                "来源IP地址可能存在安全风险"
            );
        }
        
        // 检查IP变化频率
        if (hasFrequentIPChanges(context)) {
            return new RiskFactor(
                "FREQUENT_IP_CHANGE",
                "IP变化频繁",
                20,
                "短时间内IP地址变化过于频繁"
            );
        }
        
        return null;
    }
    
    private RiskFactor assessUserAgentRisk(DeviceRiskContext context) {
        if (StrUtil.isBlank(context.getUserAgent())) {
            return null;
        }
        
        // 检查是否为可疑User-Agent
        for (Pattern pattern : SUSPICIOUS_USER_AGENTS) {
            if (pattern.matcher(context.getUserAgent()).find()) {
                return new RiskFactor(
                    "SUSPICIOUS_USER_AGENT",
                    "可疑用户代理",
                    20,
                    "检测到可疑的User-Agent"
                );
            }
        }
        
        return null;
    }
    
    private RiskFactor assessLoginBehavior(DeviceRiskContext context) {
        // 短时间内多次登录尝试
        if (hasMultipleLoginAttempts(context)) {
            return new RiskFactor(
                "MULTIPLE_LOGIN_ATTEMPTS",
                "多次登录尝试",
                15,
                "短时间内存在多次登录尝试"
            );
        }
        
        return null;
    }
    
    private RiskFactor assessTimePattern(DeviceRiskContext context) {
        if (context.getLoginTime() == null) {
            return null;
        }
        
        // 检查是否在异常时间登录
        int hour = context.getLoginTime().getHour();
        if (hour >= 2 && hour <= 5) { // 凌晨2-5点
            return new RiskFactor(
                "UNUSUAL_TIME",
                "异常登录时间",
                10,
                "在凌晨时段进行登录"
            );
        }
        
        return null;
    }
    
    private boolean hasDeviceFingerprintChanged(DeviceRiskContext context) {
        return StrUtil.isNotBlank(context.getCurrentFingerprint()) &&
               StrUtil.isNotBlank(context.getStoredFingerprint()) &&
               !Objects.equals(context.getCurrentFingerprint(), context.getStoredFingerprint());
    }
    
    private boolean hasUnusualLoginTime(DeviceRiskContext context) {
        if (context.getLoginTime() == null) {
            return false;
        }
        
        int hour = context.getLoginTime().getHour();
        return hour >= 2 && hour <= 5; // 凌晨2-5点认为是异常时间
    }
    
    private boolean hasUnusualLocationJump(DeviceRiskContext context) {
        if (StrUtil.isBlank(context.getCurrentLocation()) || 
            StrUtil.isBlank(context.getLastLocation()) ||
            context.getLastLoginTime() == null) {
            return false;
        }
        
        // 简单的地理位置跳跃检测（实际应该使用更精确的地理距离计算）
//        if (!Objects.equals(context.getCurrentLocation(), context.getLastLocation())) {
//            long minutesBetween = DateUtil.betweenMinute(
//                DateUtil.date(context.getLastLoginTime()),
//                DateUtil.date(),
//                false
//            );
//
//            // 如果在30分钟内位置发生变化，认为是异常跳跃
//            return minutesBetween < 30;
//        }
        
        return false;
    }
    
    private boolean hasUnusualLoginFrequency(DeviceRiskContext context) {
        if (context.getRecentLoginTimes() == null || context.getRecentLoginTimes().isEmpty()) {
            return false;
        }
        
        // 检查最近1小时内的登录次数
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        long recentLogins = context.getRecentLoginTimes().stream()
                .filter(time -> time.isAfter(oneHourAgo))
                .count();
        
        return recentLogins > 10; // 1小时内超过10次登录认为异常
    }
    
    private boolean isSuspiciousIP(String ip) {
        // 这里应该集成威胁情报API或使用IP黑名单
        // 简单示例：检查是否为内网IP
        return ip.startsWith("127.") || ip.startsWith("192.168.") || ip.startsWith("10.");
    }
    
    private boolean hasFrequentIPChanges(DeviceRiskContext context) {
        if (context.getRecentIPs() == null || context.getRecentIPs().size() <= 1) {
            return false;
        }
        
        // 如果最近使用了超过5个不同的IP，认为变化频繁
        return new HashSet<>(context.getRecentIPs()).size() > 5;
    }
    
    private boolean hasMultipleLoginAttempts(DeviceRiskContext context) {
        if (context.getRecentLoginTimes() == null) {
            return false;
        }
        
        LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
        long recentAttempts = context.getRecentLoginTimes().stream()
                .filter(time -> time.isAfter(thirtyMinutesAgo))
                .count();
        
        return recentAttempts > 5; // 30分钟内超过5次尝试
    }
    
    private String determineRiskLevel(int riskScore) {
        if (riskScore <= 20) {
            return "LOW";
        } else if (riskScore <= 40) {
            return "MEDIUM";
        } else if (riskScore <= 70) {
            return "HIGH";
        } else {
            return "CRITICAL";
        }
    }
    
    private List<String> generateRecommendations(DeviceRiskAssessment assessment) {
        List<String> recommendations = new ArrayList<>();
        
        switch (assessment.getRiskLevel()) {
            case "LOW":
                recommendations.add("继续正常使用");
                recommendations.add("定期检查设备安全状态");
                break;
                
            case "MEDIUM":
                recommendations.add("启用二次验证");
                recommendations.add("监控异常活动");
                recommendations.add("检查设备安全设置");
                break;
                
            case "HIGH":
                recommendations.add("立即启用二次验证");
                recommendations.add("验证设备合法性");
                recommendations.add("检查账户安全");
                recommendations.add("考虑更换密码");
                break;
                
            case "CRITICAL":
                recommendations.add("立即停止使用并联系管理员");
                recommendations.add("全面检查设备安全");
                recommendations.add("更换所有密码");
                recommendations.add("启用所有安全功能");
                break;
        }
        
        return recommendations;
    }
    
    // ==================== 内部类 ====================
    
    public static class DeviceRiskContext {
        private String currentFingerprint;
        private String storedFingerprint;
        private String currentIP;
        private String lastIP;
        private String userAgent;
        private String currentLocation;
        private String lastLocation;
        private LocalDateTime loginTime;
        private LocalDateTime lastLoginTime;
        private List<LocalDateTime> recentLoginTimes;
        private List<String> recentIPs;
        
        // Getters and Setters
        public String getCurrentFingerprint() { return currentFingerprint; }
        public void setCurrentFingerprint(String currentFingerprint) { this.currentFingerprint = currentFingerprint; }
        public String getStoredFingerprint() { return storedFingerprint; }
        public void setStoredFingerprint(String storedFingerprint) { this.storedFingerprint = storedFingerprint; }
        public String getCurrentIP() { return currentIP; }
        public void setCurrentIP(String currentIP) { this.currentIP = currentIP; }
        public String getLastIP() { return lastIP; }
        public void setLastIP(String lastIP) { this.lastIP = lastIP; }
        public String getUserAgent() { return userAgent; }
        public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
        public String getCurrentLocation() { return currentLocation; }
        public void setCurrentLocation(String currentLocation) { this.currentLocation = currentLocation; }
        public String getLastLocation() { return lastLocation; }
        public void setLastLocation(String lastLocation) { this.lastLocation = lastLocation; }
        public LocalDateTime getLoginTime() { return loginTime; }
        public void setLoginTime(LocalDateTime loginTime) { this.loginTime = loginTime; }
        public LocalDateTime getLastLoginTime() { return lastLoginTime; }
        public void setLastLoginTime(LocalDateTime lastLoginTime) { this.lastLoginTime = lastLoginTime; }
        public List<LocalDateTime> getRecentLoginTimes() { return recentLoginTimes; }
        public void setRecentLoginTimes(List<LocalDateTime> recentLoginTimes) { this.recentLoginTimes = recentLoginTimes; }
        public List<String> getRecentIPs() { return recentIPs; }
        public void setRecentIPs(List<String> recentIPs) { this.recentIPs = recentIPs; }
    }
    
    public static class TrustContext {
        private LocalDateTime accountAge;
        private Integer loginCount;
        private Double ipStability;
        private Integer securityIncidents;
        private Boolean manualTrust;
        
        // Getters and Setters
        public LocalDateTime getAccountAge() { return accountAge; }
        public void setAccountAge(LocalDateTime accountAge) { this.accountAge = accountAge; }
        public Integer getLoginCount() { return loginCount; }
        public void setLoginCount(Integer loginCount) { this.loginCount = loginCount; }
        public Double getIpStability() { return ipStability; }
        public void setIpStability(Double ipStability) { this.ipStability = ipStability; }
        public Integer getSecurityIncidents() { return securityIncidents; }
        public void setSecurityIncidents(Integer securityIncidents) { this.securityIncidents = securityIncidents; }
        public Boolean getManualTrust() { return manualTrust; }
        public void setManualTrust(Boolean manualTrust) { this.manualTrust = manualTrust; }
    }
    
    public static class DeviceRiskAssessment {
        private int riskScore;
        private String riskLevel;
        private List<RiskFactor> riskFactors;
        private List<String> recommendations;
        
        // Getters and Setters
        public int getRiskScore() { return riskScore; }
        public void setRiskScore(int riskScore) { this.riskScore = riskScore; }
        public String getRiskLevel() { return riskLevel; }
        public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }
        public List<RiskFactor> getRiskFactors() { return riskFactors; }
        public void setRiskFactors(List<RiskFactor> riskFactors) { this.riskFactors = riskFactors; }
        public List<String> getRecommendations() { return recommendations; }
        public void setRecommendations(List<String> recommendations) { this.recommendations = recommendations; }
    }
    
    public static class RiskFactor {
        private String type;
        private String description;
        private int score;
        private String details;
        
        public RiskFactor(String type, String description, int score, String details) {
            this.type = type;
            this.description = description;
            this.score = score;
            this.details = details;
        }
        
        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public int getScore() { return score; }
        public void setScore(int score) { this.score = score; }
        public String getDetails() { return details; }
        public void setDetails(String details) { this.details = details; }
    }
    
    public static class DeviceAnomaly {
        private String type;
        private String description;
        private String severity;
        private String details;
        
        public DeviceAnomaly(String type, String description, String severity, String details) {
            this.type = type;
            this.description = description;
            this.severity = severity;
            this.details = details;
        }
        
        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getSeverity() { return severity; }
        public void setSeverity(String severity) { this.severity = severity; }
        public String getDetails() { return details; }
        public void setDetails(String details) { this.details = details; }
    }
    
    public static class SecurityPolicy {
        private boolean require2FA;
        private boolean requireVerification;
        private boolean allowAccess;
        private int sessionTimeout; // 分钟
        
        // Getters and Setters
        public boolean isRequire2FA() { return require2FA; }
        public void setRequire2FA(boolean require2FA) { this.require2FA = require2FA; }
        public boolean isRequireVerification() { return requireVerification; }
        public void setRequireVerification(boolean requireVerification) { this.requireVerification = requireVerification; }
        public boolean isAllowAccess() { return allowAccess; }
        public void setAllowAccess(boolean allowAccess) { this.allowAccess = allowAccess; }
        public int getSessionTimeout() { return sessionTimeout; }
        public void setSessionTimeout(int sessionTimeout) { this.sessionTimeout = sessionTimeout; }
    }
} 