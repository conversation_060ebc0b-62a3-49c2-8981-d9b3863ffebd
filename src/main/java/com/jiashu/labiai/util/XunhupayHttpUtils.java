package com.jiashu.labiai.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * HTTP工具类 - 专门用于虎皮椒支付
 */
@Slf4j
public class XunhupayHttpUtils {

    private static final int TIMEOUT = 30000; // 30秒超时


    public static JSONObject postJson(String url, Map<String,Object> options) {
        try {
//            HttpResponse response = HttpRequest.post(url)
//                    .header("Content-Type", "application/json;charset=utf-8")
//                    .body(jsonData)
//                    .timeout(TIMEOUT)
//                    .execute();
            String result = HttpUtil.post(url, options);


           JSONObject resultObj;

            try {
                resultObj = JSONUtil.parseObj(result);
            } catch (Exception e) {
                log.error("虎皮椒支付API返回结果非JSON: url={}, error={} ,response={}", url, e.getMessage(), result);
                throw new RuntimeException("虎皮椒支付API调用失败", e);
            }
            log.info("虎皮椒支付API调用成功: url={}, response={}", url, result);
            return resultObj;
        } catch (Exception e) {
            log.error("虎皮椒支付API调用失败: url={}, error={} ", url, e.getMessage(), e);
            throw new RuntimeException("虎皮椒支付API调用失败", e);
        }
    }

//    /**
//     * 发送表单格式的POST请求
//     */
//    public static String postForm(String url, Map<String, Object> params) {
//        try {
//            HttpRequest request = HttpRequest.post(url).timeout(TIMEOUT);
//
//            // 添加表单参数
//            params.forEach((key, value) -> {
//                if (value != null) {
//                    request.form(key, value.toString());
//                }
//            });
//
//            HttpResponse response = request.execute();
//            String result = response.body();
//
//            log.info("虎皮椒支付API调用成功: url={}, response={}", url, result);
//            return result;
//        } catch (Exception e) {
//            log.error("虎皮椒支付API调用失败: url={}, params={}, error={}", url, JSONUtil.toJsonStr(params), e.getMessage(), e);
//            throw new RuntimeException("虎皮椒支付API调用失败", e);
//        }
//    }

    /**
     * 发送GET请求
     */
    public static String get(String url, Map<String, Object> params) {
        try {
            HttpRequest request = HttpRequest.get(url).timeout(TIMEOUT);

            // 添加查询参数
            if (params != null) {
                params.forEach((key, value) -> {
                    if (value != null) {
                        request.form(key, value.toString());
                    }
                });
            }

            HttpResponse response = request.execute();
            String result = response.body();

            log.info("虎皮椒支付API调用成功: url={}, response={}", url, result);
            return result;
        } catch (Exception e) {
            log.error("虎皮椒支付API调用失败: url={}, params={}, error={}", url, JSONUtil.toJsonStr(params), e.getMessage(), e);
            throw new RuntimeException("虎皮椒支付API调用失败", e);
        }
    }
} 