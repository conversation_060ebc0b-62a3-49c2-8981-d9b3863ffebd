package com.jiashu.labiai.util;

import cn.hutool.core.util.StrUtil;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 设备工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class DeviceUtil {
    
    // 移动设备正则
    private static final Pattern MOBILE_PATTERN = Pattern.compile(
        "(?i)mobile|iphone|ipod|android|blackberry|opera|mini|windows\\sce|palm|smartphone|iemobile"
    );
    
    // 平板设备正则
    private static final Pattern TABLET_PATTERN = Pattern.compile(
        "(?i)ipad|tablet|sch-i800|playbook|xoom|kindle"
    );
    
    // 浏览器正则
    private static final Pattern CHROME_PATTERN = Pattern.compile("Chrome/([\\d\\.]+)");
    private static final Pattern FIREFOX_PATTERN = Pattern.compile("Firefox/([\\d\\.]+)");
    private static final Pattern SAFARI_PATTERN = Pattern.compile("Safari/([\\d\\.]+)");
    private static final Pattern EDGE_PATTERN = Pattern.compile("Edg/([\\d\\.]+)");
    
    // 操作系统正则
    private static final Pattern WINDOWS_PATTERN = Pattern.compile("Windows NT ([\\d\\.]+)");
    private static final Pattern MAC_PATTERN = Pattern.compile("Mac OS X ([\\d_\\.]+)");
    private static final Pattern LINUX_PATTERN = Pattern.compile("Linux");
    private static final Pattern ANDROID_PATTERN = Pattern.compile("Android ([\\d\\.]+)");
    private static final Pattern IOS_PATTERN = Pattern.compile("OS ([\\d_]+)");
    
    /**
     * 解析User-Agent获取设备信息
     * 
     * @param userAgent User-Agent字符串
     * @return 设备信息
     */
    public static DeviceInfo parseUserAgent(String userAgent) {
        if (StrUtil.isBlank(userAgent)) {
            return createUnknownDevice(userAgent);
        }
        
        try {
            UserAgent agent = UserAgent.parseUserAgentString(userAgent);
            Browser browser = agent.getBrowser();
            OperatingSystem os = agent.getOperatingSystem();


            return DeviceInfo.builder()
                    .userAgent(userAgent)
                    .deviceType(os.getDeviceType().getName())
                    .platform(os.getName())
                    .browser(browser.getName())
                    .browserVersion(agent.getBrowserVersion().toString())
                    .isMobile(isMobile(userAgent))
                    .isTablet(isTablet(userAgent))
                    .build();
        } catch (Exception e) {
            log.warn("解析User-Agent失败: {}, error: {}", userAgent, e.getMessage());
            return createUnknownDevice(userAgent);
        }
    }
    
    /**
     * 生成设备名称
     * 
     * @param platform 平台
     * @param browser 浏览器
     * @param deviceType 设备类型
     * @return 设备名称
     */
    public static String generateDeviceName(String platform, String browser, String deviceType) {
        StringBuilder name = new StringBuilder();
        
        // 添加平台信息
        if (StrUtil.isNotBlank(platform)) {
            name.append(platform);
        } else {
            name.append("Unknown OS");
        }
        
        // 添加浏览器信息
        if (StrUtil.isNotBlank(browser)) {
            name.append(" - ").append(browser);
        }
        
        // 添加设备类型
        if (StrUtil.isNotBlank(deviceType) && !"unknown".equals(deviceType)) {
            name.append(" (").append(getDeviceTypeName(deviceType)).append(")");
        }
        
        return name.toString();
    }
    
    /**
     * 检测设备类型
     */
    private static String detectDeviceType(String userAgent) {
        if (isTablet(userAgent)) {
            return "tablet";
        } else if (isMobile(userAgent)) {
            return "mobile";
        } else {
            return "desktop";
        }
    }
    
    /**
     * 检测操作系统平台
     */
    private static String detectPlatform(String userAgent) {
        Matcher matcher;
        
        // Windows
        matcher = WINDOWS_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            return "Windows " + getWindowsVersion(matcher.group(1));
        }
        
        // Mac OS
        matcher = MAC_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            return "macOS " + matcher.group(1).replace("_", ".");
        }
        
        // Android
        matcher = ANDROID_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            return "Android " + matcher.group(1);
        }
        
        // iOS
        matcher = IOS_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            return "iOS " + matcher.group(1).replace("_", ".");
        }
        
        // Linux
        if (LINUX_PATTERN.matcher(userAgent).find()) {
            return "Linux";
        }
        
        return "Unknown";
    }
    
    /**
     * 检测浏览器
     */
    private static String detectBrowser(String userAgent) {
        if (EDGE_PATTERN.matcher(userAgent).find()) {
            return "Microsoft Edge";
        } else if (CHROME_PATTERN.matcher(userAgent).find()) {
            return "Chrome";
        } else if (FIREFOX_PATTERN.matcher(userAgent).find()) {
            return "Firefox";
        } else if (SAFARI_PATTERN.matcher(userAgent).find() && !userAgent.contains("Chrome")) {
            return "Safari";
        }
        return "Unknown";
    }
    
    /**
     * 检测浏览器版本
     */
    private static String detectBrowserVersion(String userAgent) {
        Matcher matcher;
        
        if (userAgent.contains("Edg/")) {
            matcher = EDGE_PATTERN.matcher(userAgent);
        } else if (userAgent.contains("Chrome/")) {
            matcher = CHROME_PATTERN.matcher(userAgent);
        } else if (userAgent.contains("Firefox/")) {
            matcher = FIREFOX_PATTERN.matcher(userAgent);
        } else if (userAgent.contains("Safari/") && !userAgent.contains("Chrome")) {
            matcher = SAFARI_PATTERN.matcher(userAgent);
        } else {
            return "Unknown";
        }
        
        return matcher.find() ? matcher.group(1) : "Unknown";
    }
    
    /**
     * 判断是否为移动设备
     */
    private static boolean isMobile(String userAgent) {
        return MOBILE_PATTERN.matcher(userAgent).find();
    }
    
    /**
     * 判断是否为平板设备
     */
    private static  boolean isTablet(String userAgent) {
        return TABLET_PATTERN.matcher(userAgent).find();
    }
    
    /**
     * 获取Windows版本名称
     */
    private static  String getWindowsVersion(String version) {
        switch (version) {
            case "10.0": return "10";
            case "6.3": return "8.1";
            case "6.2": return "8";
            case "6.1": return "7";
            case "6.0": return "Vista";
            case "5.1": return "XP";
            default: return version;
        }
    }
    
    /**
     * 获取设备类型中文名称
     */
    private static String getDeviceTypeName(String deviceType) {
        switch (deviceType) {
            case "desktop": return "桌面端";
            case "mobile": return "手机";
            case "tablet": return "平板";
            default: return "未知设备";
        }
    }
    
    /**
     * 创建未知设备信息
     */
    private static DeviceInfo createUnknownDevice(String userAgent) {
        return DeviceInfo.builder()
                .userAgent(userAgent)
                .deviceType("unknown")
                .platform("Unknown")
                .browser("Unknown")
                .browserVersion("Unknown")
                .isMobile(false)
                .isTablet(false)
                .build();
    }
}
