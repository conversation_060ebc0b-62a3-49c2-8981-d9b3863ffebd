package com.jiashu.labiai.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * 设备信息DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfo {
    
    /**
     * 设备类型: desktop/mobile/tablet/unknown
     */
    private String deviceType;
    
    /**
     * 操作系统平台
     */
    private String platform;
    
    /**
     * 浏览器名称
     */
    private String browser;
    
    /**
     * 浏览器版本
     */
    private String browserVersion;
    
    /**
     * 屏幕分辨率
     */
    private String screenResolution;
    
    /**
     * 时区
     */
    private String timezone;
    
    /**
     * 是否为移动设备
     */
    private boolean isMobile;
    
    /**
     * 是否为平板设备
     */
    private boolean isTablet;
    
    /**
     * 原始User-Agent
     */
    private String userAgent;
} 