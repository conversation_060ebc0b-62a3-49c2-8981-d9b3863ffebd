package com.jiashu.labiai.util;

import cn.hutool.core.util.IdUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtUtil {
    
    @Value("${security.jwt.secret:chatgpt-session-secret-key}")
    private String secret;
    
    @Value("${security.jwt.expiration:600}")
    private Integer expiration; // 默认10分钟
    
    private static final String CLAIM_VISITOR_ID = "visitorId";
    private static final String CLAIM_DEVICE_HASH = "deviceHash";
    private static final String CLAIM_TOKEN_VERSION = "tokenVersion";
    private static final String CLAIM_INITIAL_IP = "initialIp";
    private static final String CLAIM_INITIAL_UA = "initialUa";
    
    /**
     * 生成会话JWT令牌
     */
    public String generateSessionToken(String visitorId, String deviceHash, String clientIp, String userAgent) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            String jti = IdUtil.simpleUUID();
            
            Date now = new Date();
            Date expTime = new Date(now.getTime() + expiration * 1000);
            
            JWTCreator.Builder builder = JWT.create()
                    .withJWTId(jti)
                    .withIssuedAt(now)
                    .withExpiresAt(expTime)
                    .withClaim(CLAIM_VISITOR_ID, visitorId)
                    .withClaim(CLAIM_DEVICE_HASH, deviceHash)
                    .withClaim(CLAIM_TOKEN_VERSION, 1)
                    .withClaim(CLAIM_INITIAL_IP, clientIp)
                    .withClaim(CLAIM_INITIAL_UA, userAgent);
            
            String token = builder.sign(algorithm);
            log.debug("生成JWT令牌成功, jti: {}, visitorId: {}", jti, visitorId);
            return token;
        } catch (Exception e) {
            log.error("生成JWT令牌失败", e);
            throw new RuntimeException("JWT令牌生成失败", e);
        }
    }
    
    /**
     * 验证JWT令牌
     */
    public DecodedJWT verifyToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).build();
            return verifier.verify(token);
        } catch (JWTVerificationException e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            throw new RuntimeException("JWT令牌验证失败", e);
        }
    }
    
    /**
     * 从JWT中获取会话信息
     */
    public Map<String, Object> getSessionInfo(DecodedJWT decodedJWT) {
        Map<String, Object> info = new HashMap<>();
        info.put("jti", decodedJWT.getId());
        info.put("visitorId", decodedJWT.getClaim(CLAIM_VISITOR_ID).asString());
        info.put("deviceHash", decodedJWT.getClaim(CLAIM_DEVICE_HASH).asString());
        info.put("tokenVersion", decodedJWT.getClaim(CLAIM_TOKEN_VERSION).asInt());
        info.put("initialIp", decodedJWT.getClaim(CLAIM_INITIAL_IP).asString());
        info.put("initialUa", decodedJWT.getClaim(CLAIM_INITIAL_UA).asString());
        info.put("issuedAt", decodedJWT.getIssuedAt());
        info.put("expiresAt", decodedJWT.getExpiresAt());
        return info;
    }
    
    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(DecodedJWT decodedJWT) {
        return decodedJWT.getExpiresAt().before(new Date());
    }
    
    /**
     * 获取令牌剩余有效时间(秒)
     */
    public long getRemainingSeconds(DecodedJWT decodedJWT) {
        long remaining = (decodedJWT.getExpiresAt().getTime() - System.currentTimeMillis()) / 1000;
        return Math.max(0, remaining);
    }
    
    /**
     * 从JWT令牌中提取JTI (JWT ID)
     */
    public String extractJti(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            return decodedJWT.getId();
        } catch (Exception e) {
            log.warn("提取JTI失败: {}", e.getMessage());
            throw new RuntimeException("无法从令牌中提取JTI", e);
        }
    }
}
