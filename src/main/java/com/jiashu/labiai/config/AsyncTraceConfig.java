package com.jiashu.labiai.config;

import com.jiashu.labiai.trace.TraceContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置 - 支持TraceId传递
 * 基于Spring Boot 2.7的异步配置
 */
@Configuration
@EnableAsync
public class AsyncTraceConfig {
    
    /**
     * 自定义异步执行器
     */
    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(10);
        
        // 最大线程数
        executor.setMaxPoolSize(50);
        
        // 队列容量
        executor.setQueueCapacity(200);
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        
        // 线程名前缀
        executor.setThreadNamePrefix("async-trace-");
        
        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        // 设置任务装饰器，传递TraceId
        executor.setTaskDecorator(new TraceableTaskDecorator());
        
        // 初始化
        executor.initialize();
        
        return executor;
    }
    
    /**
     * 通用异步执行器（小任务）
     */
    @Bean(name = "smallTaskExecutor")
    public ThreadPoolTaskExecutor smallTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(30);
        executor.setThreadNamePrefix("small-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceableTaskDecorator());
        
        executor.initialize();
        return executor;
    }
    
    /**
     * 大任务异步执行器
     */
    @Bean(name = "largeTaskExecutor")
    public ThreadPoolTaskExecutor largeTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(15);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(120);
        executor.setThreadNamePrefix("large-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceableTaskDecorator());
        
        executor.initialize();
        return executor;
    }
    
    /**
     * 支持TraceId传递的任务装饰器
     */
    public static class TraceableTaskDecorator implements TaskDecorator {
        
        @Override
        public Runnable decorate(Runnable runnable) {
            // 获取当前线程的TraceId和SpanId
            String traceId = TraceContext.getTraceId();
//            String parentSpanId = TraceContext.getSpanId();
            
            return () -> {
                try {
                    // 在新线程中设置TraceId
                    TraceContext.setTraceId(traceId);
                    
//                    // 生成新的SpanId（表示这是一个子操作）
//                    String newSpanId = TraceContext.generateSpanId();
//                    TraceContext.setSpanId(newSpanId);
                    
                    // 执行原任务
                    runnable.run();
                    
                } finally {
                    // 清理MDC
                    TraceContext.clear();
                }
            };
        }
    }
} 