package com.jiashu.labiai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * MailGun 邮件服务配置
 * 基于官方 mailgun-java SDK
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mailgun")
public class MailGunConfig {
    
    /**
     * MailGun Private API Key
     * 在 Mailgun 控制台的 Settings -> API Keys 中获取
     */
    private String apiKey;
    
    /**
     * MailGun Domain
     * 在 Mailgun 控制台中配置的域名
     */
    private String domain;
    
    /**
     * MailGun 基础URL
     * 美国服务器: https://api.mailgun.net/ (默认)
     * 欧盟服务器: https://api.eu.mailgun.net/
     */
    private String baseUrl = "https://api.mailgun.net/";
    
    /**
     * 发件人邮箱
     */
    private String fromEmail;
    
    /**
     * 发件人名称
     */
    private String fromName = "蜡笔AI系统邮件";
    
    /**
     * 验证码过期时间（分钟）
     */
    private Integer verificationExpireMinutes = 10;
    
    /**
     * 是否使用欧盟服务器
     */
    private boolean useEuServer = false;
    
    /**
     * 验证码邮件主题格式
     * default: "验证码 123456 - 系统名称"
     * bracket: "[123456] 邮箱验证码"
     * simple: "您的验证码：123456"
     * short: "123456 - 邮箱验证"
     */
    private String subjectFormat = "default";
    
    /**
     * 验证码发送频率限制配置
     */
    
    /**
     * 同一邮箱发送验证码的最小间隔时间（秒）
     */
    private Integer emailInterval = 60;
    
    /**
     * 同一设备指纹发送验证码的最小间隔时间（秒）
     */
    private Integer fingerprintInterval = 30;
    
    /**
     * 同一邮箱每小时最大发送次数
     */
    private Integer emailHourlyLimit = 10;
    
    /**
     * 同一设备指纹每小时最大发送次数
     */
    private Integer fingerprintHourlyLimit = 20;
    
    /**
     * 同一邮箱每天最大发送次数
     */
    private Integer emailDailyLimit = 50;
    
    /**
     * 同一设备指纹每天最大发送次数
     */
    private Integer fingerprintDailyLimit = 100;
} 