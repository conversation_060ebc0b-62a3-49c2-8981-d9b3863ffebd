package com.jiashu.labiai.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.jiashu.labiai.handler.JSONObjectTypeHandler;
import com.jiashu.labiai.handler.JsonListTypeHandler;
import com.jiashu.labiai.interceptor.MyBatisPlusTraceInterceptor;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.apache.ibatis.session.ExecutorType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class MybatisPlusConfig {

    @Autowired
    private MyBatisPlusTraceInterceptor traceInterceptor;

    /**
     * MyBatis-Plus拦截器配置
     * 包含分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        return interceptor;
    }
    
    /**
     * MyBatis配置自定义器
     * 用于添加自定义拦截器和其他配置，避免循环依赖
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            // 添加自定义SQL拦截器（链路追踪）
            configuration.addInterceptor(traceInterceptor);
            
            // 注册自定义 TypeHandler
            configuration.getTypeHandlerRegistry().register(JsonListTypeHandler.class);
            configuration.getTypeHandlerRegistry().register(JSONObjectTypeHandler.class);
            
            // 关闭缓存（根据需要调整）
            configuration.setCacheEnabled(false);
            
            // 开启懒加载
            configuration.setLazyLoadingEnabled(true);
            
            // 设置懒加载触发方法
            configuration.setAggressiveLazyLoading(false);
            
            // 允许JDBC生成主键
            configuration.setUseGeneratedKeys(true);
            
            // 配置默认的执行器类型
            configuration.setDefaultExecutorType(ExecutorType.REUSE);
            
            // 指定 MyBatis 所用日志的具体实现
            configuration.setLogImpl(Slf4jImpl.class);
        };
    }
} 