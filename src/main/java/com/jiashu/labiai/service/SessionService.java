package com.jiashu.labiai.service;



import com.jiashu.labiai.dto.response.auth.SessionInitResponse;

import java.util.Map;

/**
 * 会话管理服务接口
 * 
 * <AUTHOR>
 */
public interface SessionService {
    
    /**
     * 初始化会话
     *
     * @return 会话信息
     */
    SessionInitResponse initSession();
    
    /**
     * 验证会话和nonce
     * 
     * @param token JWT令牌
     * @param nonce 当前nonce
     * @param currentDeviceHash 当前设备哈希（可为null，将从JWT中获取）
     * @param clientIp 客户端IP
     * @param userAgent User-Agent
     * @return 新的nonce
     */
    String validateSessionAndNonce(String token, String nonce, String currentDeviceHash, String clientIp, String userAgent);
    
    /**
     * 验证会话和nonce（不消费nonce）
     * 
     * @param token JWT令牌
     * @param nonce 当前nonce
     * @param currentDeviceHash 当前设备哈希（可为null，将从JWT中获取）
     * @param clientIp 客户端IP
     * @param userAgent User-Agent
     * @return 会话信息，用于后续操作
     */
    Map<String, Object> validateSessionWithoutConsumingNonce(String token, String nonce, String currentDeviceHash, String clientIp, String userAgent);
    
    /**
     * 生成新的nonce
     * 
     * @param jti 会话ID
     * @return 新的nonce
     */
    String generateNonce(String jti);
    
    /**
     * 验证nonce
     * 
     * @param jti 会话ID
     * @param nonce nonce值
     * @return 是否有效
     */
    boolean validateNonce(String jti, String nonce);
    
    /**
     * 检查设备是否被拉黑
     * 
     * @param visitorId 设备ID
     * @return 是否被拉黑
     */
    boolean isDeviceBlacklisted(String visitorId);
    
    /**
     * 检查会话是否被拉黑
     * 
     * @param jti 会话ID
     * @return 是否被拉黑
     */
    boolean isSessionBlacklisted(String jti);
    
    /**
     * 记录验证码尝试失败
     * 
     * @param jti 会话ID
     */
    void recordVerificationCodeFailure(String jti);
    
    /**
     * 检查验证码尝试次数是否超限
     * 
     * @param jti 会话ID
     * @return 是否超限
     */
    boolean isVerificationCodeAttemptsExceeded(String jti);
    
    /**
     * 获取当前nonce（用于测试）
     * 
     * @param jti 会话ID
     * @return 当前nonce
     */
    String getCurrentNonce(String jti);
    
    /**
     * 从JWT中提取JTI（用于测试）
     * 
     * @param token JWT令牌
     * @return JTI
     */
    String extractJti(String token);
} 