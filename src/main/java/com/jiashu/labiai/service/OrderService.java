package com.jiashu.labiai.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.entity.Order;
import com.jiashu.labiai.entity.Package;
import com.jiashu.labiai.entity.PackagePrice;
import com.jiashu.labiai.entity.UserSubscription;
import com.jiashu.labiai.enums.OrderStatusEnum;
import com.jiashu.labiai.exception.AuthorizationException;
import com.jiashu.labiai.exception.BusinessException;
import com.jiashu.labiai.exception.OrderException;
import com.jiashu.labiai.exception.PackageException;
import com.jiashu.labiai.mapper.OrderMapper;
import com.jiashu.labiai.mapper.PackageMapper;
import com.jiashu.labiai.mapper.PackagePriceMapper;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 订单服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderService extends ServiceImpl<OrderMapper, Order> implements IOrderService {

    private final PackageMapper packageMapper;
    private final PackagePriceMapper packagePriceMapper;
    private final IDiscountCodeService discountCodeService;
    private final TransactionTemplate transactionTemplate;
    private final IUserSubscriptionService userSubscriptionService;

    @Override
    @Transactional
    public ApiResponse<OrderDTO> createOrder(Long userId, Long packagePriceId, String discountCode) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("userId", userId);
                requestDetails.put("packagePriceId", packagePriceId);
                requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
                TraceUtils.recordBusinessEvent("ORDER_CREATE_START", requestDetails);

                // 1. 检查用户待支付订单数量限制
                checkUserPendingOrdersLimit(userId);

                // 2. 验证套餐价格信息
                PackagePrice packagePrice = packagePriceMapper.selectById(packagePriceId);
                if (packagePrice == null) {
                    throw PackageException.priceNotFound(packagePriceId);
                }

                // 3. 验证套餐信息
                Package pkg = packageMapper.selectById(packagePrice.getPackageId());
                if (pkg == null) {
                    throw PackageException.packageNotFound(packagePrice.getPackageId());
                }

                // 4. 如果有优惠码，先验证（不消费使用次数）
                DiscountCodeValidationDTO discountResult = null;
                if (StrUtil.isNotBlank(discountCode)) {
                    ApiResponse<DiscountCodeValidationDTO> validationResult = 
                        discountCodeService.validateDiscountCodeForOrder(
                            discountCode, pkg.getId(), packagePrice.getSalePrice(), userId);
                    
                    if (!validationResult.getSuccess()) {
                        return ApiResponse.error(validationResult.getCode(), validationResult.getMessage());
                    }
                    discountResult = validationResult.getData();
                }

                // 5. 创建完整订单（包含优惠信息）
                BigDecimal discountAmount = discountResult != null ? discountResult.getDiscountAmount() : BigDecimal.ZERO;
                Long discountCodeId = discountResult != null ? discountResult.getDiscountCodeId() : null;
                
                Order order = buildOrder(userId, pkg, packagePrice, discountCodeId, discountAmount);
                save(order);

                // 6. 如果有优惠码，原子性消费并创建使用记录
                if (discountResult != null) {
                    boolean consumed = discountCodeService.atomicUseDiscountCode(
                        discountResult.getDiscountCodeId(), userId, order.getId(), discountAmount);
                    
                    if (!consumed) {
                        // 消费失败，回滚订单
                        removeById(order.getId());
                        Map<String, Object> rollbackDetails = new HashMap<>();
                        rollbackDetails.put("orderId", order.getId());
                        rollbackDetails.put("discountCode", discountCode);
                        TraceUtils.recordError("ORDER_CREATE_DISCOUNT_FAILED_ROLLBACK", rollbackDetails);
                        return ApiResponse.error(400, "优惠码已被其他用户使用完毕，请重新选择");
                    }
                    
                    Map<String, Object> discountUseDetails = new HashMap<>();
                    discountUseDetails.put("orderId", order.getId());
                    discountUseDetails.put("discountCode", discountCode);
                    discountUseDetails.put("discountAmount", discountAmount);
                    TraceUtils.recordBusinessEvent("ORDER_DISCOUNT_CODE_CONSUMED", discountUseDetails);
                }

                // 7. 构建返回结果
                OrderDTO result = buildOrderDTO(order, pkg, packagePrice);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("orderId", order.getId());
                successDetails.put("orderNo", order.getOrderNo());
                successDetails.put("amount", order.getFinalAmount());
                TraceUtils.recordBusinessEvent("ORDER_CREATE_SUCCESS", successDetails);

                return ApiResponse.success(result, "订单创建成功");

            } catch (PackageException | OrderException e) {
                throw e;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("userId", userId);
                errorDetails.put("packagePriceId", packagePriceId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("ORDER_CREATE_FAILED", errorDetails);
                log.error("创建订单失败", e);
                throw new RuntimeException("创建订单失败", e);
            }
        });
    }

    @Override
    public ApiResponse<OrderDTO> getOrderByOrderNo(String orderNo) {
        try {
            if (StrUtil.isBlank(orderNo)) {
                throw OrderException.orderNotFound(orderNo);
//                return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "订单号不能为空");
            }

            Order order = getOne(new QueryWrapper<Order>().eq("order_no", orderNo));
            if (order == null) {
                throw OrderException.orderNotFound(orderNo);
//                return ApiResponse.error(ResponseCode.NOT_FOUND, "订单不存在");
            }

            OrderDTO result = buildOrderDTO(order);
            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("查询订单失败", e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderNo", orderNo);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_QUERY_FAILED", errorDetails);
            throw new BusinessException("查询订单失败" + e.getMessage());
        }
    }

    @Override
    public ApiResponse<OrderDTO> getOrderById(Long orderId) {
        try {
            if (orderId == null || orderId <= 0) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                TraceUtils.recordError("INVALID_ORDER_ID", errorDetails);
                throw OrderException.orderNotFound(orderId);
//                return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "订单ID无效");
            }

            Order order = getById(orderId);
            if (order == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                TraceUtils.recordError("ORDER_NOT_FOUND", errorDetails);
                throw OrderException.orderNotFound(orderId);
//                return ApiResponse.error(ResponseCode.NOT_FOUND, "订单不存在");
            }

            OrderDTO result = buildOrderDTO(order);
            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("查询订单失败", e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", orderId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_QUERY_FAILED", errorDetails);
            throw new BusinessException("查询订单失败" + e.getMessage());
//            return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR, "查询订单失败");
        }
    }

    @Override
    public ApiResponse<List<OrderDTO>> getUserOrders(Long userId, String status, Integer page, Integer size) {
        try {
            if (userId == null || userId <= 0) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("userId", userId);
                TraceUtils.recordError("INVALID_USER_ID", errorDetails);
                throw new IllegalArgumentException("用户ID无效");
//                return ApiResponse.error(ResponseCode.INVALID_PARAMETER, "用户ID无效");
            }

            // 设置默认分页参数
            if (page == null || page < 1) page = 1;
            if (size == null || size < 1) size = 10;
            if (size > 100) size = 100; // 限制最大页面大小

            QueryWrapper<Order> queryWrapper = new QueryWrapper<Order>()
                    .eq("user_id", userId)
                    .orderByDesc("created_at");

            // 如果指定了状态，添加状态过滤
            if (StrUtil.isNotBlank(status)) {
                queryWrapper.eq("status", status);
            }

            IPage<Order> pageResult = page(new Page<>(page, size), queryWrapper);

            List<OrderDTO> orderList = new ArrayList<>();
            for (Order order : pageResult.getRecords()) {
                orderList.add(buildOrderDTO(order));
            }

            return ApiResponse.success(orderList, "获取订单列表成功");

        } catch (Exception e) {
            log.error("获取用户订单列表失败", e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_LIST_FAILED", errorDetails);
            throw new BusinessException("获取订单列表失败" + e.getMessage());
//            return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR, "获取订单列表失败");
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> cancelOrder(Long orderId, Long userId) {
        try {
            if (!checkOrderOwnership(orderId, userId)) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("userId", userId);
                TraceUtils.recordError("FORBIDDEN", errorDetails);
                throw new AuthorizationException("无权操作此订单");
//                return ApiResponse.error(ResponseCode.FORBIDDEN, "无权操作此订单");
            }

            Order order = getById(orderId);
            if (order == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                TraceUtils.recordError("ORDER_NOT_FOUND", errorDetails);
                throw OrderException.orderNotFound(orderId);
//                return ApiResponse.error(ResponseCode.NOT_FOUND, "订单不存在");
            }

            // 只有待支付状态的订单可以取消
            if (!OrderStatusEnum.PENDING.getCode().equals(order.getStatus())) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("currentStatus", order.getStatus());
                TraceUtils.recordError("ORDER_STATUS_INVALID", errorDetails);
                throw OrderException.orderStatusInvalid(order.getOrderNo(), order.getStatus());
//                return ApiResponse.error(ResponseCode.BUSINESS_ERROR, "订单状态不允许取消");
            }

            // 更新订单状态为已取消
            order.setStatus(OrderStatusEnum.CANCELLED.getCode());
            updateById(order);

            // 如果订单使用了优惠码，释放优惠码使用次数
            if (order.getDiscountCodeId() != null) {
                try {
                    discountCodeService.cancelDiscountCodeUsage(orderId);
                    
                    Map<String, Object> discountReleaseDetails = new HashMap<>();
                    discountReleaseDetails.put("orderId", orderId);
                    discountReleaseDetails.put("discountCodeId", order.getDiscountCodeId());
                    TraceUtils.recordBusinessEvent("DISCOUNT_CODE_RELEASED_ON_CANCEL", discountReleaseDetails);
                    
                    log.info("订单取消，已释放优惠码: orderId={}, discountCodeId={}", 
                            orderId, order.getDiscountCodeId());
                } catch (Exception e) {
                    // 优惠码释放失败不应该影响订单取消，只记录错误日志
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", orderId);
                    errorDetails.put("discountCodeId", order.getDiscountCodeId());
                    errorDetails.put("error", e.getMessage());
                    TraceUtils.recordError("DISCOUNT_CODE_RELEASE_FAILED_ON_CANCEL", errorDetails);
                    log.error("订单取消时释放优惠码失败: orderId={}, discountCodeId={}", 
                            orderId, order.getDiscountCodeId(), e);
                }
            }

            Map<String, Object> cancelDetails = new HashMap<>();
            cancelDetails.put("orderId", orderId);
            cancelDetails.put("userId", userId);
            cancelDetails.put("hasDiscountCode", order.getDiscountCodeId() != null);
            TraceUtils.recordBusinessEvent("ORDER_CANCELLED", cancelDetails);

            return ApiResponse.success(null, "订单取消成功");

        } catch (Exception e) {
            log.error("取消订单失败", e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", orderId);
            errorDetails.put("userId", userId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_CANCEL_FAILED", errorDetails);
            throw new BusinessException("取消订单失败" + e.getMessage());
//            return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR, "取消订单失败");
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> deleteOrder(Long orderId, Long userId) {
        try {
            if (!checkOrderOwnership(orderId, userId)) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("userId", userId);
                TraceUtils.recordError("FORBIDDEN", errorDetails);
                throw new AuthorizationException("无权操作此订单");
//                return ApiResponse.error(ResponseCode.FORBIDDEN, "无权操作此订单");
            }

            Order order = getById(orderId);
            if (order == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                TraceUtils.recordError("ORDER_NOT_FOUND", errorDetails);
                throw OrderException.orderNotFound(orderId);
//                return ApiResponse.error(ResponseCode.NOT_FOUND, "订单不存在");
            }

            // 只有已取消的订单可以删除
            if (!OrderStatusEnum.CANCELLED.getCode().equals(order.getStatus())) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("currentStatus", order.getStatus());
                TraceUtils.recordError("ORDER_STATUS_INVALID", errorDetails);
                throw OrderException.orderStatusInvalid(order.getOrderNo(), order.getStatus());
//                return ApiResponse.error(ResponseCode.BUSINESS_ERROR, "只能删除已取消的订单");
            }

            // 逻辑删除
            removeById(orderId);

            Map<String, Object> deleteDetails = new HashMap<>();
            deleteDetails.put("orderId", orderId);
            deleteDetails.put("userId", userId);
            TraceUtils.recordBusinessEvent("ORDER_DELETED", deleteDetails);

            return ApiResponse.success(null, "订单删除成功");

        } catch (Exception e) {
            log.error("删除订单失败", e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", orderId);
            errorDetails.put("userId", userId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_DELETE_FAILED", errorDetails);
            throw new BusinessException("删除订单失败" + e.getMessage());
//            return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR, "删除订单失败");
        }
    }

    @Override
    public boolean checkOrderOwnership(Long orderId, Long userId) {
        if (orderId == null || userId == null) {
            return false;
        }

        Order order = getById(orderId);
        return order != null && userId.equals(order.getUserId());
    }

    @Override
    public ApiResponse<DiscountCodeValidationDTO> previewOrderWithDiscount(Long userId, Long packagePriceId, String discountCode) {
        return TraceUtils.executeWithTrace("previewOrderWithDiscount", () -> {
            try {
                Map<String, Object> startDetails = new HashMap<>();
                startDetails.put("userId", userId);
                startDetails.put("packagePriceId", packagePriceId);
                startDetails.put("discountCode", discountCode);
                TraceUtils.recordBusinessEvent("ORDER_PREVIEW_DISCOUNT_START", startDetails);

                // 1. 验证套餐价格
                PackagePrice packagePrice = packagePriceMapper.selectById(packagePriceId);
                if (packagePrice == null || packagePrice.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("packagePriceId", packagePriceId);
                    TraceUtils.recordError("PACKAGE_PRICE_NOT_FOUND", errorDetails);
                    throw PackageException.priceNotFound(packagePriceId);
                }

                // 2. 验证套餐
                Package pkg = packageMapper.selectById(packagePrice.getPackageId());
                if (pkg == null || pkg.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("packageId", packagePrice.getPackageId());
                    TraceUtils.recordError("PACKAGE_NOT_FOUND", errorDetails);
                    throw PackageException.packageNotFound(packagePrice.getPackageId());
                }

                // 3. 预验证优惠码（不消费使用次数）
                ApiResponse<DiscountCodeValidationDTO> result = discountCodeService.validateDiscountCode(
                        discountCode, pkg.getId(), packagePrice.getSalePrice(), userId);

                if (result.getSuccess()) {
                    // 添加预览信息
                    DiscountCodeValidationDTO data = result.getData();
                    data.setOriginalAmount(packagePrice.getSalePrice());
                    data.setFinalAmount(packagePrice.getSalePrice().subtract(data.getDiscountAmount()));
                    data.setPackageName(pkg.getDisplayName());
                    data.setBillingInfo(getBillingDisplayText(packagePrice.getBillingCycle(), packagePrice.getCycleCount()));

                    Map<String, Object> successDetails = new HashMap<>();
                    successDetails.put("userId", userId);
                    successDetails.put("discountCode", discountCode);
                    successDetails.put("discountAmount", data.getDiscountAmount());
                    successDetails.put("originalAmount", data.getOriginalAmount());
                    successDetails.put("finalAmount", data.getFinalAmount());
                    TraceUtils.recordBusinessEvent("ORDER_PREVIEW_DISCOUNT_SUCCESS", successDetails);
                }

                return result;

            } catch (PackageException e) {
                throw e;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("userId", userId);
                errorDetails.put("packagePriceId", packagePriceId);
                errorDetails.put("discountCode", discountCode);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("ORDER_PREVIEW_DISCOUNT_FAILED", errorDetails);
                log.error("预览订单优惠码失败", e);
                throw new BusinessException(e.getMessage());
            }
        });
    }

    @Override
    public ApiResponse<Map<String, Object>> getOrderDiscountInfo(Long orderId) {
        return TraceUtils.executeWithTrace("getOrderDiscountInfo", () -> {
            try {
                Map<String, Object> startDetails = new HashMap<>();
                startDetails.put("orderId", orderId);
                TraceUtils.recordBusinessEvent("ORDER_DISCOUNT_INFO_START", startDetails);

                // 1. 获取订单信息
                Order order = getById(orderId);
                if (order == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", orderId);
                    TraceUtils.recordError("ORDER_NOT_FOUND", errorDetails);
                    throw OrderException.orderNotFound(orderId);
                }

                Map<String, Object> discountInfo = new HashMap<>();
                discountInfo.put("orderId", orderId);
                discountInfo.put("orderNo", order.getOrderNo());
                discountInfo.put("originalAmount", order.getOriginalAmount());
                discountInfo.put("discountAmount", order.getDiscountAmount());
                discountInfo.put("finalAmount", order.getFinalAmount());
                discountInfo.put("hasDiscount", order.getDiscountCodeId() != null);

                // 2. 如果订单使用了优惠码，获取优惠码详情
                if (order.getDiscountCodeId() != null) {
                    try {
                        // 获取优惠码使用记录
                        ApiResponse<Map<String, Object>> usageResult = discountCodeService.getDiscountCodeUsageInfo(
                                order.getDiscountCodeId(), orderId);
                        
                        if (usageResult.getSuccess() && usageResult.getData() != null) {
                            Map<String, Object> usageInfo = usageResult.getData();
                            discountInfo.put("discountCodeInfo", usageInfo);
                            discountInfo.put("discountCode", usageInfo.get("discountCode"));
                            discountInfo.put("discountType", usageInfo.get("discountType"));
                            discountInfo.put("discountValue", usageInfo.get("discountValue"));
                            discountInfo.put("usedAt", usageInfo.get("usedAt"));
                        }
                    } catch (Exception e) {
                        log.warn("获取优惠码使用信息失败: orderId={}, discountCodeId={}", 
                                orderId, order.getDiscountCodeId(), e);
                        discountInfo.put("discountCodeInfo", "优惠码信息获取失败");
                    }
                } else {
                    discountInfo.put("discountCodeInfo", null);
                    discountInfo.put("discountCode", null);
                }

                // 3. 计算优惠比例
                if (order.getOriginalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal discountRate = order.getDiscountAmount()
                            .divide(order.getOriginalAmount(), 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    discountInfo.put("discountRate", discountRate);
                } else {
                    discountInfo.put("discountRate", BigDecimal.ZERO);
                }

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("orderId", orderId);
                successDetails.put("hasDiscount", order.getDiscountCodeId() != null);
                successDetails.put("discountAmount", order.getDiscountAmount());
                TraceUtils.recordBusinessEvent("ORDER_DISCOUNT_INFO_SUCCESS", successDetails);

                return ApiResponse.success(discountInfo, "获取订单优惠码信息成功");

            } catch (OrderException e) {
                throw e;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("ORDER_DISCOUNT_INFO_FAILED", errorDetails);
                log.error("获取订单优惠码信息失败", e);
                throw new BusinessException("获取订单优惠码信息失败: " + e.getMessage());
            }
        });
    }

    // ================== 私有方法 ==================

    private Order buildOrder(Long userId, Package pkg, PackagePrice packagePrice,
                             Long discountCodeId, BigDecimal discountAmount) {
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setPackageId(pkg.getId());
        order.setPackagePriceId(packagePrice.getId());
        order.setDiscountCodeId(discountCodeId);
        order.setOriginalAmount(packagePrice.getOriginalPrice());
        order.setDiscountAmount(discountAmount);
        order.setFinalAmount(packagePrice.getSalePrice().subtract(discountAmount));
        order.setCurrency(packagePrice.getCurrency());
        order.setBillingCycle(packagePrice.getBillingCycle());
        order.setCycleCount(packagePrice.getCycleCount());
        order.setStatus(OrderStatusEnum.PENDING.getCode());
        order.setOrderType("NEW");
        order.setExpiredAt(LocalDateTime.now().plusMinutes(30)); // 30分钟过期
        return order;
    }

    private OrderDTO buildOrderDTO(Order order) {
        OrderDTO dto = new OrderDTO()
                // 不设置orderId，避免暴露数据库自增主键
                .setOrderNo(order.getOrderNo())
                .setOriginalAmount(order.getOriginalAmount())
                .setDiscountAmount(order.getDiscountAmount())
                .setFinalAmount(order.getFinalAmount())
                .setCurrency(order.getCurrency())
                .setStatus(order.getStatus())
                .setExpiredAt(order.getExpiredAt())
                .setCreatedAt(order.getCreatedAt());

        // 获取套餐和价格信息
        try {
            Package pkg = packageMapper.selectById(order.getPackageId());
            PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());

            if (pkg != null) {
                dto.setPackageName(pkg.getDisplayName())
                        .setPackageFeatures(pkg.getFeatures());
            }

            if (packagePrice != null) {
                dto.setBillingInfo(getBillingDisplayText(packagePrice.getBillingCycle(), packagePrice.getCycleCount()));
            }
        } catch (Exception e) {
            log.warn("获取订单关联信息失败: orderId={}", order.getId(), e);
        }

        return dto;
    }

    private OrderDTO buildOrderDTO(Order order, Package pkg, PackagePrice packagePrice) {
        return new OrderDTO()
                // 不设置orderId，避免暴露数据库自增主键
                .setOrderNo(order.getOrderNo())
                .setPackageName(pkg.getDisplayName())
                .setPackageFeatures(pkg.getFeatures())
                .setBillingInfo(getBillingDisplayText(packagePrice.getBillingCycle(), packagePrice.getCycleCount()))
                .setOriginalAmount(order.getOriginalAmount())
                .setDiscountAmount(order.getDiscountAmount())
                .setFinalAmount(order.getFinalAmount())
                .setCurrency(order.getCurrency())
                .setStatus(order.getStatus())
                .setExpiredAt(order.getExpiredAt())
                .setCreatedAt(order.getCreatedAt());
    }

    private String getBillingDisplayText(String billingCycle, Integer cycleCount) {
        String unit;
        switch (billingCycle) {
            case "DAY":
                unit = "天";
                break;
            case "MONTH":
                unit = "个月";
                break;
            case "QUARTER":
                unit = "季度";
                break;
            case "YEAR":
                unit = "年";
                break;
            default:
                unit = billingCycle;
        }
        return cycleCount + unit;
    }

    private String generateOrderNo() {
        return "ORD" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + IdUtil.fastSimpleUUID().substring(0, 6).toUpperCase();
    }

    /**
     * 检查用户未付款订单数量限制
     * 如果用户有超过4个未付款订单，则抛出异常
     *
     * @param userId 用户ID
     * @throws OrderException 当未付款订单数量超过限制时
     */
    private void checkUserPendingOrdersLimit(Long userId) {
        try {
            // 查询用户未付款订单数量
            QueryWrapper<Order> queryWrapper = new QueryWrapper<Order>()
                    .eq("user_id", userId)
                    .eq("status", OrderStatusEnum.PENDING.getCode());

            long pendingOrderCount = count(queryWrapper);

            Map<String, Object> checkDetails = new HashMap<>();
            checkDetails.put("userId", userId);
            checkDetails.put("pendingOrderCount", pendingOrderCount);
            TraceUtils.recordBusinessEvent("ORDER_LIMIT_CHECK", checkDetails);

            if (pendingOrderCount >= 4) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("userId", userId);
                errorDetails.put("pendingOrderCount", pendingOrderCount);
                errorDetails.put("maxAllowed", 4);
                TraceUtils.recordError("ORDER_LIMIT_EXCEEDED", errorDetails);

                throw new OrderException("您有太多未付款的订单，请先完成或取消现有订单后再创建新订单。当前未付款订单数量：" + pendingOrderCount);
            }

        } catch (OrderException e) {
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("检查用户订单限制失败: userId={}", userId, e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ORDER_LIMIT_CHECK_FAILED", errorDetails);
            throw new BusinessException("检查订单限制失败");
        }
    }

    // ================== 续费相关方法 ==================

    @Override
    public ApiResponse<RenewalPreviewDTO> previewRenewal(Long userId, Long packagePriceId, String discountCode) {
        try {
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("userId", userId);
            requestDetails.put("packagePriceId", packagePriceId);
            requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
            TraceUtils.recordBusinessEvent("RENEWAL_PREVIEW_START", requestDetails);

            // 1. 获取用户当前订阅
            UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
            if (currentSubscription == null) {
                return ApiResponse.error(40001, "用户没有活跃订阅");
            }

            // 2. 验证套餐价格
            PackagePrice packagePrice = packagePriceMapper.selectById(packagePriceId);
            if (packagePrice == null || packagePrice.getStatus() != 1) {
                return ApiResponse.error(40002, "套餐价格不存在或已禁用");
            }

            // 3. 验证是否为同一套餐的续费
            if (!packagePrice.getPackageId().equals(currentSubscription.getPackageId())) {
                return ApiResponse.error(40003, "续费套餐必须与当前订阅套餐一致");
            }

            // 4. 计算续费价格
            BigDecimal originalAmount = packagePrice.getSalePrice();
            BigDecimal discountAmount = BigDecimal.ZERO;
            RenewalPreviewDTO.DiscountInfo discountInfo = null;

            // 5. 应用优惠码
            if (StrUtil.isNotBlank(discountCode)) {
                Package pkg = packageMapper.selectById(packagePrice.getPackageId());
                ApiResponse<DiscountCodeValidationDTO> validationResult =
                    discountCodeService.validateDiscountCode(discountCode, pkg.getId(), originalAmount, userId);

                if (validationResult.getSuccess()) {
                    DiscountCodeValidationDTO validation = validationResult.getData();
                    discountAmount = validation.getDiscountAmount();
                    discountInfo = RenewalPreviewDTO.DiscountInfo.builder()
                        .discountCode(discountCode)
                        .discountType(validation.getDiscountType())
                        .discountValue(validation.getDiscountValue())
                        .build();
                } else {
                    return ApiResponse.error(validationResult.getCode(), validationResult.getMessage());
                }
            }

            // 6. 计算新的到期时间
            LocalDateTime newEndTime = calculateNewEndTimeForRenewal(currentSubscription.getEndTime(), packagePrice);

            // 7. 构建预览结果
            RenewalPreviewDTO result = RenewalPreviewDTO.builder()
                .packagePriceId(packagePriceId)
                .originalAmount(originalAmount)
                .discountAmount(discountAmount)
                .finalAmount(originalAmount.subtract(discountAmount))
                .currency(packagePrice.getCurrency())
                .billingInfo("续费" + getBillingDisplayText(packagePrice.getBillingCycle(), packagePrice.getCycleCount()))
                .currentEndTime(currentSubscription.getEndTime())
                .newEndTime(newEndTime)
                .discountInfo(discountInfo)
                .build();

            return ApiResponse.success(result, "续费预览成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("packagePriceId", packagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("RENEWAL_PREVIEW_FAILED", errorDetails);
            log.error("预览续费价格失败", e);
            return ApiResponse.error(50000, "预览续费价格失败");
        }
    }

    @Override
    @Transactional
    public ApiResponse<OrderDTO> createRenewalOrder(Long userId, Long packagePriceId, String discountCode) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("userId", userId);
                requestDetails.put("packagePriceId", packagePriceId);
                requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
                TraceUtils.recordBusinessEvent("RENEWAL_ORDER_CREATE_START", requestDetails);

                // 1. 获取用户当前订阅
                UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
                if (currentSubscription == null) {
                    throw new BusinessException("用户没有活跃订阅");
                }

                // 2. 验证套餐价格
                PackagePrice packagePrice = packagePriceMapper.selectById(packagePriceId);
                if (packagePrice == null || packagePrice.getStatus() != 1) {
                    throw PackageException.priceNotFound(packagePriceId);
                }

                // 3. 验证是否为同一套餐的续费
                if (!packagePrice.getPackageId().equals(currentSubscription.getPackageId())) {
                    throw new BusinessException("续费套餐必须与当前订阅套餐一致");
                }

                // 4. 验证套餐信息
                Package pkg = packageMapper.selectById(packagePrice.getPackageId());
                if (pkg == null || pkg.getStatus() != 1) {
                    throw PackageException.packageNotFound(packagePrice.getPackageId());
                }

                // 5. 检查用户待支付订单数量限制
                checkUserPendingOrdersLimit(userId);

                // 6. 如果有优惠码，先验证
                DiscountCodeValidationDTO discountResult = null;
                if (StrUtil.isNotBlank(discountCode)) {
                    ApiResponse<DiscountCodeValidationDTO> validationResult =
                        discountCodeService.validateDiscountCodeForOrder(
                            discountCode, pkg.getId(), packagePrice.getSalePrice(), userId);

                    if (!validationResult.getSuccess()) {
                        throw new BusinessException(validationResult.getMessage());
                    }
                    discountResult = validationResult.getData();
                }

                // 7. 创建续费订单
                BigDecimal discountAmount = discountResult != null ? discountResult.getDiscountAmount() : BigDecimal.ZERO;
                Long discountCodeId = discountResult != null ? discountResult.getDiscountCodeId() : null;

                Order order = buildRenewalOrder(userId, pkg, packagePrice, discountCodeId, discountAmount);
                save(order);

                // 8. 如果有优惠码，消费使用次数
                if (discountResult != null) {
                    boolean consumed = discountCodeService.atomicUseDiscountCode(
                        discountResult.getDiscountCodeId(), userId, order.getId(), discountAmount);

                    if (!consumed) {
                        removeById(order.getId());
                        throw new BusinessException("优惠码已被其他用户使用完毕，请重新选择");
                    }
                }

                // 9. 构建返回结果
                OrderDTO result = buildOrderDTO(order, pkg, packagePrice);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("orderId", order.getId());
                successDetails.put("orderNo", order.getOrderNo());
                successDetails.put("orderType", "RENEWAL");
                TraceUtils.recordBusinessEvent("RENEWAL_ORDER_CREATE_SUCCESS", successDetails);

                return ApiResponse.success(result, "续费订单创建成功");

            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("userId", userId);
                errorDetails.put("packagePriceId", packagePriceId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("RENEWAL_ORDER_CREATE_FAILED", errorDetails);
                log.error("创建续费订单失败", e);
                throw new RuntimeException("创建续费订单失败", e);
            }
        });
    }

    // ================== 升级相关方法 ==================

    @Override
    public ApiResponse<UpgradePreviewDTO> previewUpgrade(Long userId, Long targetPackagePriceId, String discountCode) {
        try {
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("userId", userId);
            requestDetails.put("targetPackagePriceId", targetPackagePriceId);
            requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
            TraceUtils.recordBusinessEvent("UPGRADE_PREVIEW_START", requestDetails);

            // 1. 获取用户当前订阅
            UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
            if (currentSubscription == null) {
                return ApiResponse.error(40001, "用户没有活跃订阅");
            }

            // 2. 验证目标套餐价格
            PackagePrice targetPrice = packagePriceMapper.selectById(targetPackagePriceId);
            if (targetPrice == null || targetPrice.getStatus() != 1) {
                return ApiResponse.error(40002, "目标套餐价格不存在或已禁用");
            }

            // 3. 验证目标套餐
            Package targetPackage = packageMapper.selectById(targetPrice.getPackageId());
            if (targetPackage == null || targetPackage.getStatus() != 1) {
                return ApiResponse.error(40003, "目标套餐不存在或已禁用");
            }

            // 4. 验证是否为升级（不允许降级）
            Package currentPackage = packageMapper.selectById(currentSubscription.getPackageId());
            if (targetPackage.getSortOrder() <= currentPackage.getSortOrder()) {
                return ApiResponse.error(40004, "只能升级到更高级的套餐");
            }

            // 5. 计算升级费用
            BigDecimal upgradeCost = calculateUpgradeCost(currentSubscription, targetPrice);
            BigDecimal discountAmount = BigDecimal.ZERO;
            UpgradePreviewDTO.DiscountInfo discountInfo = null;

            // 6. 应用优惠码
            if (StrUtil.isNotBlank(discountCode) && upgradeCost.compareTo(BigDecimal.ZERO) > 0) {
                ApiResponse<DiscountCodeValidationDTO> validationResult =
                    discountCodeService.validateDiscountCode(discountCode, targetPackage.getId(), upgradeCost, userId);

                if (validationResult.getSuccess()) {
                    DiscountCodeValidationDTO validation = validationResult.getData();
                    discountAmount = validation.getDiscountAmount();
                    discountInfo = UpgradePreviewDTO.DiscountInfo.builder()
                        .discountCode(discountCode)
                        .discountType(validation.getDiscountType())
                        .discountValue(validation.getDiscountValue())
                        .build();
                } else {
                    return ApiResponse.error(validationResult.getCode(), validationResult.getMessage());
                }
            }

            // 7. 构建预览结果
            BigDecimal currentRemainingValue = calculateRemainingValue(currentSubscription);
            long remainingDays = Math.max(0, java.time.Duration.between(LocalDateTime.now(), currentSubscription.getEndTime()).toDays());
            BigDecimal targetMonthlyPrice = calculateMonthlyPrice(targetPrice);
            BigDecimal targetRemainingValue = targetMonthlyPrice.multiply(BigDecimal.valueOf(remainingDays))
                .divide(BigDecimal.valueOf(30), 2, java.math.RoundingMode.HALF_UP);

            UpgradePreviewDTO result = UpgradePreviewDTO.builder()
                .upgradeType("UPGRADE")
                .currentPackage(UpgradePreviewDTO.CurrentPackageInfo.builder()
                    .packageId(currentPackage.getId())
                    .packageName(currentPackage.getDisplayName())
                    .remainingDays(remainingDays)
                    .remainingValue(currentRemainingValue)
                    .build())
                .targetPackage(UpgradePreviewDTO.TargetPackageInfo.builder()
                    .packageId(targetPackage.getId())
                    .packageName(targetPackage.getDisplayName())
                    .packagePriceId(targetPackagePriceId)
                    .monthlyPrice(targetMonthlyPrice)
                    .build())
                .calculation(UpgradePreviewDTO.CalculationInfo.builder()
                    .currentRemainingValue(currentRemainingValue)
                    .targetRemainingValue(targetRemainingValue)
                    .upgradeDifference(upgradeCost)
                    .discountAmount(discountAmount)
                    .finalAmount(upgradeCost.subtract(discountAmount))
                    .endTime(currentSubscription.getEndTime()) // 升级不改变到期时间
                    .remainingDays(remainingDays)
                    .build())
                .discountInfo(discountInfo)
                .build();

            return ApiResponse.success(result, "升级预览成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("targetPackagePriceId", targetPackagePriceId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("UPGRADE_PREVIEW_FAILED", errorDetails);
            log.error("预览升级价格失败", e);
            return ApiResponse.error(50000, "预览升级价格失败");
        }
    }

    @Override
    @Transactional
    public ApiResponse<OrderDTO> createUpgradeOrder(Long userId, Long targetPackagePriceId, String discountCode) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("userId", userId);
                requestDetails.put("targetPackagePriceId", targetPackagePriceId);
                requestDetails.put("hasDiscountCode", StrUtil.isNotBlank(discountCode));
                TraceUtils.recordBusinessEvent("UPGRADE_ORDER_CREATE_START", requestDetails);

                // 1. 获取用户当前订阅
                UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
                if (currentSubscription == null) {
                    throw new BusinessException("用户没有活跃订阅");
                }

                // 2. 验证目标套餐价格
                PackagePrice targetPrice = packagePriceMapper.selectById(targetPackagePriceId);
                if (targetPrice == null || targetPrice.getStatus() != 1) {
                    throw PackageException.priceNotFound(targetPackagePriceId);
                }

                // 3. 验证目标套餐
                Package targetPackage = packageMapper.selectById(targetPrice.getPackageId());
                if (targetPackage == null || targetPackage.getStatus() != 1) {
                    throw PackageException.packageNotFound(targetPrice.getPackageId());
                }

                // 4. 验证是否为升级
                Package currentPackage = packageMapper.selectById(currentSubscription.getPackageId());
                if (targetPackage.getSortOrder() <= currentPackage.getSortOrder()) {
                    throw new BusinessException("只能升级到更高级的套餐");
                }

                // 5. 计算升级费用
                BigDecimal upgradeCost = calculateUpgradeCost(currentSubscription, targetPrice);

                // 6. 检查用户待支付订单数量限制
                checkUserPendingOrdersLimit(userId);

                // 7. 如果有优惠码且升级费用大于0，验证优惠码
                DiscountCodeValidationDTO discountResult = null;
                if (StrUtil.isNotBlank(discountCode) && upgradeCost.compareTo(BigDecimal.ZERO) > 0) {
                    ApiResponse<DiscountCodeValidationDTO> validationResult =
                        discountCodeService.validateDiscountCodeForOrder(
                            discountCode, targetPackage.getId(), upgradeCost, userId);

                    if (!validationResult.getSuccess()) {
                        throw new BusinessException(validationResult.getMessage());
                    }
                    discountResult = validationResult.getData();
                }

                // 8. 创建升级订单
                BigDecimal discountAmount = discountResult != null ? discountResult.getDiscountAmount() : BigDecimal.ZERO;
                Long discountCodeId = discountResult != null ? discountResult.getDiscountCodeId() : null;

                Order order = buildUpgradeOrder(userId, targetPackage, targetPrice, discountCodeId, discountAmount, upgradeCost);
                save(order);

                // 9. 如果有优惠码，消费使用次数
                if (discountResult != null) {
                    boolean consumed = discountCodeService.atomicUseDiscountCode(
                        discountResult.getDiscountCodeId(), userId, order.getId(), discountAmount);

                    if (!consumed) {
                        removeById(order.getId());
                        throw new BusinessException("优惠码已被其他用户使用完毕，请重新选择");
                    }
                }

                // 10. 构建返回结果
                OrderDTO result = buildOrderDTO(order, targetPackage, targetPrice);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("orderId", order.getId());
                successDetails.put("orderNo", order.getOrderNo());
                successDetails.put("orderType", "UPGRADE");
                successDetails.put("upgradeCost", upgradeCost);
                TraceUtils.recordBusinessEvent("UPGRADE_ORDER_CREATE_SUCCESS", successDetails);

                return ApiResponse.success(result, "升级订单创建成功");

            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("userId", userId);
                errorDetails.put("targetPackagePriceId", targetPackagePriceId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("UPGRADE_ORDER_CREATE_FAILED", errorDetails);
                log.error("创建升级订单失败", e);
                throw new RuntimeException("创建升级订单失败", e);
            }
        });
    }

    // ================== 续费和升级相关辅助方法 ==================

    private LocalDateTime calculateNewEndTimeForRenewal(LocalDateTime currentEndTime, PackagePrice price) {
        LocalDateTime baseTime = currentEndTime.isAfter(LocalDateTime.now()) ?
            currentEndTime : LocalDateTime.now();

        switch (price.getBillingCycle()) {
            case "DAY":
                return baseTime.plusDays(price.getCycleCount());
            case "MONTH":
                return baseTime.plusMonths(price.getCycleCount());
            case "QUARTER":
                return baseTime.plusMonths(price.getCycleCount() * 3);
            case "YEAR":
                return baseTime.plusYears(price.getCycleCount());
            default:
                return baseTime.plusMonths(price.getCycleCount());
        }
    }

    private Order buildRenewalOrder(Long userId, Package pkg, PackagePrice packagePrice, Long discountCodeId, BigDecimal discountAmount) {
        Order order = buildOrder(userId, pkg, packagePrice, discountCodeId, discountAmount);
        order.setOrderType("RENEWAL");
        // 更新订单号前缀
        order.setOrderNo("RNW" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + IdUtil.fastSimpleUUID().substring(0, 6).toUpperCase());
        return order;
    }

    private Order buildUpgradeOrder(Long userId, Package targetPackage, PackagePrice targetPrice, Long discountCodeId, BigDecimal discountAmount, BigDecimal upgradeCost) {
        // 升级订单的金额是升级差价
        Order order = new Order();
        order.setUserId(userId);
        order.setOrderNo("UPG" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + IdUtil.fastSimpleUUID().substring(0, 6).toUpperCase());
        order.setOrderType("UPGRADE");
        order.setPackageId(targetPackage.getId());
        order.setPackagePriceId(targetPrice.getId());
        order.setOriginalAmount(upgradeCost);
        order.setDiscountCodeId(discountCodeId);
        order.setDiscountAmount(discountAmount != null ? discountAmount : BigDecimal.ZERO);
        order.setFinalAmount(upgradeCost.subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO));
        order.setCurrency(targetPrice.getCurrency());
        order.setStatus(OrderStatusEnum.PENDING.getCode());
        order.setExpiredAt(LocalDateTime.now().plusMinutes(30));
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        return order;
    }

    private BigDecimal calculateUpgradeCost(UserSubscription currentSubscription, PackagePrice targetPrice) {
        // 计算当前订阅剩余价值
        BigDecimal currentRemainingValue = calculateRemainingValue(currentSubscription);

        // 计算剩余天数
        long remainingDays = java.time.Duration.between(LocalDateTime.now(), currentSubscription.getEndTime()).toDays();

        if (remainingDays <= 0) {
            // 如果已过期，升级费用为目标套餐的最小计费周期价格
            return calculateMonthlyPrice(targetPrice);
        }

        // 计算目标套餐在剩余时间内的价值
        BigDecimal targetMonthlyPrice = calculateMonthlyPrice(targetPrice);
        BigDecimal targetRemainingValue = targetMonthlyPrice
            .multiply(BigDecimal.valueOf(remainingDays))
            .divide(BigDecimal.valueOf(30), 2, java.math.RoundingMode.HALF_UP);

        // 升级差价 = 目标套餐剩余时间价值 - 当前套餐剩余价值
        BigDecimal upgradeDifference = targetRemainingValue.subtract(currentRemainingValue);

        // 确保差价不为负数（理论上不应该出现，因为已验证是升级）
        return upgradeDifference.max(BigDecimal.ZERO);
    }

    private BigDecimal calculateRemainingValue(UserSubscription subscription) {
        // 获取当前套餐的月均价格
        List<PackagePrice> prices = packagePriceMapper.selectList(
            new QueryWrapper<PackagePrice>()
                .eq("package_id", subscription.getPackageId())
                .eq("status", 1)
                .eq("billing_cycle", "MONTH")
                .eq("cycle_count", 1)
                .last("LIMIT 1")
        );

        if (prices.isEmpty()) {
            return BigDecimal.ZERO;
        }

        PackagePrice monthlyPrice = prices.get(0);
        long remainingDays = Duration.between(LocalDateTime.now(), subscription.getEndTime()).toDays();

        if (remainingDays <= 0) {
            return BigDecimal.ZERO;
        }

        return monthlyPrice.getSalePrice()
            .multiply(BigDecimal.valueOf(remainingDays))
            .divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateMonthlyPrice(PackagePrice price) {
        switch (price.getBillingCycle()) {
            case "DAY":
                return price.getSalePrice().multiply(BigDecimal.valueOf(30)).divide(BigDecimal.valueOf(price.getCycleCount()), 2, java.math.RoundingMode.HALF_UP);
            case "MONTH":
                return price.getSalePrice().divide(BigDecimal.valueOf(price.getCycleCount()), 2, java.math.RoundingMode.HALF_UP);
            case "QUARTER":
                return price.getSalePrice().divide(BigDecimal.valueOf(price.getCycleCount() * 3), 2, java.math.RoundingMode.HALF_UP);
            case "YEAR":
                return price.getSalePrice().divide(BigDecimal.valueOf(price.getCycleCount() * 12), 2, java.math.RoundingMode.HALF_UP);
            default:
                return price.getSalePrice();
        }
    }

}