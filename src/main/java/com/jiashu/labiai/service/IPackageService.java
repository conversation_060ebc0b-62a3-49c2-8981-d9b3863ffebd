package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.entity.Package;
import com.jiashu.labiai.entity.PackagePrice;

import java.util.List;

/**
 * 套餐服务接口
 */
public interface IPackageService extends IService<Package> {
    
    /**
     * 获取所有可用套餐列表
     *
     * @return 套餐列表
     */
    ApiResponse<List<PackageDTO>> getAvailablePackages();
    
    /**
     * 根据ID获取套餐详情
     *
     * @param packageId 套餐ID
     * @return 套餐详情
     */
    ApiResponse<PackageDTO> getPackageDetail(Long packageId);
    
    /**
     * 获取套餐的所有价格选项
     *
     * @param packageId 套餐ID
     * @return 价格选项列表
     */
    ApiResponse<List<PackagePriceDTO>> getPackagePrices(Long packageId);
    
    /**
     * 根据ID获取套餐价格详情
     *
     * @param priceId 价格ID
     * @return 价格详情
     */
    ApiResponse<PackagePriceDTO> getPackagePriceDetail(Long priceId);
    
    /**
     * 根据ID查询套餐
     *
     * @param packageId 套餐ID
     * @return 套餐实体
     */
    Package findPackageById(Long packageId);
    
    /**
     * 根据ID查询套餐价格
     *
     * @param priceId 价格ID
     * @return 套餐价格实体
     */
    PackagePrice findPackagePriceById(Long priceId);

    /**
     * 获取续费选项
     *
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 续费选项
     */
    ApiResponse<RenewalOptionsDTO> getRenewalOptions(Long userId, Long packageId);

    /**
     * 获取升级选项
     *
     * @param userId 用户ID
     * @return 升级选项
     */
    ApiResponse<UpgradeOptionsDTO> getUpgradeOptions(Long userId);
}