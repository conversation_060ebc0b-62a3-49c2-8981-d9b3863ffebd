package com.jiashu.labiai.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jiashu.labiai.dto.request.auth.LoginRequest;
import com.jiashu.labiai.dto.request.user.ChangePasswordRequest;
import com.jiashu.labiai.dto.response.auth.LoginResponse;
import com.jiashu.labiai.dto.response.user.ChangePasswordResponse;
import com.jiashu.labiai.entity.LoginContext;
import com.jiashu.labiai.entity.LoginLogs;
import com.jiashu.labiai.entity.Users;
import com.jiashu.labiai.enums.LoginMethod;
import com.jiashu.labiai.enums.LoginResult;
import com.jiashu.labiai.enums.SecurityEventType;
import com.jiashu.labiai.enums.UserStatus;
import com.jiashu.labiai.exception.AuthenticationException;
import com.jiashu.labiai.exception.RateLimitException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 增强认证服务 - 集成Sa-Token与现有设备指纹安全机制
 */
@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class EnhancedAuthService {

    private final IUsersService usersService;
    private final IRateLimitService rateLimitService;
    private final RedisTemplate<String, String> redisTemplate;
    private final ISecurityEventsService securityEventsService;
    private final ILoginLogsService loginLogsService;
//    private final IDeviceSecurityService deviceSecurityService;
//    private final LoginLogsMapper loginLogsMapper;

    /**
     * 密码登录 - 集成Sa-Token与现有设备指纹安全机制
     */
    public LoginResponse login(LoginRequest request, LoginContext context) {
        try {
            // 1. 前置安全检查
            preLoginSecurityCheck(request.getEmail(), context);

            // 2. 基础认证
            Users user = authenticateUser(request.getEmail(), request.getPassword());

            // 3. 设备识别与风险评估
//            DeviceSecurityService.DeviceSecurityResult securityResult =
//                deviceSecurityService.verifyDeviceSecurity(user.getId(), context);
//
//            // 4. 登录风险评估
//            RiskAssessmentResult riskResult = assessLoginRisk(user, securityResult, context);
//
//            // 5. 根据风险等级决定认证策略
//            if (riskResult.getRiskScore() > 80) { // 高风险阈值
//                // 高风险登录，要求双因子验证
//                if (!request.hasTwoFactorCode()) {
//                    return buildTwoFactorRequired(user, securityResult, riskResult);
//                }
//                // 验证双因子
//                validateTwoFactor(user, request.getTwoFactorCode());
//            }

            // 6. Sa-Token登录
            SaTokenInfo saTokenInfo = performSaTokenLogin(user, context);

            // 7. 记录登录成功
            recordSuccessfulLogin(user, context, saTokenInfo);

            // 8. 后置处理
            postLoginProcessing(user, context);

            // 9. 构建响应
            return buildLoginResponse(user);

        } catch (AuthenticationException e) {
            // 记录失败登录
            recordFailedLogin(request.getEmail(), context, e.getMessage());
            throw e;
        } catch (Exception e) {
            context.setLoginResult(LoginResult.FAILED);
            log.error("登录过程发生异常", e);
            recordFailedLogin(request.getEmail(), context, "系统异常");
            throw new AuthenticationException("登录失败，请稍后重试");
        }
    }

    /**
     * 前置安全检查
     */
    private void preLoginSecurityCheck(String email, LoginContext context) {
        // 检查账号锁定状态
        Users user = findUserByEmail(email);

        if (user == null) {
            context.setLoginResult(LoginResult.ACCOUNT_NOT_FOUND);
            throw new AuthenticationException("邮箱或密码错误");
        }


        if (user.getLockedUntil() != null && user.getLockedUntil().isAfter(LocalDateTime.now())) {
            context.setLoginResult(LoginResult.ACCOUNT_LOCKED);
            Duration lockDuration = Duration.between(LocalDateTime.now(), user.getLockedUntil());
            throw new AuthenticationException(
                    String.format("账号已被锁定，请%d分钟后再试", lockDuration.toMinutes()));
        }

        // 检查登录频率限制
        if (!rateLimitService.checkUserLoginRate(user.getId())) {
            context.setLoginResult(LoginResult.RATE_LIMITED);
            throw new RateLimitException("账号登录过于频繁，请稍后重试");
        }

        if (!rateLimitService.checkIpLoginRate(context.getIpAddress())) {
            context.setLoginResult(LoginResult.RATE_LIMITED);
            throw new RateLimitException("该IP登录过于频繁，请稍后重试");
        }

        if (!rateLimitService.checkDeviceLoginRate(context.getDeviceId())) {
            context.setLoginResult(LoginResult.RATE_LIMITED);
            throw new RateLimitException("该设备登录过于频繁，请稍后重试");
        }
    }

    /**
     * 基础用户认证
     */
    private Users authenticateUser(String email, String password) {
        Users user = findUserByEmail(email);
        if (user == null) {
            throw new AuthenticationException("邮箱或密码错误");
        }

        // 检查用户状态：1:正常 2:锁定 3:禁用 4:注销
        if (user.getStatus() != null) {
            if (user.getStatus() == UserStatus.LOCKED) {
                throw new AuthenticationException("账号已被锁定");
            } else if (user.getStatus() == UserStatus.DISABLED) {
                throw new AuthenticationException("账号已被禁用");
            } else if (user.getStatus() == UserStatus.DELETED) {
                throw new AuthenticationException("账号已注销");
            }
        }

        // 检查是否为第三方登录用户
//        if (user.getUserType() != null && user.getUserType() == 2 && user.getPasswordHash() == null) {
//            throw new LoginException("该账号为第三方登录账号，请使用对应的第三方登录方式");
//        }

        // 验证密码 - 使用MD5（适配现有系统）
        String md5Password = DigestUtil.md5Hex(password);
        if (!md5Password.equals(user.getPasswordHash())) {
            // 增加失败次数
            incrementFailedAttempts(user);

            // 检查是否需要锁定账号
//            if (user.getLoginFailedCount() != null && user.getLoginFailedCount() >= 5) {
//                lockAccount(user);
//                throw new LoginException("登录失败次数过多，账号已被锁定15分钟");
//            }
//
//            throw new LoginException("邮箱或密码错误");
        }

        // 重置失败次数
        resetFailedAttempts(user);

        return user;
    }

    /**
     * 登录风险评估
     */
//    private RiskAssessmentResult assessLoginRisk(Users user, DeviceSecurityService.DeviceSecurityResult securityResult, LoginContext context) {
//        int riskScore = 0;
//        List<String> riskFactors = new ArrayList<>();
//
//        // 设备风险
//        if (!securityResult.getDevice().getIsTrusted()) {
//            riskScore += 20;
//            riskFactors.add("非受信任设备");
//        }
//
//        // 新设备风险
//        if (securityResult.isNewDevice()) {
//            riskScore += 25;
//            riskFactors.add("新设备登录");
//        }
//
//        // IP变化风险
//        if (user.getLastLoginIp() != null && !user.getLastLoginIp().equals(context.getIpAddress())) {
//            riskScore += 15;
//            riskFactors.add("IP地址变化");
//        }
//
//        // 登录时间风险（异常时间登录）
//        if (isUnusualLoginTime(context.getLoginTime())) {
//            riskScore += 10;
//            riskFactors.add("异常时间登录");
//        }
//
//        // 地理位置风险
//        if (hasLocationChanged(user, context)) {
//            riskScore += 20;
//            riskFactors.add("地理位置变化");
//        }
//
//        return RiskAssessmentResult.builder()
//            .riskScore(riskScore)
//            .riskFactors(riskFactors)
//            .isRiskyLogin(riskScore > 60)
//            .build();
//    }

    /**
     * 执行Sa-Token登录
     */
    private SaTokenInfo performSaTokenLogin(Users user, LoginContext context) {
        SaLoginParameter loginParameter = new SaLoginParameter();
        loginParameter.setDeviceId(context.getDeviceId());
        if (!context.isRememberMe()) {
            // 设置Token有效期为2小时
            loginParameter.setTimeout(2 * 60 * 60);
        }

        // 执行Sa-Token登录
        StpUtil.login(user.getId(), loginParameter);

        StpUtil.getTokenSession().set("loginContext", context);
        // 获取Token信息
        return StpUtil.getTokenInfo();
    }

    /**
     * 记录成功登录
     */
    private void recordSuccessfulLogin(Users user, LoginContext context, SaTokenInfo tokenInfo) {
        LoginLogs loginLog = new LoginLogs();
        loginLog.setUserId(user.getId());
        loginLog.setEmail(user.getEmail());
        loginLog.setDeviceId(context.getDeviceId());
        loginLog.setDeviceHash(context.getDeviceHash());
        loginLog.setIpAddress(context.getIpAddress());
        loginLog.setLoginType(context.getLoginType());
        loginLog.setLoginResult(LoginResult.SUCCESS);
        loginLog.setSatokenValue(tokenInfo.getTokenValue());
        loginLog.setSessionTimeout(tokenInfo.sessionTimeout);
        loginLog.setCreatedAt(LocalDateTime.now());
        loginLogsService.save(loginLog);
    }

    /**
     * 记录失败登录
     */
    private void recordFailedLogin(String email, LoginContext context, String reason) {
        try {
            LoginLogs loginLog = new LoginLogs();
            loginLog.setEmail(email);
            loginLog.setDeviceId(context.getDeviceId());
            loginLog.setDeviceHash(context.getDeviceHash());
            loginLog.setIpAddress(context.getIpAddress());
            loginLog.setUserAgent(context.getDeviceInfo().getUserAgent());

            loginLog.setLoginType(context.getLoginType());
            loginLog.setLoginMethod(LoginMethod.PASSWORD);
            loginLog.setLoginResult(LoginResult.PASSWORD_ERROR);
            loginLog.setFailureReason(reason);
            loginLog.setCreatedAt(LocalDateTime.now());
            loginLogsService.save(loginLog);

        } catch (Exception e) {
            log.error("记录失败登录日志异常", e);
        }
    }

    /**
     * 后置处理
     */
    private void postLoginProcessing(Users user, LoginContext context) {
        // 更新用户最后登录信息
        user.setLastLoginAt(LocalDateTime.now());
        user.setLastLoginIp(context.getIpAddress());
        user.setLastLoginDeviceId(context.getDeviceId());
        user.setLastLoginType(LoginMethod.PASSWORD);
//        user.setLoginFailedCount(0); // 重置失败次数
        user.setLockedUntil(null); // 清除锁定状态
        usersService.updateById(user);

        // 检查并发登录数量限制
        checkConcurrentLoginLimit(user.getId());
    }

    /**
     * 构建登录响应
     */
    private LoginResponse buildLoginResponse(Users user) {
        return LoginResponse.builder()
                .email(user.getEmail()).build();
    }

//    /**
//     * 构建双因子验证要求响应
//     */
//    private LoginResponse buildTwoFactorRequired(Users user, DeviceSecurityService.DeviceSecurityResult securityResult,
//                                               RiskAssessmentResult riskResult) {
//        // TODO: 实现双因子验证响应
//        throw new LoginException("需要双因子验证，暂未实现");
//    }

//    /**
//     * 验证双因子
//     */
//    private void validateTwoFactor(Users user, String twoFactorCode) {
//        // TODO: 实现双因子验证逻辑
//        throw new LoginException("双因子验证暂未实现");
//    }

    /**
     * 检查并发登录限制
     */
    private void checkConcurrentLoginLimit(Long userId) {
        List<String> activeTokens = StpUtil.getTokenValueListByLoginId(userId);
        int maxLoginCount = 8; // 最大并发登录数

        if (activeTokens.size() > maxLoginCount) {
            // 踢掉最早的Token（简化实现）
            for (int i = 0; i < activeTokens.size() - maxLoginCount; i++) {
                StpUtil.kickoutByTokenValue(activeTokens.get(i));
            }
        }
    }

    // 辅助方法
    private Users findUserByEmail(String email) {
        LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Users::getEmail, email);
        return usersService.getOne(queryWrapper);
    }

    /**
     * 增加登录失败次数
     *
     * @param user 用户对象
     */
    private void incrementFailedAttempts(Users user) {
        if (user == null || user.getId() == null) {
            return;
        }
        String clientIp = TraceContext.getClientIPFromMDC();
        String deviceId = TraceContext.getVisitorId();
        try {
            // 获取当前环境信息


            // 1. 增加用户登录失败次数
            int userFailCount = rateLimitService.incrementUserLoginFailure(user.getId());

            // 2. 增加IP登录失败次数
            int ipFailCount = rateLimitService.incrementIpLoginFailure(clientIp);

            // 3. 增加设备登录失败次数
            int deviceFailCount = rateLimitService.incrementDeviceLoginFailure(deviceId);

//            // 4. 记录登录失败事件
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("userId", user.getId());
            eventData.put("userEmail", user.getEmail());
            eventData.put("ipAddress", clientIp);
            eventData.put("deviceId", deviceId);
            eventData.put("userFailCount", userFailCount);
            eventData.put("ipFailCount", ipFailCount);
            eventData.put("deviceFailCount", deviceFailCount);
//
//            // 根据失败次数确定事件类型
//            SecurityEventType eventType = (userFailCount >= 3)
//                ? SecurityEventType.MULTIPLE_LOGIN_FAILURES
//                : SecurityEventType.LOGIN_FAILED;
//
//            // 记录安全事件
//            securityEventsService.recordSecurityEvent(
//                eventType,
//                "登录失败",
//                String.format("用户[%s]登录失败，当前失败次数：%d", user.getEmail(), userFailCount),
//                user.getId(),
//                deviceId,
//                null,
//                clientIp,
//                null,
//                eventData
//            );

            // 5. 如果达到最大失败次数，锁定账户
//            if (userFailCount >= 5) {
//                // 锁定账户30分钟
//                LocalDateTime lockedUntil = LocalDateTime.now().plusMinutes(30);
//                user.setStatus(UserStatus.LOCKED);
//                user.setLockedUntil(lockedUntil);
//                usersService.updateById(user);
//
//                // 记录账户锁定事件
//                securityEventsService.recordSecurityEvent(
//                    SecurityEventType.ACCOUNT_LOCKED,
//                    "账户锁定",
//                    String.format("用户[%s]因多次登录失败被锁定，锁定至：%s", user.getEmail(), lockedUntil),
//                    user.getId(),
//                    deviceId,
//                    null,
//                    clientIp,
//                    null,
//                    eventData
//                );
//            }

//            log.info("增加登录失败次数: userId={}, userFailCount={}, ipFailCount={}, deviceFailCount={}",
//                user.getId(), userFailCount, ipFailCount, deviceFailCount);
            TraceUtils.recordBusinessEvent("login_failed", eventData);

        } catch (Exception e) {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("userId", user.getId());
            eventData.put("userEmail", user.getEmail());
            eventData.put("ipAddress", clientIp);
            eventData.put("deviceId", deviceId);
            eventData.put("error", e.getMessage());
            TraceUtils.recordError("login_failed_count_error", eventData);
        }
    }

    /**
     * 重置登录失败次数
     *
     * @param user 用户对象
     */
    private void resetFailedAttempts(Users user) {
        if (user == null || user.getId() == null) {
            return;
        }

        try {
            // 获取当前环境信息
            String clientIp = TraceContext.getClientIPFromMDC();
            String deviceId = TraceContext.getVisitorId();

            // 重置用户登录失败次数
            rateLimitService.resetUserLoginFailure(user.getId());

            // 重置IP登录失败次数
            rateLimitService.resetIpLoginFailure(clientIp);

            // 重置设备登录失败次数
            rateLimitService.resetDeviceLoginFailure(deviceId);

            // 如果用户被锁定，解锁账户
            if (user.getStatus() == UserStatus.LOCKED) {
                user.setStatus(UserStatus.NORMAL);
                user.setLockedUntil(null);
                usersService.updateById(user);

                // 记录账户解锁事件
                Map<String, Object> eventData = new HashMap<>();
                eventData.put("userId", user.getId());
                eventData.put("userEmail", user.getEmail());
                eventData.put("ipAddress", clientIp);
                eventData.put("deviceId", deviceId);
                eventData.put("reason", "登录成功，自动解锁");

                securityEventsService.recordSecurityEvent(
                        SecurityEventType.ACCOUNT_UNLOCKED,
                        "账户解锁",
                        String.format("用户[%s]登录成功，账户已解锁", user.getEmail()),
                        user.getId(),
                        deviceId,
                        null,
                        clientIp,
                        null,
                        eventData
                );
            }

            log.info("重置登录失败次数: userId={}", user.getId());

        } catch (Exception e) {
            log.error("重置登录失败次数异常: userId={}, error={}", user.getId(), e.getMessage(), e);
        }
    }

    private void lockAccount(Users user) {
        user.setLockedUntil(LocalDateTime.now().plusMinutes(15));
        usersService.updateById(user);
    }

    private boolean isUnusualLoginTime(Date loginTime) {
        // 简化实现：判断是否在深夜时间（23:00-06:00）
        Calendar cal = Calendar.getInstance();
        cal.setTime(loginTime);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        return hour >= 23 || hour <= 6;
    }

    private boolean hasLocationChanged(Users user, LoginContext context) {
        // 简化实现：检查IP前3段是否相同
        if (user.getLastLoginIp() == null || context.getIpAddress() == null) {
            return false;
        }

        String[] lastIpParts = user.getLastLoginIp().split("\\.");
        String[] currentIpParts = context.getIpAddress().split("\\.");

        if (lastIpParts.length >= 3 && currentIpParts.length >= 3) {
            return !(lastIpParts[0].equals(currentIpParts[0]) &&
                    lastIpParts[1].equals(currentIpParts[1]) &&
                    lastIpParts[2].equals(currentIpParts[2]));
        }

        return !user.getLastLoginIp().equals(context.getIpAddress());
    }

    /**
     * 修改密码
     */
    public ChangePasswordResponse changePassword(
            ChangePasswordRequest request,Users user) {

        // 2. 验证当前密码
        String currentPasswordHash = DigestUtil.md5Hex(request.getCurrentPassword());
        if (!currentPasswordHash.equals(user.getPasswordHash())) {
            throw new ValidationException("当前密码错误");
        }
        
        // 3. 检查新密码是否与当前密码相同
        String newPasswordHash = DigestUtil.md5Hex(request.getNewPassword());
        if (newPasswordHash.equals(user.getPasswordHash())) {
            throw new ValidationException("新密码不能与当前密码相同");
        }
        
        // 4. 更新密码
        user.setPasswordHash(newPasswordHash);
        user.setUpdatedAt(LocalDateTime.now());
        boolean updated = usersService.updateById(user);
        
        if (!updated) {
            throw new AuthenticationException("密码修改失败，请稍后重试");
        }
        
        // 5. 记录安全事件
        recordPasswordChangeEvent(user);
        
        // 6. 强制下线所有会话（安全考虑）
        forceLogoutAllSessions(user.getId());
        
        log.info("用户修改密码成功: userId={}, email={}", user.getId(), user.getEmail());
        
        return ChangePasswordResponse.builder()
                .message("密码修改成功,请重新登录")
                .email(user.getEmail())
                .changedAt(LocalDateTime.now())
                .requireRelogin(true)
                .build();
    }
    
    /**
     * 记录密码修改事件
     */
    private void recordPasswordChangeEvent(Users user) {
        try {
            String clientIp = TraceContext.getClientIPFromMDC();
            String deviceId = TraceContext.getVisitorId();
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("userId", user.getId());
            eventData.put("userEmail", user.getEmail());
            eventData.put("ipAddress", clientIp);
            eventData.put("deviceId", deviceId);
            eventData.put("changeTime", LocalDateTime.now());
            
            securityEventsService.recordSecurityEvent(
                    SecurityEventType.PASSWORD_CHANGED,
                    "密码修改",
                    String.format("用户[%s]修改了登录密码", user.getEmail()),
                    user.getId(),
                    deviceId,
                    null,
                    clientIp,
                    null,
                    eventData
            );
            
        } catch (Exception e) {
            log.error("记录密码修改事件失败", e);
        }
    }
    
    /**
     * 强制下线所有会话
     */
    private void forceLogoutAllSessions(Long userId) {
        try {
            List<String> tokenList = StpUtil.getTokenValueListByLoginId(userId);
            for (String token : tokenList) {
                StpUtil.kickoutByTokenValue(token);
            }
            log.info("已强制下线用户所有会话: userId={}, sessionCount={}", userId, tokenList.size());
        } catch (Exception e) {
            log.error("强制下线会话失败: userId={}", userId, e);
        }
    }
} 