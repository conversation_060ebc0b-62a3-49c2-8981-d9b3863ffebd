package com.jiashu.labiai.service;

/**
 * 用户设备管理服务接口
 * 
 * <AUTHOR>
 */
public interface UserDeviceService {
    
    /**
     * 记录用户设备信息
     * 
     * @param userId 用户ID
     * @param visitorId 设备指纹ID
     * @param deviceHash 设备哈希
     * @param clientIP 客户端IP
     * @param userAgent User-Agent
     */
    void recordUserDevice(Long userId, String visitorId, String deviceHash, 
                         String clientIP, String userAgent);
    
    /**
     * 检查设备是否受信任
     * 
     * @param userId 用户ID
     * @param visitorId 设备指纹ID
     * @return true-受信任，false-不受信任
     */
    boolean isDeviceTrusted(Long userId, String visitorId);
    
    /**
     * 设置设备为受信任状态
     * 
     * @param userId 用户ID
     * @param visitorId 设备指纹ID
     */
    void trustDevice(Long userId, String visitorId);
    
    /**
     * 更新设备最后活跃时间
     * 
     * @param userId 用户ID
     * @param visitorId 设备指纹ID
     * @param clientIP 客户端IP
     */
    void updateDeviceActivity(Long userId, String visitorId, String clientIP);
} 