package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.PackageDTO;
import com.jiashu.labiai.dto.PackagePriceDTO;
import com.jiashu.labiai.entity.Package;
import com.jiashu.labiai.entity.PackagePrice;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.PackageException;
import com.jiashu.labiai.mapper.PackageMapper;
import com.jiashu.labiai.mapper.PackagePriceMapper;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 套餐服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PackageService extends ServiceImpl<PackageMapper, Package> implements IPackageService {

    private final PackagePriceMapper packagePriceMapper;
    


    @Override
    public ApiResponse<List<PackageDTO>> getAvailablePackages() {
        try {
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("action", "list_packages");
            TraceUtils.recordBusinessEvent("PACKAGE_LIST_REQUEST", requestDetails);

            // 获取启用的套餐
            List<Package> packages = list(
                new QueryWrapper<Package>()
                    .eq("status", 1)
                    .orderByAsc("sort_order")
            );

            List<PackageDTO> result = new ArrayList<>();
            
            for (Package pkg : packages) {
                // 获取套餐的价格
                List<PackagePrice> prices = packagePriceMapper.selectList(
                    new QueryWrapper<PackagePrice>()
                        .eq("package_id", pkg.getId())
                        .eq("status", 1)
                        .orderByAsc("billing_cycle")
                );

                PackageDTO packageDTO = buildPackageDTO(pkg, prices);
                result.add(packageDTO);
            }

            return ApiResponse.success(result, "获取套餐列表成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("PACKAGE_LIST_FAILED", errorDetails);
            log.error("获取套餐列表失败", e);
            return ApiResponse.<List<PackageDTO>>error(ResponseCode.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ApiResponse<PackageDTO> getPackageDetail(Long packageId) {
        try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("packageId", packageId);
                TraceUtils.recordBusinessEvent("PACKAGE_DETAIL_REQUEST", requestDetails);

                Package pkg = getById(packageId);
                if (pkg == null || pkg.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("packageId", packageId);
                    TraceUtils.recordError("PACKAGE_NOT_FOUND", errorDetails);
                    throw PackageException.packageNotFound(packageId);
                }

                // 获取套餐的价格
                List<PackagePrice> prices = packagePriceMapper.selectList(
                    new QueryWrapper<PackagePrice>()
                        .eq("package_id", pkg.getId())
                        .eq("status", 1)
                        .orderByAsc("billing_cycle")
                );

                PackageDTO result = buildPackageDTO(pkg, prices);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("packageId", packageId);
                successDetails.put("priceCount", prices.size());
                TraceUtils.recordBusinessEvent("PACKAGE_DETAIL_SUCCESS", successDetails);

                return ApiResponse.success(result, "获取套餐详情成功");

            } catch (PackageException e) {
                throw e; // 重新抛出业务异常
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("packageId", packageId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PACKAGE_DETAIL_FAILED", errorDetails);
                log.error("获取套餐详情失败", e);
                throw new RuntimeException("获取套餐详情失败", e);
            }
    }

    @Override
    public ApiResponse<List<PackagePriceDTO>> getPackagePrices(Long packageId) {
        try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("packageId", packageId);
                TraceUtils.recordBusinessEvent("PACKAGE_PRICES_REQUEST", requestDetails);

                // 验证套餐存在
                Package pkg = getById(packageId);
                if (pkg == null || pkg.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("packageId", packageId);
                    TraceUtils.recordError("PACKAGE_NOT_FOUND", errorDetails);
                    throw PackageException.packageNotFound(packageId);
                }

                List<PackagePrice> prices = packagePriceMapper.selectList(
                    new QueryWrapper<PackagePrice>()
                        .eq("package_id", packageId)
                        .eq("status", 1)
                        .orderByAsc("billing_cycle")
                );

                List<PackagePriceDTO> result = new ArrayList<>();
                for (PackagePrice price : prices) {
                    result.add(buildPackagePriceDTO(price));
                }

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("packageId", packageId);
                successDetails.put("priceCount", result.size());
                TraceUtils.recordBusinessEvent("PACKAGE_PRICES_SUCCESS", successDetails);

                return ApiResponse.success(result, "获取价格选项成功");

            } catch (PackageException e) {
                throw e; // 重新抛出业务异常  
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("packageId", packageId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PACKAGE_PRICES_FAILED", errorDetails);
                log.error("获取套餐价格失败", e);
                throw new RuntimeException("获取套餐价格失败", e);
            }
    }

    @Override
    public ApiResponse<PackagePriceDTO> getPackagePriceDetail(Long priceId) {
        try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("priceId", priceId);
                TraceUtils.recordBusinessEvent("PACKAGE_PRICE_DETAIL_REQUEST", requestDetails);

                PackagePrice price = packagePriceMapper.selectById(priceId);
                if (price == null || price.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("priceId", priceId);
                    TraceUtils.recordError("PACKAGE_PRICE_NOT_FOUND", errorDetails);
                    throw PackageException.priceNotFound(priceId);
                }

                PackagePriceDTO result = buildPackagePriceDTO(price);

                // 添加套餐信息
                Package pkg = getById(price.getPackageId());
                if (pkg != null) {
                    result.setPackageName(pkg.getDisplayName())
                          .setPackageDescription(pkg.getDescription())
                          .setPackageFeatures(pkg.getFeatures());
                }

                return ApiResponse.success(result, "获取价格详情成功");

            } catch (PackageException e) {
                throw e; // 重新抛出业务异常
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("priceId", priceId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PACKAGE_PRICE_DETAIL_FAILED", errorDetails);
                log.error("获取价格详情失败", e);
                throw new RuntimeException("获取价格详情失败", e);
            }
    }



    @Override
    public Package findPackageById(Long packageId) {
        return getById(packageId);
    }

    @Override
    public PackagePrice findPackagePriceById(Long priceId) {
        return packagePriceMapper.selectById(priceId);
    }



    // ================== 私有方法 ==================

    private PackageDTO buildPackageDTO(Package pkg, List<PackagePrice> prices) {
        PackageDTO packageDTO = new PackageDTO()
            .setId(pkg.getId())
            .setName(pkg.getName())
            .setDisplayName(pkg.getDisplayName())
            .setDescription(pkg.getDescription())
            .setFeatures(pkg.getFeatures())
            .setSortOrder(pkg.getSortOrder());

        // 处理价格信息
        List<PackagePriceDTO> priceList = new ArrayList<>();
        for (PackagePrice price : prices) {
            priceList.add(buildPackagePriceDTO(price));
        }
        
        packageDTO.setPrices(priceList);
        return packageDTO;
    }

    private PackagePriceDTO buildPackagePriceDTO(PackagePrice price) {
        PackagePriceDTO priceDTO = new PackagePriceDTO()
            .setId(price.getId())
            .setBillingCycle(price.getBillingCycle())
            .setCycleCount(price.getCycleCount())
            .setOriginalPrice(price.getOriginalPrice())
            .setSalePrice(price.getSalePrice())
            .setCurrency(price.getCurrency());
        
        // 计算折扣百分比
        if (price.getOriginalPrice().compareTo(price.getSalePrice()) > 0) {
            double discount = (1 - price.getSalePrice().doubleValue() / price.getOriginalPrice().doubleValue()) * 100;
            priceDTO.setDiscountPercent((int) Math.round(discount));
        } else {
            priceDTO.setDiscountPercent(0);
        }

        // 生成显示文本
        String displayText = getBillingDisplayText(price.getBillingCycle(), price.getCycleCount());
        priceDTO.setDisplayText(displayText);
        
        return priceDTO;
    }



    private String getBillingDisplayText(String billingCycle, Integer cycleCount) {
        String unit;
        switch (billingCycle) {
            case "DAY":
                unit = "天";
                break;
            case "MONTH":
                unit = "个月";
                break;
            case "QUARTER":
                unit = "季度";
                break;
            case "YEAR":
                unit = "年";
                break;
            default:
                unit = billingCycle;
        }
        return cycleCount + unit;
    }


} 