package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.entity.Package;
import com.jiashu.labiai.entity.PackagePrice;
import com.jiashu.labiai.entity.UserSubscription;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.PackageException;
import com.jiashu.labiai.mapper.PackageMapper;
import com.jiashu.labiai.mapper.PackagePriceMapper;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 套餐服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PackageService extends ServiceImpl<PackageMapper, Package> implements IPackageService {

    private final PackagePriceMapper packagePriceMapper;
    private final IUserSubscriptionService userSubscriptionService;
    


    @Override
    public ApiResponse<List<PackageDTO>> getAvailablePackages() {
        try {
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("action", "list_packages");
            TraceUtils.recordBusinessEvent("PACKAGE_LIST_REQUEST", requestDetails);

            // 获取启用的套餐
            List<Package> packages = list(
                new QueryWrapper<Package>()
                    .eq("status", 1)
                    .orderByAsc("sort_order")
            );

            List<PackageDTO> result = new ArrayList<>();
            
            for (Package pkg : packages) {
                // 获取套餐的价格
                List<PackagePrice> prices = packagePriceMapper.selectList(
                    new QueryWrapper<PackagePrice>()
                        .eq("package_id", pkg.getId())
                        .eq("status", 1)
                        .orderByAsc("billing_cycle")
                );

                PackageDTO packageDTO = buildPackageDTO(pkg, prices);
                result.add(packageDTO);
            }

            return ApiResponse.success(result, "获取套餐列表成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("PACKAGE_LIST_FAILED", errorDetails);
            log.error("获取套餐列表失败", e);
            return ApiResponse.<List<PackageDTO>>error(ResponseCode.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ApiResponse<PackageDTO> getPackageDetail(Long packageId) {
        try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("packageId", packageId);
                TraceUtils.recordBusinessEvent("PACKAGE_DETAIL_REQUEST", requestDetails);

                Package pkg = getById(packageId);
                if (pkg == null || pkg.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("packageId", packageId);
                    TraceUtils.recordError("PACKAGE_NOT_FOUND", errorDetails);
                    throw PackageException.packageNotFound(packageId);
                }

                // 获取套餐的价格
                List<PackagePrice> prices = packagePriceMapper.selectList(
                    new QueryWrapper<PackagePrice>()
                        .eq("package_id", pkg.getId())
                        .eq("status", 1)
                        .orderByAsc("billing_cycle")
                );

                PackageDTO result = buildPackageDTO(pkg, prices);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("packageId", packageId);
                successDetails.put("priceCount", prices.size());
                TraceUtils.recordBusinessEvent("PACKAGE_DETAIL_SUCCESS", successDetails);

                return ApiResponse.success(result, "获取套餐详情成功");

            } catch (PackageException e) {
                throw e; // 重新抛出业务异常
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("packageId", packageId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PACKAGE_DETAIL_FAILED", errorDetails);
                log.error("获取套餐详情失败", e);
                throw new RuntimeException("获取套餐详情失败", e);
            }
    }

    @Override
    public ApiResponse<List<PackagePriceDTO>> getPackagePrices(Long packageId) {
        try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("packageId", packageId);
                TraceUtils.recordBusinessEvent("PACKAGE_PRICES_REQUEST", requestDetails);

                // 验证套餐存在
                Package pkg = getById(packageId);
                if (pkg == null || pkg.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("packageId", packageId);
                    TraceUtils.recordError("PACKAGE_NOT_FOUND", errorDetails);
                    throw PackageException.packageNotFound(packageId);
                }

                List<PackagePrice> prices = packagePriceMapper.selectList(
                    new QueryWrapper<PackagePrice>()
                        .eq("package_id", packageId)
                        .eq("status", 1)
                        .orderByAsc("billing_cycle")
                );

                List<PackagePriceDTO> result = new ArrayList<>();
                for (PackagePrice price : prices) {
                    result.add(buildPackagePriceDTO(price));
                }

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("packageId", packageId);
                successDetails.put("priceCount", result.size());
                TraceUtils.recordBusinessEvent("PACKAGE_PRICES_SUCCESS", successDetails);

                return ApiResponse.success(result, "获取价格选项成功");

            } catch (PackageException e) {
                throw e; // 重新抛出业务异常  
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("packageId", packageId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PACKAGE_PRICES_FAILED", errorDetails);
                log.error("获取套餐价格失败", e);
                throw new RuntimeException("获取套餐价格失败", e);
            }
    }

    @Override
    public ApiResponse<PackagePriceDTO> getPackagePriceDetail(Long priceId) {
        try {
                Map<String, Object> requestDetails = new HashMap<>();
                requestDetails.put("priceId", priceId);
                TraceUtils.recordBusinessEvent("PACKAGE_PRICE_DETAIL_REQUEST", requestDetails);

                PackagePrice price = packagePriceMapper.selectById(priceId);
                if (price == null || price.getStatus() != 1) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("priceId", priceId);
                    TraceUtils.recordError("PACKAGE_PRICE_NOT_FOUND", errorDetails);
                    throw PackageException.priceNotFound(priceId);
                }

                PackagePriceDTO result = buildPackagePriceDTO(price);

                // 添加套餐信息
                Package pkg = getById(price.getPackageId());
                if (pkg != null) {
                    result.setPackageName(pkg.getDisplayName())
                          .setPackageDescription(pkg.getDescription())
                          .setPackageFeatures(pkg.getFeatures());
                }

                return ApiResponse.success(result, "获取价格详情成功");

            } catch (PackageException e) {
                throw e; // 重新抛出业务异常
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("priceId", priceId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PACKAGE_PRICE_DETAIL_FAILED", errorDetails);
                log.error("获取价格详情失败", e);
                throw new RuntimeException("获取价格详情失败", e);
            }
    }



    @Override
    public Package findPackageById(Long packageId) {
        return getById(packageId);
    }

    @Override
    public PackagePrice findPackagePriceById(Long priceId) {
        return packagePriceMapper.selectById(priceId);
    }

    @Override
    public ApiResponse<RenewalOptionsDTO> getRenewalOptions(Long userId, Long packageId) {
        try {
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("userId", userId);
            requestDetails.put("packageId", packageId);
            TraceUtils.recordBusinessEvent("RENEWAL_OPTIONS_REQUEST", requestDetails);

            // 1. 获取用户当前订阅
            UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
            if (currentSubscription == null) {
                return ApiResponse.error(40001, "用户没有活跃订阅");
            }

            // 2. 验证套餐ID是否匹配当前订阅
            if (!currentSubscription.getPackageId().equals(packageId)) {
                return ApiResponse.error(40002, "套餐ID与当前订阅不匹配");
            }

            // 3. 获取套餐信息
            Package pkg = getById(packageId);
            if (pkg == null || pkg.getStatus() != 1) {
                return ApiResponse.error(40003, "套餐不存在或已禁用");
            }

            // 4. 构建当前订阅信息
            RenewalOptionsDTO.CurrentSubscriptionInfo currentInfo = buildCurrentSubscriptionInfo(currentSubscription, pkg);

            // 5. 获取续费选项（该套餐的所有价格选项）
            List<PackagePrice> prices = packagePriceMapper.selectList(
                new QueryWrapper<PackagePrice>()
                    .eq("package_id", packageId)
                    .eq("status", 1)
                    .orderByAsc("billing_cycle")
            );

            List<RenewalOptionsDTO.RenewalOption> renewalOptions = new ArrayList<>();
            for (PackagePrice price : prices) {
                RenewalOptionsDTO.RenewalOption option = buildRenewalOption(price, currentSubscription);
                renewalOptions.add(option);
            }

            RenewalOptionsDTO result = RenewalOptionsDTO.builder()
                .currentSubscription(currentInfo)
                .renewalOptions(renewalOptions)
                .build();

            return ApiResponse.success(result, "获取续费选项成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("packageId", packageId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("RENEWAL_OPTIONS_FAILED", errorDetails);
            log.error("获取续费选项失败", e);
            return ApiResponse.error(50000, "获取续费选项失败");
        }
    }

    @Override
    public ApiResponse<UpgradeOptionsDTO> getUpgradeOptions(Long userId) {
        try {
            Map<String, Object> requestDetails = new HashMap<>();
            requestDetails.put("userId", userId);
            TraceUtils.recordBusinessEvent("UPGRADE_OPTIONS_REQUEST", requestDetails);

            // 1. 获取用户当前订阅
            UserSubscription currentSubscription = userSubscriptionService.getCurrentSubscription(userId);
            if (currentSubscription == null) {
                return ApiResponse.error(40001, "用户没有活跃订阅");
            }

            // 2. 获取当前套餐信息
            Package currentPackage = getById(currentSubscription.getPackageId());
            if (currentPackage == null) {
                return ApiResponse.error(40002, "当前订阅套餐不存在");
            }

            // 3. 构建当前订阅信息
            UpgradeOptionsDTO.CurrentSubscriptionInfo currentInfo = buildCurrentSubscriptionInfoForUpgrade(currentSubscription, currentPackage);

            // 4. 获取可升级的套餐（sort_order更大的套餐）
            List<Package> upgradePackages = list(
                new QueryWrapper<Package>()
                    .eq("status", 1)
                    .gt("sort_order", currentPackage.getSortOrder())
                    .orderByAsc("sort_order")
            );

            List<UpgradeOptionsDTO.UpgradeOption> upgradeOptions = new ArrayList<>();
            for (Package pkg : upgradePackages) {
                UpgradeOptionsDTO.UpgradeOption option = buildUpgradeOption(pkg, currentSubscription);
                upgradeOptions.add(option);
            }

            UpgradeOptionsDTO result = UpgradeOptionsDTO.builder()
                .currentSubscription(currentInfo)
                .upgradeOptions(upgradeOptions)
                .build();

            return ApiResponse.success(result, "获取升级选项成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("userId", userId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("UPGRADE_OPTIONS_FAILED", errorDetails);
            log.error("获取升级选项失败", e);
            return ApiResponse.error(50000, "获取升级选项失败");
        }
    }



    // ================== 私有方法 ==================

    private PackageDTO buildPackageDTO(Package pkg, List<PackagePrice> prices) {
        PackageDTO packageDTO = new PackageDTO()
            .setId(pkg.getId())
            .setName(pkg.getName())
            .setDisplayName(pkg.getDisplayName())
            .setDescription(pkg.getDescription())
            .setFeatures(pkg.getFeatures())
            .setSortOrder(pkg.getSortOrder());

        // 处理价格信息
        List<PackagePriceDTO> priceList = new ArrayList<>();
        for (PackagePrice price : prices) {
            priceList.add(buildPackagePriceDTO(price));
        }
        
        packageDTO.setPrices(priceList);
        return packageDTO;
    }

    private PackagePriceDTO buildPackagePriceDTO(PackagePrice price) {
        PackagePriceDTO priceDTO = new PackagePriceDTO()
            .setId(price.getId())
            .setBillingCycle(price.getBillingCycle())
            .setCycleCount(price.getCycleCount())
            .setOriginalPrice(price.getOriginalPrice())
            .setSalePrice(price.getSalePrice())
            .setCurrency(price.getCurrency());
        
        // 计算折扣百分比
        if (price.getOriginalPrice().compareTo(price.getSalePrice()) > 0) {
            double discount = (1 - price.getSalePrice().doubleValue() / price.getOriginalPrice().doubleValue()) * 100;
            priceDTO.setDiscountPercent((int) Math.round(discount));
        } else {
            priceDTO.setDiscountPercent(0);
        }

        // 生成显示文本
        String displayText = getBillingDisplayText(price.getBillingCycle(), price.getCycleCount());
        priceDTO.setDisplayText(displayText);
        
        return priceDTO;
    }



    private String getBillingDisplayText(String billingCycle, Integer cycleCount) {
        String unit;
        switch (billingCycle) {
            case "DAY":
                unit = "天";
                break;
            case "MONTH":
                unit = "个月";
                break;
            case "QUARTER":
                unit = "季度";
                break;
            case "YEAR":
                unit = "年";
                break;
            default:
                unit = billingCycle;
        }
        return cycleCount + unit;
    }

    // ================== 续费和升级相关私有方法 ==================

    private RenewalOptionsDTO.CurrentSubscriptionInfo buildCurrentSubscriptionInfo(UserSubscription subscription, Package pkg) {
        LocalDateTime now = LocalDateTime.now();
        long daysRemaining = Duration.between(now, subscription.getEndTime()).toDays();
        boolean isExpiringSoon = daysRemaining <= 7 && daysRemaining > 0;
        boolean isInGracePeriod = subscription.getEndTime().isBefore(now) && daysRemaining >= -3;

        return RenewalOptionsDTO.CurrentSubscriptionInfo.builder()
            .subscriptionId(subscription.getId())
            .packageName(pkg.getDisplayName())
            .endTime(subscription.getEndTime())
            .status(subscription.getStatus())
            .daysRemaining(Math.max(0, daysRemaining))
            .isExpiringSoon(isExpiringSoon)
            .isInGracePeriod(isInGracePeriod)
            .build();
    }

    private RenewalOptionsDTO.RenewalOption buildRenewalOption(PackagePrice price, UserSubscription currentSubscription) {
        LocalDateTime newEndTime = calculateNewEndTime(currentSubscription.getEndTime(), price);

        return RenewalOptionsDTO.RenewalOption.builder()
            .packagePriceId(price.getId())
            .billingCycle(price.getBillingCycle())
            .cycleCount(price.getCycleCount())
            .originalPrice(price.getOriginalPrice())
            .salePrice(price.getSalePrice())
            .currency(price.getCurrency())
            .displayText("续费" + getBillingDisplayText(price.getBillingCycle(), price.getCycleCount()))
            .newEndTime(newEndTime)
            .discountPercent(calculateDiscountPercent(price.getOriginalPrice(), price.getSalePrice()))
            .build();
    }

    private UpgradeOptionsDTO.CurrentSubscriptionInfo buildCurrentSubscriptionInfoForUpgrade(UserSubscription subscription, Package pkg) {
        LocalDateTime now = LocalDateTime.now();
        long daysRemaining = Duration.between(now, subscription.getEndTime()).toDays();
        BigDecimal remainingValue = calculateRemainingValue(subscription);

        return UpgradeOptionsDTO.CurrentSubscriptionInfo.builder()
            .subscriptionId(subscription.getId())
            .packageId(subscription.getPackageId())
            .packageName(pkg.getDisplayName())
            .endTime(subscription.getEndTime())
            .remainingDays(Math.max(0, daysRemaining))
            .remainingValue(remainingValue)
            .build();
    }

    private UpgradeOptionsDTO.UpgradeOption buildUpgradeOption(Package pkg, UserSubscription currentSubscription) {
        // 计算剩余天数
        long remainingDays = Math.max(0, Duration.between(LocalDateTime.now(), currentSubscription.getEndTime()).toDays());

        // 获取套餐的月付价格作为基准
        PackagePrice monthlyPrice = packagePriceMapper.selectOne(
            new QueryWrapper<PackagePrice>()
                .eq("package_id", pkg.getId())
                .eq("status", 1)
                .eq("billing_cycle", "MONTH")
                .eq("cycle_count", 1)
                .last("LIMIT 1")
        );

        if (monthlyPrice == null) {
            // 如果没有月付价格，使用第一个可用价格
            monthlyPrice = packagePriceMapper.selectOne(
                new QueryWrapper<PackagePrice>()
                    .eq("package_id", pkg.getId())
                    .eq("status", 1)
                    .last("LIMIT 1")
            );
        }

        List<UpgradeOptionsDTO.PriceOption> priceOptions = new ArrayList<>();
        BigDecimal estimatedCost = BigDecimal.ZERO;

        if (monthlyPrice != null) {
            // 计算剩余天数的升级费用
            estimatedCost = calculateUpgradeDifferenceForRemainingTime(currentSubscription, monthlyPrice);

            // 创建按天计费的价格选项（针对剩余天数）
            UpgradeOptionsDTO.PriceOption priceOption = UpgradeOptionsDTO.PriceOption.builder()
                .packagePriceId(monthlyPrice.getId())
                .billingCycle("DAY") // 按天计费
                .originalPrice(estimatedCost) // 升级差价作为原价
                .salePrice(estimatedCost) // 升级差价作为售价
                .displayText(pkg.getDisplayName() + " 升级至到期(" + remainingDays + "天)")
                .build();
            priceOptions.add(priceOption);
        }

        return UpgradeOptionsDTO.UpgradeOption.builder()
            .packageId(pkg.getId())
            .packageName(pkg.getDisplayName())
            .description(pkg.getDescription())
            .features(pkg.getFeatures())
            .prices(priceOptions)
            .upgradeType("UPGRADE")
            .estimatedCost(estimatedCost)
            .build();
    }

    private LocalDateTime calculateNewEndTime(LocalDateTime currentEndTime, PackagePrice price) {
        LocalDateTime baseTime = currentEndTime.isAfter(LocalDateTime.now()) ?
            currentEndTime : LocalDateTime.now();

        switch (price.getBillingCycle()) {
            case "DAY":
                return baseTime.plusDays(price.getCycleCount());
            case "MONTH":
                return baseTime.plusMonths(price.getCycleCount());
            case "QUARTER":
                return baseTime.plusMonths(price.getCycleCount() * 3);
            case "YEAR":
                return baseTime.plusYears(price.getCycleCount());
            default:
                return baseTime.plusMonths(price.getCycleCount());
        }
    }

    private Integer calculateDiscountPercent(BigDecimal originalPrice, BigDecimal salePrice) {
        if (originalPrice.compareTo(salePrice) > 0) {
            double discount = (1 - salePrice.doubleValue() / originalPrice.doubleValue()) * 100;
            return (int) Math.round(discount);
        }
        return 0;
    }

    private BigDecimal calculateRemainingValue(UserSubscription subscription) {
        // 获取当前套餐的月均价格
        List<PackagePrice> prices = packagePriceMapper.selectList(
            new QueryWrapper<PackagePrice>()
                .eq("package_id", subscription.getPackageId())
                .eq("status", 1)
                .eq("billing_cycle", "MONTH")
                .eq("cycle_count", 1)
                .last("LIMIT 1")
        );

        if (prices.isEmpty()) {
            return BigDecimal.ZERO;
        }

        PackagePrice monthlyPrice = prices.get(0);
        long remainingDays = Duration.between(LocalDateTime.now(), subscription.getEndTime()).toDays();

        if (remainingDays <= 0) {
            return BigDecimal.ZERO;
        }

        return monthlyPrice.getSalePrice()
            .multiply(BigDecimal.valueOf(remainingDays))
            .divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateUpgradeDifferenceForRemainingTime(UserSubscription currentSubscription, PackagePrice targetPrice) {
        // 计算当前订阅剩余价值
        BigDecimal currentRemainingValue = calculateRemainingValue(currentSubscription);

        // 计算剩余天数
        long remainingDays = Duration.between(LocalDateTime.now(), currentSubscription.getEndTime()).toDays();

        if (remainingDays <= 0) {
            // 如果已过期，升级费用为目标套餐的月付价格
            return targetPrice.getSalePrice();
        }

        // 计算目标套餐在剩余时间内的价值
        BigDecimal targetMonthlyPrice = targetPrice.getSalePrice(); // 假设传入的是月付价格
        BigDecimal targetRemainingValue = targetMonthlyPrice
            .multiply(BigDecimal.valueOf(remainingDays))
            .divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);

        // 升级差价 = 目标套餐剩余时间价值 - 当前套餐剩余价值
        BigDecimal upgradeDifference = targetRemainingValue.subtract(currentRemainingValue);

        // 确保差价不为负数
        return upgradeDifference.max(BigDecimal.ZERO);
    }

}