package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiashu.labiai.dto.request.DeviceManageRequest;
import com.jiashu.labiai.dto.response.DeviceInfoResponse;
import com.jiashu.labiai.dto.response.DeviceRiskResponse;
import com.jiashu.labiai.entity.UserDevices;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
public interface IUserDevicesService extends IService<UserDevices> {
    
    /**
     * 注册或更新设备信息
     * @param userId 用户ID
     * @param deviceId 设备ID(visitorId)
     * @param deviceHash 设备指纹哈希
     * @param request HTTP请求
     * @return 设备记录
     */
    UserDevices registerOrUpdateDevice(Long userId, String deviceId, String deviceHash, HttpServletRequest request);
    
    /**
     * 验证设备安全性
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param deviceHash 设备指纹哈希
     * @param clientIP 客户端IP
     * @return 是否通过验证
     */
    boolean validateDeviceSecurity(Long userId, String deviceId, String deviceHash, String clientIP);
    
    /**
     * 获取用户所有设备
     * @param userId 用户ID
     * @return 设备列表
     */
    List<DeviceInfoResponse> getUserDevices(Long userId);
    
    /**
     * 获取设备详细信息
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 设备信息
     */
    DeviceInfoResponse getDeviceInfo(Long userId, String deviceId);
    
    /**
     * 设备风险评估
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param clientIP 客户端IP
     * @return 风险评估结果
     */
    DeviceRiskResponse assessDeviceRisk(Long userId, String deviceId, String clientIP);
    
    /**
     * 更新设备信任状态
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param trusted 是否信任
     * @param reason 信任原因
     * @return 是否更新成功
     */
    boolean updateDeviceTrust(Long userId, String deviceId, boolean trusted, String reason);
    
    /**
     * 删除设备
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 是否删除成功
     */
    boolean removeDevice(Long userId, String deviceId);
    
    /**
     * 管理设备状态
     * @param userId 用户ID
     * @param request 管理请求
     * @return 是否操作成功
     */
    boolean manageDevice(Long userId, DeviceManageRequest request);
    
    /**
     * 计算设备信任度
     * @param device 设备记录
     * @param clientIP 当前IP
     * @return 信任度评分 (0-100)
     */
    int calculateTrustLevel(UserDevices device, String clientIP);
    
    /**
     * 更新设备活动信息
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param clientIP 客户端IP
     * @param location 地理位置
     */
    void updateDeviceActivity(Long userId, String deviceId, String clientIP, String location);
    
    /**
     * 检查设备是否存在异常
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param deviceHash 设备指纹
     * @param clientIP 客户端IP
     * @return 异常描述，null表示正常
     */
    String checkDeviceAnomaly(Long userId, String deviceId, String deviceHash, String clientIP);

    /**
     * 检查设备是否被拉黑
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 是否被拉黑
     */
    boolean isDeviceBlacklisted(Long userId, String deviceId);

    void recordUserDevice(Long userId, String visitorId, String deviceHash, String clientIP, String userAgent);
}
