package com.jiashu.labiai.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.PaymentCreateResponseDTO;
import com.jiashu.labiai.dto.PaymentMethodDTO;
import com.jiashu.labiai.dto.PaymentStatusDTO;
import com.jiashu.labiai.entity.*;
import com.jiashu.labiai.entity.Package;
import com.jiashu.labiai.enums.OrderStatusEnum;
import com.jiashu.labiai.enums.PaymentStatusEnum;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.OrderException;
import com.jiashu.labiai.exception.PaymentCallbackException;
import com.jiashu.labiai.exception.PaymentException;
import com.jiashu.labiai.exception.PaymentMethodException;
import com.jiashu.labiai.mapper.*;
import com.jiashu.labiai.util.TraceUtils;
import com.jiashu.labiai.util.XunhupayHttpUtils;
import com.jiashu.labiai.util.XunhupayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 支付服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentService extends ServiceImpl<PaymentMapper, Payment> implements IPaymentService {

    private final OrderMapper orderMapper;
    private final PaymentMethodMapper paymentMethodMapper;
    private final PackageMapper packageMapper;
    private final PackagePriceMapper packagePriceMapper;
    private final IUserSubscriptionService userSubscriptionService;

    @Value("${payment.callback-base-url}")
    private String callbackBaseUrl;


    @Override
    @Transactional
    public ApiResponse<PaymentCreateResponseDTO> createPayment(Long orderId, String paymentMethodCode,
                                                               String clientIp, String userAgent) {
        return TraceUtils.executeWithTrace("createPayment", () -> {
            try {
                Map<String, Object> startDetails = new HashMap<>();
                startDetails.put("orderId", orderId);
                startDetails.put("paymentMethodCode", paymentMethodCode);
                startDetails.put("clientIp", clientIp);
                TraceUtils.recordBusinessEvent("PAYMENT_CREATE_START", startDetails);

                // 1. 验证订单
                Order order = orderMapper.selectById(orderId);
                if (order == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", orderId);
                    TraceUtils.recordError("ORDER_NOT_FOUND", errorDetails);
                    throw OrderException.orderNotFound(orderId);
                }

                if (!order.getStatus().equals(OrderStatusEnum.PENDING.getCode())) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", orderId);
                    errorDetails.put("currentStatus", order.getStatus());
                    TraceUtils.recordError("ORDER_STATUS_INVALID", errorDetails);
                    throw OrderException.orderStatusInvalid(order.getOrderNo(), order.getStatus());
                }

                // 检查订单是否过期
                if (order.getExpiredAt() != null && order.getExpiredAt().isBefore(LocalDateTime.now())) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderNo", order.getOrderNo());
                    TraceUtils.recordError("ORDER_EXPIRED", errorDetails);
                    throw OrderException.orderExpired(order.getOrderNo());
                }

                // 2. 获取支付方式
                PaymentMethod paymentMethod = paymentMethodMapper.selectOne(
                        new QueryWrapper<PaymentMethod>()
                                .eq("method_code", paymentMethodCode)
                                .eq("status", 1)
                );

                if (paymentMethod == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("methodCode", paymentMethodCode);
                    TraceUtils.recordError("PAYMENT_METHOD_NOT_FOUND", errorDetails);
                    throw PaymentMethodException.methodNotFound(paymentMethodCode);
                }

                // 3. 验证支付金额
                if (!validatePaymentAmount(paymentMethod, order.getFinalAmount())) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("amount", order.getFinalAmount());
                    errorDetails.put("methodCode", paymentMethodCode);
                    TraceUtils.recordError("PAYMENT_AMOUNT_INVALID", errorDetails);

                    Map<String, Object> limitDetails = new HashMap<>();
                    limitDetails.put("amount", order.getFinalAmount());
                    limitDetails.put("minAmount", paymentMethod.getMinAmount());
                    limitDetails.put("maxAmount", paymentMethod.getMaxAmount());
                    throw PaymentMethodException.amountExceedsLimit(paymentMethodCode, limitDetails);
                }

                // 4. 查找是否有可复用的支付记录
                Payment payment = findReusablePayment(order, paymentMethod);
                boolean isReused = payment != null;
                
                if (payment == null) {
                    // 创建新的支付记录
                    payment = createPaymentRecord(order, paymentMethod, clientIp, userAgent);
                    Map<String, Object> createDetails = new HashMap<>();
                    createDetails.put("paymentNo", payment.getPaymentNo());
                    createDetails.put("tradeOrderId", payment.getTradeOrderId());
                    TraceUtils.recordBusinessEvent("PAYMENT_RECORD_CREATED", createDetails);
                } else {
                    // 复用现有支付记录，更新客户端信息和过期时间
                    payment.setClientIp(clientIp);
                    payment.setUserAgent(userAgent);
                    payment.setExpiredAt(LocalDateTime.now().plusMinutes(5)); // 重新设置过期时间
                    updateById(payment);
                    
                    Map<String, Object> reuseDetails = new HashMap<>();
                    reuseDetails.put("paymentNo", payment.getPaymentNo());
                    reuseDetails.put("tradeOrderId", payment.getTradeOrderId());
                    reuseDetails.put("originalCreatedAt", payment.getCreatedAt());
                    TraceUtils.recordBusinessEvent("PAYMENT_RECORD_REUSED", reuseDetails);
                }

                // 5. 调用支付平台（无论是新建还是复用都需要重新调用）
                Map<String, Object> platformResult = callPaymentPlatform(payment, paymentMethod, order);

                // 6. 更新支付记录
                updatePaymentWithPlatformResult(payment, platformResult);

                // 7. 构建返回结果
                PaymentCreateResponseDTO result = buildPaymentCreateResponse(payment, order);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("paymentNo", payment.getPaymentNo());
                successDetails.put("amount", payment.getAmount());
                successDetails.put("isReused", isReused);
                TraceUtils.recordBusinessEvent("PAYMENT_CREATE_SUCCESS", successDetails);

                return ApiResponse.success(result, "支付订单创建成功");

            } catch (PaymentException e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("errorType", e.getClass().getSimpleName());
                errorDetails.put("message", e.getMessage());
                TraceUtils.recordError("PAYMENT_ERROR", errorDetails);
                throw e;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("message", e.getMessage());
                TraceUtils.recordError("PAYMENT_SYSTEM_ERROR", errorDetails);
                throw new PaymentException("支付系统异常", e);
            }
        });
    }

    @Override
    @Transactional
    public ApiResponse<String> handlePaymentCallback(String provider, String callbackData) {
        return TraceUtils.executeWithTrace("handlePaymentCallback", () -> {
            try {
                Map<String, Object> startDetails = new HashMap<>();
                startDetails.put("provider", provider);
                startDetails.put("dataLength", callbackData.length());
                TraceUtils.recordBusinessEvent("PAYMENT_CALLBACK_START", startDetails);

                if (!"xunhupay".equals(provider)) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("provider", provider);
                    TraceUtils.recordError("UNSUPPORTED_PROVIDER", errorDetails);
                    return ApiResponse.error(ResponseCode.BAD_REQUEST, "不支持的支付提供商");
                }

                // 解析回调参数
                Map<String, String> params = parseCallbackParams(callbackData);
                String tradeOrderId = params.get("trade_order_id");

                if (tradeOrderId == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("callbackData", callbackData);
                    TraceUtils.recordError("MISSING_TRADE_ORDER_ID", errorDetails);
                    return ApiResponse.error(ResponseCode.BAD_REQUEST, "缺少商户订单号");
                }

                // 查找支付记录
                Payment payment = findByTradeOrderId(tradeOrderId);
                if (payment == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("tradeOrderId", tradeOrderId);
                    TraceUtils.recordError("PAYMENT_NOT_FOUND", errorDetails);
                    return ApiResponse.error(ResponseCode.NOT_FOUND, "支付记录不存在");
                }

                // 检查是否已处理
                if (PaymentStatusEnum.SUCCESS.getCode().equals(payment.getStatus())) {
                    Map<String, Object> alreadyDetails = new HashMap<>();
                    alreadyDetails.put("paymentNo", payment.getPaymentNo());
                    TraceUtils.recordBusinessEvent("PAYMENT_ALREADY_SUCCESS", alreadyDetails);
                    return ApiResponse.success("success", "支付已成功处理");
                }

                // 验证回调签名
                if (!verifyCallbackSignature(payment, params)) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("tradeOrderId", tradeOrderId);
                    TraceUtils.recordError("CALLBACK_VERIFY_FAILED", errorDetails);
                    return ApiResponse.error(ResponseCode.PAYMENT_CALLBACK_VERIFY_FAILED);
                }

                // 处理支付成功
                processPaymentSuccess(payment, params);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("paymentNo", payment.getPaymentNo());
                successDetails.put("tradeOrderId", tradeOrderId);
                TraceUtils.recordBusinessEvent("PAYMENT_CALLBACK_SUCCESS", successDetails);

                return ApiResponse.success("success", "回调处理成功");

            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("provider", provider);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("PAYMENT_CALLBACK_FAILED", errorDetails);
                log.error("处理支付回调失败", e);
                return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR, "回调处理失败");
            }
        });
    }

    @Override
    public ApiResponse<PaymentStatusDTO> queryPaymentStatus(String paymentNo) {
        return TraceUtils.executeWithTrace("queryPaymentStatus", () -> {
            try {
                if (StrUtil.isBlank(paymentNo)) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("paymentNo", paymentNo);
                    TraceUtils.recordError("MISSING_PAYMENT_NO", errorDetails);
                    throw new IllegalArgumentException("支付单号不能为空");
                }

                Payment payment = getOne(
                        new QueryWrapper<Payment>().eq("payment_no", paymentNo)
                );

                if (payment == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("paymentNo", paymentNo);
                    TraceUtils.recordError("PAYMENT_NOT_FOUND", errorDetails);
                    throw PaymentCallbackException.paymentNotFound(paymentNo);
                }

                PaymentStatusDTO result = new PaymentStatusDTO()
                        .setPaymentNo(payment.getPaymentNo())
                        .setStatus(payment.getStatus())
                        .setAmount(payment.getAmount())
                        .setPaidAt(payment.getPaidAt())
                        .setCreatedAt(payment.getCreatedAt());

                return ApiResponse.success(result);

            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("paymentNo", paymentNo);
                TraceUtils.recordError("QUERY_PAYMENT_STATUS_FAILED", errorDetails);
                log.error("查询支付状态失败", e);
                return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR);
            }
        });
    }

    @Override
    public ApiResponse<List<PaymentMethodDTO>> getAvailablePaymentMethods(BigDecimal amount) {
        return TraceUtils.executeWithTrace("getAvailablePaymentMethods", () -> {
            try {
                List<PaymentMethod> methods = paymentMethodMapper.selectList(
                        new QueryWrapper<PaymentMethod>()
                                .eq("status", 1)
                                .orderByDesc("priority")
                                .orderByAsc("sort_order")
                );

                List<PaymentMethodDTO> result = new ArrayList<>();
                for (PaymentMethod method : methods) {
                    if (validatePaymentAmount(method, amount)) {
                        PaymentMethodDTO methodDTO = new PaymentMethodDTO()
                                .setMethodCode(method.getMethodCode())
                                .setMethodName(method.getMethodName())
                                .setIconUrl(method.getIconUrl())
                                .setDescription(method.getDescription())
                                .setMinAmount(method.getMinAmount())
                                .setMaxAmount(method.getMaxAmount())
                                .setPriority(method.getPriority())
                                .setRecommended(method.getPriority() >= 100);
                        result.add(methodDTO);
                    }
                }

                return ApiResponse.success(result);

            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("amount", amount);
                TraceUtils.recordError("GET_PAYMENT_METHODS_FAILED", errorDetails);
                log.error("获取支付方式失败", e);
                return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR);
            }
        });
    }

    @Override
    public Payment findByPaymentNo(String paymentNo) {
        return getOne(
                new QueryWrapper<Payment>().eq("payment_no", paymentNo)
        );
    }

    @Override
    public Payment findByTradeOrderId(String tradeOrderId) {
        return getOne(
                new QueryWrapper<Payment>().eq("trade_order_id", tradeOrderId)
        );
    }

    @Override
    @Transactional
    public void processSubscription(Order order) {
        TraceUtils.executeWithTrace("processSubscription", () -> {
            try {
                // 调用订阅服务处理订阅
                ApiResponse<UserSubscription> result = userSubscriptionService.processSubscription(order);
                
                if (result.getSuccess()) {
                    Map<String, Object> subscriptionDetails = new HashMap<>();
                    subscriptionDetails.put("orderId", order.getId());
                    subscriptionDetails.put("userId", order.getUserId());
                    subscriptionDetails.put("packageId", order.getPackageId());
                    subscriptionDetails.put("subscriptionId", result.getData().getId());
                    TraceUtils.recordBusinessEvent("SUBSCRIPTION_PROCESSED", subscriptionDetails);
                } else {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", order.getId());
                    errorDetails.put("error", result.getMessage());
                    TraceUtils.recordError("SUBSCRIPTION_PROCESS_FAILED", errorDetails);
                    throw new RuntimeException("处理订阅失败: " + result.getMessage());
                }
                
            } catch (Exception e) {
                log.error("处理订阅失败", e);
                throw new RuntimeException("处理订阅失败", e);
            }
            return null;
        });
    }

//    @Override
//    @Transactional
//    public void recordDiscountCodeUsage(Order order) {
//        if (order.getDiscountCodeId() != null) {
//            TraceUtils.executeWithTrace("recordDiscountCodeUsage", () -> {
//                // TODO: 记录优惠码使用
//                Map<String, Object> usageDetails = new HashMap<>();
//                usageDetails.put("orderId", order.getId());
//                usageDetails.put("discountCodeId", order.getDiscountCodeId());
//                usageDetails.put("discountAmount", order.getDiscountAmount());
//                TraceUtils.recordBusinessEvent("DISCOUNT_CODE_USED", usageDetails);
//                return null;
//            });
//        }
//    }

    @Override
    public void sendPaymentSuccessNotification(Order order) {
        TraceUtils.executeWithTrace("sendPaymentSuccessNotification", () -> {
            // TODO: 发送支付成功通知
            Map<String, Object> notificationDetails = new HashMap<>();
            notificationDetails.put("orderId", order.getId());
            notificationDetails.put("userId", order.getUserId());
            TraceUtils.recordBusinessEvent("PAYMENT_NOTIFICATION_SENT", notificationDetails);
            return null;
        });
    }

    @Override
    @Transactional
    public void updatePayment(Payment payment) {
        updateById(payment);
    }

    @Override
    @Transactional
    public void updateOrder(Order order) {
        orderMapper.updateById(order);
    }

    @Override
    public boolean validatePaymentAmount(PaymentMethod paymentMethod, BigDecimal amount) {
        if (amount.compareTo(paymentMethod.getMinAmount()) < 0) {
            return false;
        }
        if (paymentMethod.getMaxAmount() != null &&
                amount.compareTo(paymentMethod.getMaxAmount()) > 0) {
            return false;
        }
        return true;
    }

    @Override
    public ApiResponse<PaymentStatusDTO> queryPaymentStatusByOrderId(Long orderId) {
        return TraceUtils.executeWithTrace("queryPaymentStatusByOrderId", () -> {
            try {
                if (orderId == null || orderId <= 0) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", orderId);
                    TraceUtils.recordError("INVALID_ORDER_ID", errorDetails);
                    throw new IllegalArgumentException("订单ID无效");
                }

                // 先查询是否有成功的支付记录
                Payment successPayment = findSuccessPaymentByOrderId(orderId);
                if (successPayment != null) {
                    PaymentStatusDTO result = new PaymentStatusDTO()
                            .setPaymentNo(successPayment.getPaymentNo())
                            .setStatus(successPayment.getStatus())
                            .setAmount(successPayment.getAmount())
                            .setPaidAt(successPayment.getPaidAt())
                            .setCreatedAt(successPayment.getCreatedAt());
                    return ApiResponse.success(result);
                }

                // 如果没有成功的支付记录，返回最新的支付记录
                Payment latestPayment = findLatestPaymentByOrderId(orderId);
                if (latestPayment == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("orderId", orderId);
                    TraceUtils.recordError("NO_PAYMENT_FOUND", errorDetails);
                    throw PaymentCallbackException.paymentNotFound("订单" + orderId);
                }

                PaymentStatusDTO result = new PaymentStatusDTO()
                        .setPaymentNo(latestPayment.getPaymentNo())
                        .setStatus(latestPayment.getStatus())
                        .setAmount(latestPayment.getAmount())
                        .setPaidAt(latestPayment.getPaidAt())
                        .setCreatedAt(latestPayment.getCreatedAt());

                return ApiResponse.success(result);

            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("QUERY_ORDER_PAYMENT_STATUS_FAILED", errorDetails);
                log.error("查询订单支付状态失败", e);
                return ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR);
            }
        });
    }

    @Override
    public Payment findLatestPaymentByOrderId(Long orderId) {
        return getOne(
                new QueryWrapper<Payment>()
                        .eq("order_id", orderId)
                        .orderByDesc("created_at")
                        .last("LIMIT 1")
        );
    }

    @Override
    public Payment findSuccessPaymentByOrderId(Long orderId) {
        return getOne(
                new QueryWrapper<Payment>()
                        .eq("order_id", orderId)
                        .eq("status", PaymentStatusEnum.SUCCESS.getCode())
                        .orderByDesc("created_at")
                        .last("LIMIT 1")
        );
    }

    // ================== 私有方法 ==================

    /**
     * 查找可复用的支付记录
     * 
     * @param order 订单
     * @param paymentMethod 支付方式
     * @return 可复用的支付记录，如果没有则返回null
     */
    private Payment findReusablePayment(Order order, PaymentMethod paymentMethod) {
        try {
            // 查找该订单下相同支付方式的待支付记录
            QueryWrapper<Payment> queryWrapper = new QueryWrapper<Payment>()
                    .eq("order_id", order.getId())
                    .eq("payment_method_id", paymentMethod.getId())
                    .eq("amount", order.getFinalAmount())
                    .eq("status", PaymentStatusEnum.PENDING.getCode())
                    .orderByDesc("created_at")
                    .last("LIMIT 1");
            
            Payment existingPayment = getOne(queryWrapper);
            
            if (existingPayment != null) {
                Map<String, Object> checkDetails = new HashMap<>();
                checkDetails.put("paymentNo", existingPayment.getPaymentNo());
                checkDetails.put("orderId", order.getId());
                checkDetails.put("paymentMethodId", paymentMethod.getId());
                checkDetails.put("amount", order.getFinalAmount());
                checkDetails.put("status", existingPayment.getStatus());
                checkDetails.put("createdAt", existingPayment.getCreatedAt());
                
                // 检查是否可以复用（这里可以根据业务需要添加更多条件）
                boolean canReuse = canReusePayment(existingPayment);
                checkDetails.put("canReuse", canReuse);
                
                TraceUtils.recordBusinessEvent("PAYMENT_REUSE_CHECK", checkDetails);
                
                return canReuse ? existingPayment : null;
            }
            
            return null;
            
        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", order.getId());
            errorDetails.put("paymentMethodId", paymentMethod.getId());
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("PAYMENT_REUSE_CHECK_FAILED", errorDetails);
            log.warn("查找可复用支付记录失败", e);
            return null; // 查找失败时返回null，继续创建新记录
        }
    }
    
    /**
     * 判断支付记录是否可以复用
     * 
     * @param payment 支付记录
     * @return true如果可以复用，false否则
     */
    private boolean canReusePayment(Payment payment) {
        // 1. 状态必须是待支付
        if (!PaymentStatusEnum.PENDING.getCode().equals(payment.getStatus())) {
            return false;
        }
        
        // 2. 检查创建时间，如果创建时间超过30分钟，不复用（可配置）
        LocalDateTime createdAt = payment.getCreatedAt();
        if (createdAt != null && createdAt.isBefore(LocalDateTime.now().minusMinutes(30))) {
            Map<String, Object> expiredDetails = new HashMap<>();
            expiredDetails.put("paymentNo", payment.getPaymentNo());
            expiredDetails.put("createdAt", createdAt);
            expiredDetails.put("minutesAgo", java.time.Duration.between(createdAt, LocalDateTime.now()).toMinutes());
            TraceUtils.recordBusinessEvent("PAYMENT_TOO_OLD_FOR_REUSE", expiredDetails);
            return false;
        }
        
        // 3. 检查是否有tradeOrderId（如果有说明已经调用过支付平台）
        // 这里可以根据业务需要决定是否允许复用已经调用过支付平台的记录
        
        return true;
    }

    private Payment createPaymentRecord(Order order, PaymentMethod paymentMethod,
                                        String clientIp, String userAgent) {
        Payment payment = new Payment();
        payment.setPaymentNo(generatePaymentNo());
        payment.setOrderId(order.getId());
        payment.setUserId(order.getUserId());
        payment.setPaymentMethodId(paymentMethod.getId());
        payment.setAmount(order.getFinalAmount());
        payment.setCurrency(order.getCurrency());
        payment.setTradeOrderId(generateTradeOrderId());
        payment.setStatus(PaymentStatusEnum.PENDING.getCode());
        payment.setClientIp(clientIp);
        payment.setUserAgent(userAgent);
        // 虎皮椒二维码有效期5分钟
        payment.setExpiredAt(LocalDateTime.now().plusMinutes(5));
        save(payment);
        return payment;
    }

    private Map<String, Object> callPaymentPlatform(Payment payment, PaymentMethod paymentMethod,
                                                    Order order) {
        try {
            // 获取虎皮椒配置
            JSONObject apiConfig = paymentMethod.getApiConfig();
            String appId = (String) apiConfig.get("app_id");
            String appSecret = (String) apiConfig.get("app_secret");
            String apiUrl = (String) apiConfig.get("api_url");

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("version", "1.1");
            params.put("appid", appId);
            params.put("trade_order_id", payment.getTradeOrderId());
            params.put("total_fee", payment.getAmount());
            params.put("title", buildOrderTitle(order));
            params.put("time", XunhupayUtils.getSecondTimestamp(new Date()));
            params.put("notify_url", callbackBaseUrl + "/payment/notify/xunhupay");
//            params.put("return_url", returnUrl != null ? returnUrl : callbackBaseUrl + "/payment/success");
//            params.put("callback_url", cancelUrl != null ? cancelUrl : callbackBaseUrl + "/payment/cancel");
            params.put("plugins", "LabIAI-Payment-System-v1.0");
            params.put("nonce_str", XunhupayUtils.generateNonceStr(16));
            params.put("hash", XunhupayUtils.generateSign(params, appSecret));

            // 调用虎皮椒API
            JSONObject result = XunhupayHttpUtils.postJson(apiUrl, params);
//            Map<String, Object> result = JSONUtil.toBean(response, Map.class);
//            JSONObject result = JSONUtil.parseObj(response);


            // 检查返回结果
            Integer errCode = (Integer) result.get("errcode");
            String errMsg = (String) result.get("errmsg");

            if (errCode != null && errCode == 0 && "success!".equals(errMsg)) {
                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("tradeOrderId", payment.getTradeOrderId());
                successDetails.put("openid", result.get("openid"));
                TraceUtils.recordBusinessEvent("XUNHUPAY_CREATE_SUCCESS", successDetails);
                return result;
            } else {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("tradeOrderId", payment.getTradeOrderId());
                errorDetails.put("errcode", errCode);
                errorDetails.put("errmsg", errMsg);
                TraceUtils.recordError("XUNHUPAY_CREATE_FAILED", errorDetails);
                throw new RuntimeException(errMsg != null ? errMsg : "虎皮椒支付创建失败");
            }

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("tradeOrderId", payment.getTradeOrderId());
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("CALL_PAYMENT_PLATFORM_FAILED", errorDetails);
            log.error("调用支付平台失败", e);
            throw new RuntimeException("支付系统异常，请稍后重试", e);
        }
    }

    private void updatePaymentWithPlatformResult(Payment payment, Map<String, Object> platformResult) {
        payment.setPaymentUrl((String) platformResult.get("url"));
        payment.setQrCodeUrl((String) platformResult.get("url_qrcode"));
        payment.setPlatformOrderId(String.valueOf(platformResult.get("openid")));
        updateById(payment);
    }

    private PaymentCreateResponseDTO buildPaymentCreateResponse(Payment payment, Order order) {
        return new PaymentCreateResponseDTO()
                .setPaymentNo(payment.getPaymentNo())
                .setOrderNo(order.getOrderNo())
                .setAmount(payment.getAmount())
                .setPaymentUrl(payment.getPaymentUrl())
                .setQrCodeUrl(payment.getQrCodeUrl())
                .setExpiredAt(payment.getExpiredAt());

    }

    private Map<String, String> parseCallbackParams(String callbackData) {
        Map<String, String> params = new HashMap<>();
        try {
            // 尝试解析为JSON
            Map<String, Object> jsonMap = JSONUtil.toBean(callbackData, Map.class);
            jsonMap.forEach((key, value) -> params.put(key, value != null ? value.toString() : ""));
        } catch (Exception e) {
            // 解析为表单参数
            if (callbackData != null && !callbackData.isEmpty()) {
                String[] pairs = callbackData.split("&");
                for (String pair : pairs) {
                    String[] kv = pair.split("=", 2);
                    if (kv.length == 2) {
                        params.put(kv[0], kv[1]);
                    }
                }
            }
        }
        return params;
    }

    private boolean verifyCallbackSignature(Payment payment, Map<String, String> params) {
        try {
            PaymentMethod paymentMethod = paymentMethodMapper.selectById(payment.getPaymentMethodId());
            Map<String, Object> apiConfig = paymentMethod.getApiConfig();
            String appSecret = (String) apiConfig.get("app_secret");

            return XunhupayUtils.verifySign(params, appSecret);
        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("paymentNo", payment.getPaymentNo());
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("VERIFY_SIGNATURE_FAILED", errorDetails);
            return false;
        }
    }

    @Transactional
    public void processPaymentSuccess(Payment payment, Map<String, String> params) {
        try {
            Map<String, Object> processDetails = new HashMap<>();
            processDetails.put("paymentNo", payment.getPaymentNo());
            processDetails.put("orderId", payment.getOrderId());
            processDetails.put("amount", payment.getAmount());
            TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PROCESS_START", processDetails);

            // 更新支付记录
            payment.setStatus(PaymentStatusEnum.SUCCESS.getCode());
            payment.setTransactionId(params.get("transaction_id"));
            payment.setPlatformOrderId(params.get("open_order_id"));

            // 将Map<String, String>转换为Map<String, Object>
            Map<String, Object> callbackData = new HashMap<>();
            params.forEach(callbackData::put);
            payment.setCallbackData(callbackData);

            payment.setPaidAt(LocalDateTime.now());
            updateById(payment);

            // 更新订单状态
            Order order = orderMapper.selectById(payment.getOrderId());
            String originalOrderStatus = order.getStatus();
            order.setStatus(OrderStatusEnum.PAID.getCode());
            updateOrder(order);

            // 记录订单类型和相关信息
            Map<String, Object> orderDetails = new HashMap<>();
            orderDetails.put("orderId", order.getId());
            orderDetails.put("orderNo", order.getOrderNo());
            orderDetails.put("orderType", order.getOrderType());
            orderDetails.put("packageId", order.getPackageId());
            orderDetails.put("userId", order.getUserId());
            orderDetails.put("originalStatus", originalOrderStatus);
            orderDetails.put("newStatus", order.getStatus());
            TraceUtils.recordBusinessEvent("ORDER_STATUS_UPDATED", orderDetails);

            // 处理订阅
            processSubscription(order);

            // 发送通知
            sendPaymentSuccessNotification(order);

            Map<String, Object> successDetails = new HashMap<>();
            successDetails.put("paymentNo", payment.getPaymentNo());
            successDetails.put("orderId", order.getId());
            successDetails.put("orderType", order.getOrderType());
            successDetails.put("packageId", order.getPackageId());
            successDetails.put("userId", order.getUserId());
            TraceUtils.recordBusinessEvent("PAYMENT_SUCCESS_PROCESS_COMPLETE", successDetails);

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("paymentNo", payment.getPaymentNo());
            errorDetails.put("orderId", payment.getOrderId());
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("PAYMENT_SUCCESS_PROCESS_FAILED", errorDetails);
            log.error("处理支付成功失败", e);
            throw e; // 重新抛出异常，确保事务回滚
        }
    }

    private String buildOrderTitle(Order order) {
        try {
            Package pkg = packageMapper.selectById(order.getPackageId());
            PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());

            String packageName = pkg.getDisplayName() != null ? pkg.getDisplayName() : pkg.getName();
            String billingInfo = getBillingDisplayText(packagePrice.getBillingCycle(), packagePrice.getCycleCount());

            // 根据订单类型生成不同的标题
            String orderType = order.getOrderType();
            if ("RENEWAL".equals(orderType)) {
                return "续费 " + packageName + " - " + billingInfo;
            } else if ("UPGRADE".equals(orderType)) {
                return "升级到 " + packageName + " - 剩余时间";
            } else {
                return packageName + " - " + billingInfo;
            }
        } catch (Exception e) {
            return "LabIAI订阅服务";
        }
    }

    /**
     * 获取计费周期显示文本
     */
    private String getBillingDisplayText(String billingCycle, Integer cycleCount) {
        String unit;
        switch (billingCycle) {
            case "DAY":
                unit = "天";
                break;
            case "MONTH":
                unit = "个月";
                break;
            case "QUARTER":
                unit = "季度";
                break;
            case "YEAR":
                unit = "年";
                break;
            default:
                unit = billingCycle;
        }
        return cycleCount + unit;
    }

    private String generateOrderNo() {
        return DateUtil.format(new Date(), "yyyyMMdd") + IdUtil.getSnowflakeNextIdStr();
    }

    private String generatePaymentNo() {
        return "PAY" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + IdUtil.fastSimpleUUID().substring(0, 6).toUpperCase();
    }

    private String generateTradeOrderId() {
        return "TXN" + System.currentTimeMillis() + IdUtil.randomUUID().substring(0, 8).toUpperCase();
    }
} 