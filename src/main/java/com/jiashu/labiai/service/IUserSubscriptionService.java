package com.jiashu.labiai.service;

import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.response.user.UserSubscriptionInfoResponse;
import com.jiashu.labiai.entity.Order;
import com.jiashu.labiai.entity.UserSubscription;

/**
 * 用户订阅服务接口
 */
public interface IUserSubscriptionService {
    
    /**
     * 获取用户当前有效订阅
     * @param userId 用户ID
     * @return 订阅信息，如果没有有效订阅则返回null
     */
    UserSubscription getCurrentSubscription(Long userId);
    
    /**
     * 处理订阅（支付成功后调用）
     * 根据用户是否已有订阅，自动判断是新建、续费还是升级
     * @param order 已支付的订单
     * @return 处理结果
     */
    ApiResponse<UserSubscription> processSubscription(Order order);
    
    /**
     * 创建新订阅
     * @param order 订单信息
     * @return 创建的订阅
     */
    UserSubscription createNewSubscription(Order order);
    
    /**
     * 续费订阅（同套餐）
     * @param order 订单信息
     * @param currentSubscription 当前订阅
     * @return 更新后的订阅
     */
    UserSubscription renewSubscription(Order order, UserSubscription currentSubscription);
    
    /**
     * 升级/降级订阅（不同套餐）
     * @param order 订单信息
     * @param currentSubscription 当前订阅
     * @return 更新后的订阅
     */
    UserSubscription upgradeSubscription(Order order, UserSubscription currentSubscription);
    
    /**
     * 取消订阅
     * @param userId 用户ID
     */
    void cancelSubscription(Long userId);
    
    /**
     * 检查并更新过期订阅（定时任务调用）
     */
    void checkAndUpdateExpiredSubscriptions();
    
    /**
     * 获取用户订阅信息（包含当前订阅和历史记录）
     * @param userId 用户ID
     * @return 用户订阅信息
     */
    UserSubscriptionInfoResponse getUserSubscriptionInfo(Long userId);
} 