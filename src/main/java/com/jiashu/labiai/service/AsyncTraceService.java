package com.jiashu.labiai.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 异步服务示例
 * 展示异步任务中的链路追踪使用
 */
@Service
public class AsyncTraceService {
    
    private static final Logger log = LoggerFactory.getLogger(AsyncTraceService.class);
    
    /**
     * 异步发送邮件通知（保留TraceId）
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> sendEmailAsync(String email, String subject, String content) {
        log.info("开始异步发送邮件: email={}, subject={}", email, subject);
        
        try {
            // 模拟邮件发送耗时
            Thread.sleep(1000);
            
            log.info("异步邮件发送成功: email={}", email);
            
        } catch (Exception e) {
            log.error("异步邮件发送失败: email={}", email, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 异步发送短信通知
     */
    @Async("smallTaskExecutor")
    public CompletableFuture<Boolean> sendSmsAsync(String phone, String message) {
        log.info("开始异步发送短信: phone={}, message={}", phone, message);
        
        try {
            // 模拟短信发送
            Thread.sleep(500);
            
            log.info("异步短信发送成功: phone={}", phone);
            return CompletableFuture.completedFuture(true);
            
        } catch (Exception e) {
            log.error("异步短信发送失败: phone={}", phone, e);
            return CompletableFuture.completedFuture(false);
        }
    }
    
    /**
     * 异步处理用户数据
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> processUserDataAsync(Long userId, String operation) {
        log.info("开始异步处理用户数据: userId={}, operation={}", userId, operation);
        
        try {
            // 模拟数据处理
            Thread.sleep(2000);
            
            log.info("用户数据处理完成: userId={}, operation={}", userId, operation);
            
        } catch (Exception e) {
            log.error("用户数据处理失败: userId={}, operation={}", userId, operation, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 异步生成报表
     */
    @Async("largeTaskExecutor")
    public CompletableFuture<String> generateReportAsync(String reportType, String dateRange) {
        log.info("开始异步生成报表: reportType={}, dateRange={}", reportType, dateRange);
        
        try {
            // 模拟报表生成（大任务）
            Thread.sleep(5000);
            
            String reportId = "RPT_" + System.currentTimeMillis();
            log.info("报表生成完成: reportType={}, reportId={}", reportType, reportId);
            
            return CompletableFuture.completedFuture(reportId);
            
        } catch (Exception e) {
            log.error("报表生成失败: reportType={}, dateRange={}", reportType, dateRange, e);
            return CompletableFuture.completedFuture(null);
        }
    }
    
    /**
     * 异步记录审计日志
     */
    @Async("smallTaskExecutor")
    public CompletableFuture<Void> recordAuditLogAsync(String action, String userId, String details) {
        log.info("开始记录审计日志: action={}, userId={}, details={}", action, userId, details);
        
        try {
            // 模拟写入审计日志
//            Thread.sleep(200);
            
            log.info("审计日志记录完成: action={}, userId={}", action, userId);
            
        } catch (Exception e) {
            log.error("审计日志记录失败: action={}, userId={}", action, userId, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 异步清理缓存
     */
    @Async("smallTaskExecutor")
    public CompletableFuture<Void> clearCacheAsync(String cacheKey) {
        log.info("开始异步清理缓存: cacheKey={}", cacheKey);
        
        try {
            // 模拟缓存清理
            Thread.sleep(100);
            
            log.info("缓存清理完成: cacheKey={}", cacheKey);
            
        } catch (Exception e) {
            log.error("缓存清理失败: cacheKey={}", cacheKey, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 组合异步任务示例
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> processUserLoginAsync(String email, String phone, Long userId) {
        log.info("开始处理用户登录后续任务: email={}, userId={}", email, userId);
        
        try {
            // 组合多个异步任务
            CompletableFuture<Void> emailTask = sendEmailAsync(email, "登录通知", "您已成功登录系统");
            CompletableFuture<Boolean> smsTask = sendSmsAsync(phone, "您已登录系统");
            CompletableFuture<Void> auditTask = recordAuditLogAsync("USER_LOGIN", userId.toString(), "用户登录成功");
            
            // 等待所有任务完成
            CompletableFuture.allOf(emailTask, smsTask, auditTask).get();
            
            log.info("用户登录后续任务全部完成: userId={}", userId);
            
        } catch (Exception e) {
            log.error("用户登录后续任务处理失败: userId={}", userId, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
} 