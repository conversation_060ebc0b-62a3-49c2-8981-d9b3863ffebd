package com.jiashu.labiai.service;

import com.jiashu.labiai.dto.request.auth.UserRegisterRequest;
import com.jiashu.labiai.dto.response.auth.UserRegisterResponse;

/**
 * 用户注册服务接口
 * 
 * <AUTHOR>
 */
public interface UserRegisterService {
    
    /**
     * 执行用户注册
     * 
     * @param request 注册请求
     * @return 注册响应
     */
    UserRegisterResponse registerUser(UserRegisterRequest request);
    
    /**
     * 检查邮箱是否已被注册
     * 
     * @param email 邮箱地址
     * @return true-已存在，false-不存在
     */
    boolean isEmailExists(String email);
    
    /**
     * 检查手机号是否已被注册
     * 
     * @param phone 手机号
     * @return true-已存在，false-不存在
     */
    boolean isPhoneExists(String phone);
    
    /**
     * 生成默认昵称
     * 
     * @param email 邮箱地址
     * @return 昵称
     */
    String generateDefaultNickname(String email);
} 