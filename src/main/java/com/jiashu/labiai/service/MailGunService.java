package com.jiashu.labiai.service;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.jiashu.labiai.config.MailGunConfig;
import com.jiashu.labiai.constants.RedisKeyConstants;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.BusinessException;
import com.jiashu.labiai.exception.RateLimitException;
import com.jiashu.labiai.exception.ThirdPartyServiceException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.TraceUtils;
import com.mailgun.api.v3.MailgunMessagesApi;
import com.mailgun.client.MailgunClient;
import com.mailgun.model.message.Message;
import com.mailgun.model.message.MessageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MailGun 邮件发送服务
 * 基于官方 mailgun-java SDK
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MailGunService {
    
    private final MailGunConfig mailGunConfig;
    private final StringRedisTemplate stringRedisTemplate;
    
    private MailgunMessagesApi mailgunMessagesApi;
    
    /**
     * 初始化 MailGun 客户端
     */
    @PostConstruct
    public void initClient() {
        long startTime = System.currentTimeMillis();
        
        try {
            if (mailGunConfig.isUseEuServer()) {
                // 欧盟服务器
                mailgunMessagesApi = MailgunClient.config("https://api.eu.mailgun.net/", mailGunConfig.getApiKey())
                        .createApi(MailgunMessagesApi.class);
            } else {
                // 美国服务器（默认）
                mailgunMessagesApi = MailgunClient.config(mailGunConfig.getApiKey())
                        .createApi(MailgunMessagesApi.class);
            }
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录成功的业务事件
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("domain", mailGunConfig.getDomain());
            eventData.put("server", mailGunConfig.isUseEuServer() ? "EU" : "US");
            eventData.put("duration", duration);
            
            TraceUtils.recordBusinessEvent("mailgun_client_init_success", eventData);
            
            log.info("MailGun 客户端初始化成功 - 域名: {}, 服务器: {}, 耗时: {}ms", 
                mailGunConfig.getDomain(), 
                mailGunConfig.isUseEuServer() ? "EU" : "US",
                duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("mailgun_client_init_error", e.getMessage(), e);
            
            log.error("MailGun 客户端初始化失败 - 耗时: {}ms", duration, e);
        }
    }
    
    /**
     * 发送验证码邮件
     */
    public void sendVerificationCode(String email) {
        long startTime = System.currentTimeMillis();
        
        // 记录业务事件开始
        Map<String, Object> startEventData = new HashMap<>();
        startEventData.put("email", email);
        startEventData.put("deviceInfo", TraceContext.getDeviceSummary());
        TraceUtils.recordBusinessEvent("verification_code_send_start", startEventData);
        
        try {
            // 生成6位数字验证码
            String code = RandomUtil.randomNumbers(6);
            
            // 构建验证码邮件内容
            String html = buildVerificationEmailHtml(code);
            String text = buildVerificationEmailText(code);
            
            // 构建包含验证码的邮件主题
            String subject = buildVerificationSubject(code);
            
            // 构建邮件消息
            Message message = Message.builder()
                    .from(buildFromAddress())
                    .to(email)
                    .subject(subject)
                    .text(text)
                    .html(html)
                    .tag("verification")
                    .build();
            
            // 发送邮件
            MessageResponse response = mailgunMessagesApi.sendMessage(mailGunConfig.getDomain(), message);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (response != null && StrUtil.isNotBlank(response.getId())) {
                // 将验证码存储到Redis
                String key = RedisKeyConstants.buildVerificationCodeKey(email);
                stringRedisTemplate.opsForValue().set(key, code, 
                    mailGunConfig.getVerificationExpireMinutes(), TimeUnit.MINUTES);
                
                // 记录成功业务事件
                Map<String, Object> successEventData = new HashMap<>();
                successEventData.put("email", email);
                successEventData.put("messageId", response.getId());
                successEventData.put("code", code);
                successEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("verification_code_send_success", successEventData);
                
                log.info("验证码邮件发送成功 - 邮箱: {}, 消息ID: {}, 验证码: {}, 耗时: {}ms", 
                    email, response.getId(), code, duration);
            } else {
                // 记录发送失败事件
                Map<String, Object> failEventData = new HashMap<>();
                failEventData.put("email", email);
                failEventData.put("response", response != null ? response.toString() : "null");
                failEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("verification_code_send_failed", failEventData);
                
                log.error("验证码邮件发送失败 - 邮箱: {}, 响应: {}, 耗时: {}ms", email, response, duration);
                throw new ThirdPartyServiceException(ResponseCode.EMAIL_SERVICE_ERROR);
            }
            
        } catch (ThirdPartyServiceException e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("verification_code_send_service_error", e.getMessage(), e);
            throw e; // 重新抛出自定义异常
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("verification_code_send_unexpected_error", e.getMessage(), e);
            
            log.error("发送验证码邮件异常 - 邮箱: {}, 耗时: {}ms", email, duration, e);
            throw new ThirdPartyServiceException("验证码发送失败: " + e.getMessage(), e);
        }
    }
    

    
    /**
     * 发送普通邮件
     */
    public ApiResponse<String> sendEmail(String to, String subject, String htmlContent) {
        return sendEmail(to, subject, htmlContent, null);
    }
    
    /**
     * 发送邮件（支持HTML和纯文本）
     */
    public ApiResponse<String> sendEmail(String to, String subject, String htmlContent, String textContent) {
        long startTime = System.currentTimeMillis();
        
        // 记录邮件发送开始事件
        Map<String, Object> startEventData = new HashMap<>();
        startEventData.put("to", to);
        startEventData.put("subject", subject);
        startEventData.put("hasHtml", StrUtil.isNotBlank(htmlContent));
        startEventData.put("hasText", StrUtil.isNotBlank(textContent));
        startEventData.put("deviceInfo", TraceContext.getDeviceSummary());
        TraceUtils.recordBusinessEvent("email_send_start", startEventData);
        
        try {
            Message.MessageBuilder messageBuilder = Message.builder()
                    .from(buildFromAddress())
                    .to(to)
                    .subject(subject);
            
            if (StrUtil.isNotBlank(htmlContent)) {
                messageBuilder.html(htmlContent);
            }
            if (StrUtil.isNotBlank(textContent)) {
                messageBuilder.text(textContent);
            }
            
            Message message = messageBuilder.build();
            
            // 发送邮件
            MessageResponse response = mailgunMessagesApi.sendMessage(mailGunConfig.getDomain(), message);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (response != null && StrUtil.isNotBlank(response.getId())) {
                // 记录邮件发送成功事件
                Map<String, Object> successEventData = new HashMap<>();
                successEventData.put("to", to);
                successEventData.put("subject", subject);
                successEventData.put("messageId", response.getId());
                successEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("email_send_success", successEventData);
                
                log.info("邮件发送成功 - 收件人: {}, 主题: {}, 消息ID: {}, 耗时: {}ms", 
                    to, subject, response.getId(), duration);
                return ApiResponse.success(response.getId(), "邮件发送成功");
            } else {
                // 记录邮件发送失败事件
                Map<String, Object> failEventData = new HashMap<>();
                failEventData.put("to", to);
                failEventData.put("subject", subject);
                failEventData.put("response", response != null ? response.toString() : "null");
                failEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("email_send_failed", failEventData);
                
                log.error("邮件发送失败 - 收件人: {}, 主题: {}, 响应: {}, 耗时: {}ms", to, subject, response, duration);
                return ApiResponse.error(ResponseCode.EMAIL_SERVICE_ERROR, "邮件发送失败");
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("email_send_unexpected_error", e.getMessage(), e);
            
            log.error("邮件发送异常 - 收件人: {}, 主题: {}, 耗时: {}ms", to, subject, duration, e);
            return ApiResponse.error(ResponseCode.EMAIL_SERVICE_ERROR, "邮件发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建发件人地址
     */
    private String buildFromAddress() {
        return String.format("%s <%s>", mailGunConfig.getFromName(), mailGunConfig.getFromEmail());
    }
    
    /**
     * 构建验证码邮件HTML内容
     */
    private String buildVerificationEmailHtml(String code) {
        try {
            // 读取HTML模板文件
            String template = loadEmailTemplate("mail-template/verification-code.html");
            
            // 替换模板中的占位符
            return template.replace("{{VERIFICATION_CODE}}", code);
            
        } catch (Exception e) {
            log.error("读取邮件模板失败，使用默认模板", e);
            // 如果模板读取失败，使用原来的默认模板
            return buildDefaultVerificationEmailHtml(code);
        }
    }
    
    /**
     * 加载邮件模板文件
     */
    private String loadEmailTemplate(String templatePath) throws IOException {
        long startTime = System.currentTimeMillis();
        
        try {
            // 使用Hutool的ResourceUtil读取classpath资源
            String content = ResourceUtil.readUtf8Str(templatePath);
            if (StrUtil.isBlank(content)) {
                throw new IOException("模板文件不存在或为空: " + templatePath);
            }
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录模板加载成功事件
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("templatePath", templatePath);
            eventData.put("contentLength", content.length());
            eventData.put("duration", duration);
            
            TraceUtils.recordBusinessEvent("email_template_load_success", eventData);
            
            log.info("成功加载邮件模板: {}, 内容长度: {}, 耗时: {}ms", templatePath, content.length(), duration);
//            log.debug("模板内容预览: {}", content.substring(0, Math.min(100, content.length())));
            
            return content;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("email_template_load_error", e.getMessage(), e);
            
            log.error("加载邮件模板失败: {}, 耗时: {}ms", templatePath, duration, e);
            throw new IOException("加载邮件模板失败", e);
        }
    }
    
    /**
     * 构建默认验证码邮件HTML内容（备用方案）
     */
    private String buildDefaultVerificationEmailHtml(String code) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>")
            .append("<html>")
            .append("<head>")
            .append("<meta charset=\"UTF-8\">")
            .append("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">")
            .append("<title>邮箱验证码</title>")
            .append("</head>")
            .append("<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0;\">")
            .append("<div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">")
            .append("<div style=\"text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px;\">")
            .append("<h1 style=\"color: #007bff; margin: 0; font-size: 28px;\">邮箱验证</h1>")
            .append("</div>")
            .append("<div style=\"background-color: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">")
            .append("<h2 style=\"color: #333; margin-bottom: 20px; font-size: 24px;\">您的验证码</h2>")
            .append("<div style=\"background-color: #007bff; color: white; padding: 15px 30px; border-radius: 8px; display: inline-block; margin: 20px 0;\">")
            .append("<span style=\"font-size: 32px; font-weight: bold; letter-spacing: 8px;\">").append(code).append("</span>")
            .append("</div>")
            .append("<p style=\"margin-top: 20px; color: #666; font-size: 16px;\">")
            .append("此验证码将在 <strong style=\"color: #007bff;\">").append(mailGunConfig.getVerificationExpireMinutes()).append(" 分钟</strong> 后过期，请及时使用。")
            .append("</p>")
            .append("</div>")
            .append("<div style=\"background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 30px;\">")
            .append("<p style=\"margin: 0; color: #856404; font-size: 14px;\">")
            .append("<strong>安全提示：</strong>请勿将验证码泄露给他人，我们的工作人员不会向您索要验证码。")
            .append("</p>")
            .append("</div>")
            .append("<div style=\"text-align: center; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;\">")
            .append("<p>如果您没有申请此验证码，请忽略此邮件。</p>")
            .append("<p>此邮件由系统自动发送，请勿回复。</p>")
            .append("<p style=\"margin-top: 15px;\">")
            .append("&copy; ").append(java.time.Year.now()).append(" ").append(mailGunConfig.getFromName())
            .append("</p>")
            .append("</div>")
            .append("</div>")
            .append("</body>")
            .append("</html>");
        
        return html.toString();
    }
    
    /**
     * 构建验证码邮件纯文本内容
     */
    private String buildVerificationEmailText(String code) {
        StringBuilder text = new StringBuilder();
        text.append("【邮箱验证码】\n\n")
            .append("您的验证码是: ").append(code).append("\n\n")
            .append("此验证码将在 ").append(mailGunConfig.getVerificationExpireMinutes()).append(" 分钟后过期，请及时使用。\n\n")
            .append("安全提示：\n")
            .append("- 请勿将验证码泄露给他人\n")
            .append("- 我们的工作人员不会向您索要验证码\n\n")
            .append("如果您没有申请此验证码，请忽略此邮件。\n\n")
            .append("========================================\n")
            .append("此邮件由系统自动发送，请勿回复。\n")
            .append("© ").append(java.time.Year.now()).append(" ").append(mailGunConfig.getFromName());
        
        return text.toString();
    }
    
    /**
     * 发送HTML邮件的便捷方法
     */
    public ApiResponse<String> sendHtmlEmail(String to, String subject, String htmlContent) {
        return sendEmail(to, subject, htmlContent, null);
    }
    
    /**
     * 发送纯文本邮件的便捷方法
     */
    public ApiResponse<String> sendTextEmail(String to, String subject, String textContent) {
        return sendEmail(to, subject, null, textContent);
    }
    
    /**
     * 批量发送邮件
     */
    public ApiResponse<String> sendBatchEmails(String[] recipients, String subject, String htmlContent, String textContent) {
        try {
            Message.MessageBuilder messageBuilder = Message.builder()
                    .from(buildFromAddress())
                    .subject(subject);
            
            // 添加所有收件人
            for (String recipient : recipients) {
                messageBuilder.to(recipient);
            }
            
            if (StrUtil.isNotBlank(htmlContent)) {
                messageBuilder.html(htmlContent);
            }
            if (StrUtil.isNotBlank(textContent)) {
                messageBuilder.text(textContent);
            }
            
            Message message = messageBuilder.build();
            
            // 发送邮件
            MessageResponse response = mailgunMessagesApi.sendMessage(mailGunConfig.getDomain(), message);
            
            if (response != null && StrUtil.isNotBlank(response.getId())) {
                log.info("批量邮件发送成功 - 收件人数量: {}, 主题: {}, 消息ID: {}", 
                    recipients.length, subject, response.getId());
                return ApiResponse.success(response.getId(), "批量邮件发送成功");
            } else {
                log.error("批量邮件发送失败 - 收件人数量: {}, 主题: {}, 响应: {}", 
                    recipients.length, subject, response);
                return ApiResponse.error(ResponseCode.EMAIL_SERVICE_ERROR, "批量邮件发送失败");
            }
            
        } catch (Exception e) {
            log.error("批量邮件发送异常 - 收件人数量: {}, 主题: {}", recipients.length, subject, e);
            return ApiResponse.error(ResponseCode.EMAIL_SERVICE_ERROR, "批量邮件发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送基于模板的邮件
     */
    public ApiResponse<String> sendTemplateEmail(String to, String subject, String templatePath, Map<String, String> variables) {
        try {
            // 加载并处理模板
            String htmlContent = loadAndProcessTemplate(templatePath, variables);
            
            // 发送邮件
            return sendHtmlEmail(to, subject, htmlContent);
            
        } catch (Exception e) {
            log.error("发送模板邮件异常 - 收件人: {}, 模板: {}", to, templatePath, e);
            return ApiResponse.error(ResponseCode.EMAIL_SERVICE_ERROR, "邮件发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载并处理邮件模板（替换占位符）
     */
    private String loadAndProcessTemplate(String templatePath, Map<String, String> variables) throws IOException {
        String template = loadEmailTemplate(templatePath);
        
        if (variables != null && !variables.isEmpty()) {
            for (Map.Entry<String, String> entry : variables.entrySet()) {
                String placeholder = "{{" + entry.getKey() + "}}";
                template = template.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
            }
        }
        
        return template;
    }
    
    /**
     * 发送验证码邮件（支持设备指纹限制）
     */
    public void sendVerificationCodeWithFingerprint(String email, String fingerprint) {
        long startTime = System.currentTimeMillis();
        
        // 记录业务事件开始
        Map<String, Object> startEventData = new HashMap<>();
        startEventData.put("email", email);
        startEventData.put("fingerprint", fingerprint);
        startEventData.put("deviceInfo", TraceContext.getDeviceSummary());
        TraceUtils.recordBusinessEvent("verification_code_fingerprint_send_start", startEventData);
        
        try {
            // 验证发送频率限制
            validateSendingLimit(email, fingerprint);
            
            // 生成6位数字验证码
            String code = RandomUtil.randomNumbers(6);
            
            // 准备模板变量
            Map<String, String> variables = new HashMap<>();
            variables.put("VERIFICATION_CODE", code);
            
            // 使用模板构建邮件内容
            String htmlContent = loadAndProcessTemplate("mail-template/verification-code.html", variables);
            String textContent = buildVerificationEmailText(code);
            
            // 构建包含验证码的邮件主题
            String subject = buildVerificationSubject(code);
            
            // 构建邮件消息
            Message.MessageBuilder messageBuilder = Message.builder()
                    .from(buildFromAddress())
                    .to(email)
                    .subject(subject)
                    .html(htmlContent)
                    .tag("verification-fingerprint");
            
            if (StrUtil.isNotBlank(textContent)) {
                messageBuilder.text(textContent);
            }
            
            Message message = messageBuilder.build();
            
            // 发送邮件
            MessageResponse response = mailgunMessagesApi.sendMessage(mailGunConfig.getDomain(), message);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (response != null && StrUtil.isNotBlank(response.getId())) {
                // 将验证码存储到Redis
                String key = RedisKeyConstants.buildVerificationCodeKey(email);
                stringRedisTemplate.opsForValue().set(key, code, 
                    mailGunConfig.getVerificationExpireMinutes(), TimeUnit.MINUTES);
                
                // 记录发送频率
                recordSendingLimit(email, fingerprint);
                
                // 记录成功业务事件
                Map<String, Object> successEventData = new HashMap<>();
                successEventData.put("email", email);
                successEventData.put("fingerprint", fingerprint);
                successEventData.put("messageId", response.getId());
                successEventData.put("code", code);
                successEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("verification_code_fingerprint_send_success", successEventData);
                
                log.info("验证码邮件发送成功（设备指纹版） - 邮箱: {}, 设备指纹: {}, 消息ID: {}, 验证码: {}, 耗时: {}ms", 
                    email, fingerprint, response.getId(), code, duration);
            } else {
                // 记录发送失败事件
                Map<String, Object> failEventData = new HashMap<>();
                failEventData.put("email", email);
                failEventData.put("fingerprint", fingerprint);
                failEventData.put("response", response != null ? response.toString() : "null");
                failEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("verification_code_fingerprint_send_failed", failEventData);
                
                log.error("验证码邮件发送失败（设备指纹版） - 邮箱: {}, 设备指纹: {}, 响应: {}, 耗时: {}ms", 
                    email, fingerprint, response, duration);
                throw new ThirdPartyServiceException(ResponseCode.EMAIL_SERVICE_ERROR);
            }
            
        } catch (RateLimitException e) {
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录限流事件
            Map<String, Object> rateLimitEventData = new HashMap<>();
            rateLimitEventData.put("email", email);
            rateLimitEventData.put("fingerprint", fingerprint);
            rateLimitEventData.put("reason", e.getMessage());
            rateLimitEventData.put("duration", duration);
            
            TraceUtils.recordBusinessEvent("verification_code_rate_limited", rateLimitEventData);
            
            log.warn("验证码发送被限制 - 邮箱: {}, 设备指纹: {}, 原因: {}, 耗时: {}ms", 
                email, fingerprint, e.getMessage(), duration);
            throw e; // 重新抛出频率限制异常
        } catch (ThirdPartyServiceException e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("verification_code_fingerprint_service_error", e.getMessage(), e);
            throw e; // 重新抛出邮件发送异常
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("verification_code_fingerprint_unexpected_error", e.getMessage(), e);
            
            log.error("发送验证码邮件异常（设备指纹版） - 邮箱: {}, 设备指纹: {}, 耗时: {}ms", 
                email, fingerprint, duration, e);
            throw new ThirdPartyServiceException("验证码发送失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询发送限制状态
     */
    public Map<String, Object> getSendingLimitStatus(String email, String fingerprint) {
        Map<String, Object> status = new HashMap<>();
        
        // 邮箱间隔限制状态
        String emailIntervalKey = RedisKeyConstants.buildEmailIntervalKey(email);
        boolean emailIntervalBlocked = stringRedisTemplate.hasKey(emailIntervalKey);
        Long emailIntervalTtl = emailIntervalBlocked ? stringRedisTemplate.getExpire(emailIntervalKey) : 0L;
        
        // 设备指纹间隔限制状态
        String fingerprintIntervalKey = RedisKeyConstants.buildFingerprintIntervalKey(fingerprint);
        boolean fingerprintIntervalBlocked = StrUtil.isNotBlank(fingerprint) && 
            stringRedisTemplate.hasKey(fingerprintIntervalKey);
        Long fingerprintIntervalTtl = fingerprintIntervalBlocked ? 
            stringRedisTemplate.getExpire(fingerprintIntervalKey) : 0L;
        
        // 每小时发送次数
        String emailHourlyKey = RedisKeyConstants.buildEmailHourlyKey(email, getCurrentHour());
        String emailHourlyCount = stringRedisTemplate.opsForValue().get(emailHourlyKey);
        int emailHourlyUsed = StrUtil.isNotBlank(emailHourlyCount) ? Integer.parseInt(emailHourlyCount) : 0;
        
        String fingerprintHourlyKey = RedisKeyConstants.buildFingerprintHourlyKey(fingerprint, getCurrentHour());
        String fingerprintHourlyCount = stringRedisTemplate.opsForValue().get(fingerprintHourlyKey);
        int fingerprintHourlyUsed = StrUtil.isNotBlank(fingerprintHourlyCount) ? 
            Integer.parseInt(fingerprintHourlyCount) : 0;
        
        // 每天发送次数
        String emailDailyKey = RedisKeyConstants.buildEmailDailyKey(email, getCurrentDate());
        String emailDailyCount = stringRedisTemplate.opsForValue().get(emailDailyKey);
        int emailDailyUsed = StrUtil.isNotBlank(emailDailyCount) ? Integer.parseInt(emailDailyCount) : 0;
        
        String fingerprintDailyKey = RedisKeyConstants.buildFingerprintDailyKey(fingerprint, getCurrentDate());
        String fingerprintDailyCount = stringRedisTemplate.opsForValue().get(fingerprintDailyKey);
        int fingerprintDailyUsed = StrUtil.isNotBlank(fingerprintDailyCount) ? 
            Integer.parseInt(fingerprintDailyCount) : 0;
        
        status.put("emailIntervalBlocked", emailIntervalBlocked);
        status.put("emailIntervalTtl", emailIntervalTtl);
        status.put("fingerprintIntervalBlocked", fingerprintIntervalBlocked);
        status.put("fingerprintIntervalTtl", fingerprintIntervalTtl);
        status.put("emailHourlyUsed", emailHourlyUsed);
        status.put("emailHourlyLimit", mailGunConfig.getEmailHourlyLimit());
        status.put("fingerprintHourlyUsed", fingerprintHourlyUsed);
        status.put("fingerprintHourlyLimit", mailGunConfig.getFingerprintHourlyLimit());
        status.put("emailDailyUsed", emailDailyUsed);
        status.put("emailDailyLimit", mailGunConfig.getEmailDailyLimit());
        status.put("fingerprintDailyUsed", fingerprintDailyUsed);
        status.put("fingerprintDailyLimit", mailGunConfig.getFingerprintDailyLimit());
        
        return status;
    }
    
    /**
     * 构建验证码邮件主题
     */
    private String buildVerificationSubject(String code) {
        return buildVerificationSubject(code, mailGunConfig.getSubjectFormat());
    }
    
    /**
     * 构建验证码邮件主题（自定义格式）
     */
    private String buildVerificationSubject(String code, String format) {
        switch (format.toLowerCase()) {
            case "bracket":
                return String.format("[%s] 邮箱验证码", code);
            case "simple":
                return String.format("您的验证码：%s", code);
            case "short":
                return String.format("%s - 邮箱验证", code);
            case "default":
            default:
                return String.format("验证码 %s - %s", code, mailGunConfig.getFromName());
        }
    }
    
    /**
     * 验证发送频率限制
     */
    private void validateSendingLimit(String email, String fingerprint) {
        long startTime = System.currentTimeMillis();
        
        // 记录限流验证开始事件
        Map<String, Object> startEventData = new HashMap<>();
        startEventData.put("email", email);
        startEventData.put("fingerprint", fingerprint);
        TraceUtils.recordBusinessEvent("rate_limit_validation_start", startEventData);
        
        try {
            // 验证邮箱发送间隔
            String emailIntervalKey = RedisKeyConstants.buildEmailIntervalKey(email);
            String lastEmailSend = stringRedisTemplate.opsForValue().get(emailIntervalKey);
            if (StrUtil.isNotBlank(lastEmailSend)) {
                Map<String, Object> rateLimitEventData = new HashMap<>();
                rateLimitEventData.put("email", email);
                rateLimitEventData.put("type", "email_interval");
                rateLimitEventData.put("limit", mailGunConfig.getEmailInterval());
                TraceUtils.recordBusinessEvent("rate_limit_triggered", rateLimitEventData);
                
                throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "发送过于频繁，请" + mailGunConfig.getEmailInterval() + "秒后再试");
            }
            
            // 验证设备指纹发送间隔
            if (StrUtil.isNotBlank(fingerprint)) {
                String fingerprintIntervalKey = "fingerprint_interval:" + fingerprint;
                String lastFingerprintSend = stringRedisTemplate.opsForValue().get(fingerprintIntervalKey);
                if (StrUtil.isNotBlank(lastFingerprintSend)) {
                    Map<String, Object> rateLimitEventData = new HashMap<>();
                    rateLimitEventData.put("fingerprint", fingerprint);
                    rateLimitEventData.put("type", "fingerprint_interval");
                    rateLimitEventData.put("limit", mailGunConfig.getFingerprintInterval());
                    TraceUtils.recordBusinessEvent("rate_limit_triggered", rateLimitEventData);
                    
                    throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "发送过于频繁，请" + mailGunConfig.getFingerprintInterval() + "秒后再试");
                }
            }
            
            // 验证邮箱每小时发送次数
            String emailHourlyKey = "email_hourly:" + email + ":" + getCurrentHour();
            String emailHourlyCount = stringRedisTemplate.opsForValue().get(emailHourlyKey);
            if (StrUtil.isNotBlank(emailHourlyCount) && 
                Integer.parseInt(emailHourlyCount) >= mailGunConfig.getEmailHourlyLimit()) {
                Map<String, Object> rateLimitEventData = new HashMap<>();
                rateLimitEventData.put("email", email);
                rateLimitEventData.put("type", "email_hourly");
                rateLimitEventData.put("current", Integer.parseInt(emailHourlyCount));
                rateLimitEventData.put("limit", mailGunConfig.getEmailHourlyLimit());
                TraceUtils.recordBusinessEvent("rate_limit_triggered", rateLimitEventData);
                
                throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "该邮箱每小时发送次数已达上限，请稍后再试");
            }
            
            // 验证设备指纹每小时发送次数
            if (StrUtil.isNotBlank(fingerprint)) {
                String fingerprintHourlyKey = "fingerprint_hourly:" + fingerprint + ":" + getCurrentHour();
                String fingerprintHourlyCount = stringRedisTemplate.opsForValue().get(fingerprintHourlyKey);
                if (StrUtil.isNotBlank(fingerprintHourlyCount) && 
                    Integer.parseInt(fingerprintHourlyCount) >= mailGunConfig.getFingerprintHourlyLimit()) {
                    Map<String, Object> rateLimitEventData = new HashMap<>();
                    rateLimitEventData.put("fingerprint", fingerprint);
                    rateLimitEventData.put("type", "fingerprint_hourly");
                    rateLimitEventData.put("current", Integer.parseInt(fingerprintHourlyCount));
                    rateLimitEventData.put("limit", mailGunConfig.getFingerprintHourlyLimit());
                    TraceUtils.recordBusinessEvent("rate_limit_triggered", rateLimitEventData);
                    
                    throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "该设备每小时发送次数已达上限，请稍后再试");
                }
            }
            
            // 验证邮箱每天发送次数
            String emailDailyKey = "email_daily:" + email + ":" + getCurrentDate();
            String emailDailyCount = stringRedisTemplate.opsForValue().get(emailDailyKey);
            if (StrUtil.isNotBlank(emailDailyCount) && 
                Integer.parseInt(emailDailyCount) >= mailGunConfig.getEmailDailyLimit()) {
                Map<String, Object> rateLimitEventData = new HashMap<>();
                rateLimitEventData.put("email", email);
                rateLimitEventData.put("type", "email_daily");
                rateLimitEventData.put("current", Integer.parseInt(emailDailyCount));
                rateLimitEventData.put("limit", mailGunConfig.getEmailDailyLimit());
                TraceUtils.recordBusinessEvent("rate_limit_triggered", rateLimitEventData);
                
                throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "该邮箱今日发送次数已达上限，请明天再试");
            }
            
            // 验证设备指纹每天发送次数
            if (StrUtil.isNotBlank(fingerprint)) {
                String fingerprintDailyKey = "fingerprint_daily:" + fingerprint + ":" + getCurrentDate();
                String fingerprintDailyCount = stringRedisTemplate.opsForValue().get(fingerprintDailyKey);
                if (StrUtil.isNotBlank(fingerprintDailyCount) && 
                    Integer.parseInt(fingerprintDailyCount) >= mailGunConfig.getFingerprintDailyLimit()) {
                    Map<String, Object> rateLimitEventData = new HashMap<>();
                    rateLimitEventData.put("fingerprint", fingerprint);
                    rateLimitEventData.put("type", "fingerprint_daily");
                    rateLimitEventData.put("current", Integer.parseInt(fingerprintDailyCount));
                    rateLimitEventData.put("limit", mailGunConfig.getFingerprintDailyLimit());
                    TraceUtils.recordBusinessEvent("rate_limit_triggered", rateLimitEventData);
                    
                    throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "该设备今日发送次数已达上限，请明天再试");
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录验证通过事件
            Map<String, Object> passEventData = new HashMap<>();
            passEventData.put("email", email);
            passEventData.put("fingerprint", fingerprint);
            passEventData.put("duration", duration);
            TraceUtils.recordBusinessEvent("rate_limit_validation_pass", passEventData);
            
        } catch (RateLimitException e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("rate_limit_validation_rejected", "Rate limit rejected after " + duration + "ms", e);
            throw e; // 重新抛出限流异常
        }
    }
    
    /**
     * 记录发送频率
     */
    private void recordSendingLimit(String email, String fingerprint) {
        // 记录邮箱发送间隔
        String emailIntervalKey = "email_interval:" + email;
        stringRedisTemplate.opsForValue().set(emailIntervalKey, "1", 
            mailGunConfig.getEmailInterval(), TimeUnit.SECONDS);
        
        // 记录设备指纹发送间隔
        if (StrUtil.isNotBlank(fingerprint)) {
            String fingerprintIntervalKey = "fingerprint_interval:" + fingerprint;
            stringRedisTemplate.opsForValue().set(fingerprintIntervalKey, "1", 
                mailGunConfig.getFingerprintInterval(), TimeUnit.SECONDS);
        }
        
        // 记录邮箱每小时发送次数
        String emailHourlyKey = "email_hourly:" + email + ":" + getCurrentHour();
        stringRedisTemplate.opsForValue().increment(emailHourlyKey);
        stringRedisTemplate.expire(emailHourlyKey, 3600, TimeUnit.SECONDS);
        
        // 记录设备指纹每小时发送次数
        if (StrUtil.isNotBlank(fingerprint)) {
            String fingerprintHourlyKey = "fingerprint_hourly:" + fingerprint + ":" + getCurrentHour();
            stringRedisTemplate.opsForValue().increment(fingerprintHourlyKey);
            stringRedisTemplate.expire(fingerprintHourlyKey, 3600, TimeUnit.SECONDS);
        }
        
        // 记录邮箱每天发送次数
        String emailDailyKey = "email_daily:" + email + ":" + getCurrentDate();
        stringRedisTemplate.opsForValue().increment(emailDailyKey);
        stringRedisTemplate.expire(emailDailyKey, 86400, TimeUnit.SECONDS);
        
        // 记录设备指纹每天发送次数
        if (StrUtil.isNotBlank(fingerprint)) {
            String fingerprintDailyKey = "fingerprint_daily:" + fingerprint + ":" + getCurrentDate();
            stringRedisTemplate.opsForValue().increment(fingerprintDailyKey);
            stringRedisTemplate.expire(fingerprintDailyKey, 86400, TimeUnit.SECONDS);
        }
    }
    
    /**
     * 获取当前小时（格式：yyyyMMddHH）
     */
    private String getCurrentHour() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHH"));
    }
    
    /**
     * 获取当前日期（格式：yyyyMMdd）
     */
    private String getCurrentDate() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    /**
     * 验证邮箱验证码
     */
    public boolean verifyCode(String email, String code) {
        long startTime = System.currentTimeMillis();
        
        // 记录验证开始事件
        Map<String, Object> startEventData = new HashMap<>();
        startEventData.put("email", email);
        startEventData.put("inputCode", code);
        startEventData.put("deviceInfo", TraceContext.getDeviceSummary());
        TraceUtils.recordBusinessEvent("verification_code_verify_start", startEventData);
        
        try {
            String key = "verification_code:" + email;
            String storedCode = stringRedisTemplate.opsForValue().get(key);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (StrUtil.isBlank(storedCode)) {
                // 记录验证码过期事件
                Map<String, Object> expiredEventData = new HashMap<>();
                expiredEventData.put("email", email);
                expiredEventData.put("inputCode", code);
                expiredEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("verification_code_expired", expiredEventData);
                
                log.warn("验证码验证失败 - 邮箱: {}, 原因: 验证码不存在或已过期, 耗时: {}ms", email, duration);
                throw new ValidationException(ResponseCode.VERIFICATION_CODE_EXPIRED, "验证码不存在或已过期");
            }
            
            if (!code.equals(storedCode)) {
                // 记录验证码错误事件
                Map<String, Object> invalidEventData = new HashMap<>();
                invalidEventData.put("email", email);
                invalidEventData.put("inputCode", code);
                invalidEventData.put("storedCode", storedCode);
                invalidEventData.put("duration", duration);
                
                TraceUtils.recordBusinessEvent("verification_code_invalid", invalidEventData);
                
                log.warn("验证码验证失败 - 邮箱: {}, 输入验证码: {}, 存储验证码: {}, 耗时: {}ms", 
                    email, code, storedCode, duration);
                throw new ValidationException(ResponseCode.VERIFICATION_CODE_INVALID, "验证码错误");
            }
            
            // 验证成功，删除验证码
            stringRedisTemplate.delete(key);
            
            // 记录验证成功事件
            Map<String, Object> successEventData = new HashMap<>();
            successEventData.put("email", email);
            successEventData.put("code", code);
            successEventData.put("duration", duration);
            
            TraceUtils.recordBusinessEvent("verification_code_verify_success", successEventData);
            
            log.info("验证码验证成功 - 邮箱: {}, 验证码: {}, 耗时: {}ms", email, code, duration);
            return true;
            
        } catch (ValidationException e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("verification_code_verify_validation_error", e.getMessage(), e);
            throw e; // 重新抛出自定义异常
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TraceUtils.recordError("verification_code_verify_unexpected_error", e.getMessage(), e);
            
            log.error("验证码验证异常 - 邮箱: {}, 验证码: {}, 耗时: {}ms", email, code, duration, e);
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "验证码验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取邮件配置信息（用于前端展示）
     */
    public Map<String, Object> getEmailConfigInfo() {
        Map<String, Object> config = new HashMap<>();
        
        config.put("emailInterval", mailGunConfig.getEmailInterval());
        config.put("fingerprintInterval", mailGunConfig.getFingerprintInterval());
        config.put("emailHourlyLimit", mailGunConfig.getEmailHourlyLimit());
        config.put("fingerprintHourlyLimit", mailGunConfig.getFingerprintHourlyLimit());
        config.put("emailDailyLimit", mailGunConfig.getEmailDailyLimit());
        config.put("fingerprintDailyLimit", mailGunConfig.getFingerprintDailyLimit());
        config.put("verificationExpireMinutes", mailGunConfig.getVerificationExpireMinutes());
        config.put("fromName", mailGunConfig.getFromName());
        
        // 添加友好的描述信息
        config.put("rules", buildLimitRulesDescription());
        
        return config;
    }
    
    /**
     * 构建限制规则描述信息
     */
    private Map<String, String> buildLimitRulesDescription() {
        Map<String, String> rules = new HashMap<>();
        
        rules.put("emailInterval", "同一邮箱发送间隔：" + mailGunConfig.getEmailInterval() + "秒");
        rules.put("fingerprintInterval", "同一设备发送间隔：" + mailGunConfig.getFingerprintInterval() + "秒");
        rules.put("emailHourlyLimit", "邮箱每小时限制：" + mailGunConfig.getEmailHourlyLimit() + "次");
        rules.put("fingerprintHourlyLimit", "设备每小时限制：" + mailGunConfig.getFingerprintHourlyLimit() + "次");
        rules.put("emailDailyLimit", "邮箱每天限制：" + mailGunConfig.getEmailDailyLimit() + "次");
        rules.put("fingerprintDailyLimit", "设备每天限制：" + mailGunConfig.getFingerprintDailyLimit() + "次");
        rules.put("codeExpire", "验证码有效期：" + mailGunConfig.getVerificationExpireMinutes() + "分钟");
        
        return rules;
    }
} 