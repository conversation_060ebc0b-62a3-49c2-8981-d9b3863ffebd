package com.jiashu.labiai.service;

import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.DiscountCodeValidationDTO;

import java.math.BigDecimal;

/**
 * 优惠码服务接口
 */
public interface IDiscountCodeService {
    
    /**
     * 验证优惠码
     *
     * @param discountCode 优惠码
     * @param packageId 套餐ID
     * @param amount 订单金额
     * @param userId 用户ID
     * @return 验证结果
     */
    ApiResponse<DiscountCodeValidationDTO> validateDiscountCode(String discountCode, Long packageId, 
                                                               BigDecimal amount, Long userId);
    
    /**
     * 取消优惠码使用（订单超时时调用）
     *
     * @param orderId 订单ID
     */
    void cancelDiscountCodeUsage(Long orderId);
    
    /**
     * 获取优惠码使用信息
     *
     * @param discountCodeId 优惠码ID
     * @param orderId 订单ID
     * @return 优惠码使用信息
     */
    ApiResponse<java.util.Map<String, Object>> getDiscountCodeUsageInfo(Long discountCodeId, Long orderId);
    
    /**
     * 验证优惠码用于订单创建（不消费使用次数）
     *
     * @param discountCode 优惠码
     * @param packageId 套餐ID
     * @param amount 订单金额
     * @param userId 用户ID
     * @return 验证结果
     */
    ApiResponse<DiscountCodeValidationDTO> validateDiscountCodeForOrder(String discountCode, Long packageId, 
                                                                       BigDecimal amount, Long userId);
    
    /**
     * 原子性消费优惠码并创建使用记录
     *
     * @param discountCodeId 优惠码ID
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param discountAmount 优惠金额
     * @return 是否消费成功
     */
    boolean atomicUseDiscountCode(Long discountCodeId, Long userId, Long orderId, BigDecimal discountAmount);
} 