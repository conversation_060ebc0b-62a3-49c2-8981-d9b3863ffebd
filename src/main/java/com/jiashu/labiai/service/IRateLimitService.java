package com.jiashu.labiai.service;

/**
 * 滑动窗口限流服务接口
 * 
 * <AUTHOR>
 */
public interface IRateLimitService {
    
    /**
     * 检查是否超过限流阈值
     * 
     * @param key 限流键
     * @param windowSeconds 时间窗口大小(秒)
     * @param maxRequests 最大请求数
     * @return 是否允许请求
     */
    boolean isAllowed(String key, int windowSeconds, int maxRequests);
    
    /**
     * 获取下次允许请求的时间戳
     * 
     * @param key 限流键
     * @param windowSeconds 时间窗口大小(秒) 
     * @return 时间戳，如果当前可以请求则返回null
     */
    Long getNextAllowedTime(String key, int windowSeconds);
    
    /**
     * 清除限流记录
     * 
     * @param key 限流键
     */
    void clearLimit(String key);
    
    /**
     * 获取当前时间窗口内的请求数
     * 
     * @param key 限流键
     * @param windowSeconds 时间窗口大小(秒)
     * @return 请求数
     */
    int getCurrentCount(String key, int windowSeconds);
    
    /**
     * 检查用户登录频率
     * 
     * @param userId 用户ID
     * @return 是否允许登录
     */
    boolean checkUserLoginRate(Long userId);
    
    /**
     * 检查IP登录频率
     * 
     * @param ip IP地址
     * @return 是否允许登录
     */
    boolean checkIpLoginRate(String ip);
    
    /**
     * 检查设备登录频率
     * 
     * @param deviceId 设备ID
     * @return 是否允许登录
     */
    boolean checkDeviceLoginRate(String deviceId);
    
    /**
     * 增加用户登录失败次数
     * 
     * @param userId 用户ID
     * @return 当前失败次数
     */
    int incrementUserLoginFailure(Long userId);
    
    /**
     * 增加IP登录失败次数
     * 
     * @param ip IP地址
     * @return 当前失败次数
     */
    int incrementIpLoginFailure(String ip);
    
    /**
     * 增加设备登录失败次数
     * 
     * @param deviceId 设备ID
     * @return 当前失败次数
     */
    int incrementDeviceLoginFailure(String deviceId);
    
    /**
     * 重置用户登录失败次数
     * 
     * @param userId 用户ID
     */
    void resetUserLoginFailure(Long userId);
    
    /**
     * 重置IP登录失败次数
     * 
     * @param ip IP地址
     */
    void resetIpLoginFailure(String ip);
    
    /**
     * 重置设备登录失败次数
     * 
     * @param deviceId 设备ID
     */
    void resetDeviceLoginFailure(String deviceId);
    
    /**
     * 获取用户登录失败次数
     * 
     * @param userId 用户ID
     * @return 失败次数
     */
    int getUserLoginFailureCount(Long userId);
    
    /**
     * 获取IP登录失败次数
     * 
     * @param ip IP地址
     * @return 失败次数
     */
    int getIpLoginFailureCount(String ip);
    
    /**
     * 获取设备登录失败次数
     * 
     * @param deviceId 设备ID
     * @return 失败次数
     */
    int getDeviceLoginFailureCount(String deviceId);
    
    /**
     * 检查用户是否达到最大失败次数
     * 
     * @param userId 用户ID
     * @return 是否达到最大失败次数
     */
    boolean isUserLoginLocked(Long userId);
} 