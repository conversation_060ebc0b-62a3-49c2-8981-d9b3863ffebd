package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.DiscountCodeValidationDTO;
import com.jiashu.labiai.entity.DiscountCode;
import com.jiashu.labiai.entity.DiscountCodeUsage;
import com.jiashu.labiai.exception.DiscountCodeException;
import com.jiashu.labiai.mapper.DiscountCodeMapper;
import com.jiashu.labiai.mapper.DiscountCodeUsageMapper;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠码服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DiscountCodeService extends ServiceImpl<DiscountCodeMapper, DiscountCode> implements IDiscountCodeService {

    private final DiscountCodeUsageMapper discountCodeUsageMapper;

    @Override
    public ApiResponse<DiscountCodeValidationDTO> validateDiscountCode(String discountCode, Long packageId, 
                                                                      BigDecimal amount, Long userId) {
        return TraceUtils.executeWithTrace("validateDiscountCode", () -> {
            try {
                Map<String, Object> startDetails = new HashMap<>();
                startDetails.put("discountCode", discountCode);
                startDetails.put("packageId", packageId);
                startDetails.put("amount", amount);
                startDetails.put("userId", userId);
                TraceUtils.recordBusinessEvent("DISCOUNT_CODE_VALIDATE_START", startDetails);

                // 1. 查询优惠码
                DiscountCode code = getOne(
                        new QueryWrapper<DiscountCode>()
                        .eq("code", discountCode)
                        .eq("status", 1)
                );
                
                if (code == null) {
                    Map<String, Object> errorDetails = new HashMap<>();
                    errorDetails.put("discountCode", discountCode);
                    TraceUtils.recordError("DISCOUNT_CODE_NOT_FOUND", errorDetails);
                    throw DiscountCodeException.codeNotFound(discountCode);
                }

                // 2. 验证时间有效性
                LocalDateTime now = LocalDateTime.now();
                if (code.getStartTime() != null && now.isBefore(code.getStartTime())) {
                    throw DiscountCodeException.codeNotFound(discountCode); // 未到开始时间
                }
                if (code.getEndTime() != null && now.isAfter(code.getEndTime())) {
                    throw DiscountCodeException.codeExpired(discountCode);
                }

                // 3. 验证使用次数 - 这里只做基础验证，真正的原子性检查在使用时进行
                if (code.getUsedCount() >= code.getMaxUsage()) {
                    throw DiscountCodeException.codeUsedUp(discountCode);
                }

                // 4. 验证最小金额
                if (amount.compareTo(code.getMinAmount()) < 0) {
                    throw DiscountCodeException.minimumAmountNotMet(discountCode, code.getMinAmount());
                }

                // 5. 验证适用套餐
                if (code.getApplicablePackages() != null && 
                    !code.getApplicablePackages().isEmpty() && 
                    !code.getApplicablePackages().contains(packageId)) {
                    throw DiscountCodeException.notApplicableToPackage(discountCode, packageId);
                }

                // 6. 验证用户使用限制（一码一用时）
                if ("SINGLE".equals(code.getUsageType())) {
                    long userUsageCount = discountCodeUsageMapper.selectCount(
                        new QueryWrapper<DiscountCodeUsage>()
                            .eq("discount_code_id", code.getId())
                            .eq("user_id", userId)
                            .eq("status", "USED")
                    );
                    if (userUsageCount > 0) {
                        throw DiscountCodeException.codeUsedUp(discountCode);
                    }
                }

                // 7. 计算优惠金额
                BigDecimal discountAmount = calculateDiscountAmount(code, amount);
                BigDecimal finalAmount = amount.subtract(discountAmount);

                // 8. 构建返回结果
                DiscountCodeValidationDTO result = new DiscountCodeValidationDTO()
                    .setDiscountCodeId(code.getId())
                    .setDiscountCode(code.getCode())
                    .setValid(true)
                    .setDiscountType(code.getType())
                    .setDiscountValue(code.getValue())
                    .setDiscountAmount(discountAmount)
                    .setFinalAmount(finalAmount);

                Map<String, Object> successDetails = new HashMap<>();
                successDetails.put("discountCodeId", code.getId());
                successDetails.put("discountAmount", discountAmount);
                TraceUtils.recordBusinessEvent("DISCOUNT_CODE_VALIDATE_SUCCESS", successDetails);

                return ApiResponse.success(result, "优惠码验证成功");

            } catch (DiscountCodeException e) {
                throw e;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCode", discountCode);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("DISCOUNT_CODE_VALIDATE_FAILED", errorDetails);
                log.error("验证优惠码失败", e);
                throw new RuntimeException("验证优惠码失败", e);
            }
        });
    }

    /**
     * 计算优惠金额
     */
    private BigDecimal calculateDiscountAmount(DiscountCode code, BigDecimal originalAmount) {
        BigDecimal discountAmount;
        
        if ("FIXED".equals(code.getType())) {
            // 固定金额优惠
            discountAmount = code.getValue();
        } else if ("PERCENTAGE".equals(code.getType())) {
            // 百分比优惠
            discountAmount = originalAmount.multiply(code.getValue())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            
            // 检查最大优惠金额限制
            if (code.getMaxDiscount() != null && 
                discountAmount.compareTo(code.getMaxDiscount()) > 0) {
                discountAmount = code.getMaxDiscount();
            }
        } else {
            discountAmount = BigDecimal.ZERO;
        }
        
        // 优惠金额不能超过原始金额
        if (discountAmount.compareTo(originalAmount) > 0) {
            discountAmount = originalAmount;
        }
        
        return discountAmount;
    }

    /**
     * 取消优惠码使用（订单超时时调用）
     */
    @Override
    @Transactional
    public void cancelDiscountCodeUsage(Long orderId) {
        TraceUtils.executeWithTrace("cancelDiscountCodeUsage", () -> {
            try {
                // 1. 查找该订单的优惠码使用记录
                List<DiscountCodeUsage> usageList = discountCodeUsageMapper.selectList(
                    new QueryWrapper<DiscountCodeUsage>()
                        .eq("order_id", orderId)
                        .eq("status", "USED")
                );

                for (DiscountCodeUsage usage : usageList) {
                    // 2. 更新使用记录状态为已取消
                    usage.setStatus("CANCELLED");
                    discountCodeUsageMapper.updateById(usage);

                    // 3. 原子性恢复优惠码使用次数
                    // SQL: UPDATE discount_codes SET used_count = used_count - 1 
                    //      WHERE id = ? AND used_count > 0
                    int decrementResult = baseMapper.decrementUsedCount(usage.getDiscountCodeId());
                    
                    if (decrementResult > 0) {
                        Map<String, Object> cancelDetails = new HashMap<>();
                        cancelDetails.put("discountCodeId", usage.getDiscountCodeId());
                        cancelDetails.put("orderId", orderId);
                        cancelDetails.put("userId", usage.getUserId());
                        TraceUtils.recordBusinessEvent("DISCOUNT_CODE_USAGE_CANCELLED", cancelDetails);
                    } else {
                        log.warn("恢复优惠码使用次数失败，可能已经为0: discountCodeId={}", usage.getDiscountCodeId());
                    }
                }

                return null;
            } catch (Exception e) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("orderId", orderId);
                errorDetails.put("error", e.getMessage());
                TraceUtils.recordError("CANCEL_DISCOUNT_CODE_USAGE_FAILED", errorDetails);
                log.error("取消优惠码使用失败", e);
                throw new RuntimeException("取消优惠码使用失败", e);
            }
        });
    }

    @Override
    public ApiResponse<Map<String, Object>> getDiscountCodeUsageInfo(Long discountCodeId, Long orderId) {
        try {
            Map<String, Object> startDetails = new HashMap<>();
            startDetails.put("discountCodeId", discountCodeId);
            startDetails.put("orderId", orderId);
            TraceUtils.recordBusinessEvent("GET_DISCOUNT_CODE_USAGE_INFO_START", startDetails);

            // 1. 获取优惠码信息
            DiscountCode code = getById(discountCodeId);
            if (code == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCodeId", discountCodeId);
                TraceUtils.recordError("DISCOUNT_CODE_NOT_FOUND", errorDetails);
                return ApiResponse.error(404, "优惠码不存在");
            }

            // 2. 获取使用记录
            DiscountCodeUsage usage = discountCodeUsageMapper.selectOne(
                new QueryWrapper<DiscountCodeUsage>()
                    .eq("discount_code_id", discountCodeId)
                    .eq("order_id", orderId)
                    .orderByDesc("id")
                    .last("LIMIT 1")
            );

            if (usage == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCodeId", discountCodeId);
                errorDetails.put("orderId", orderId);
                TraceUtils.recordError("DISCOUNT_CODE_USAGE_NOT_FOUND", errorDetails);
                return ApiResponse.error(404, "优惠码使用记录不存在");
            }

            // 3. 构建返回信息
            Map<String, Object> usageInfo = new HashMap<>();
            usageInfo.put("discountCodeId", code.getId());
            usageInfo.put("discountCode", code.getCode());
            usageInfo.put("discountName", code.getName());
            usageInfo.put("discountType", code.getType());
            usageInfo.put("discountValue", code.getValue());
            usageInfo.put("discountAmount", usage.getDiscountAmount());
            usageInfo.put("usageStatus", usage.getStatus());
            usageInfo.put("usedAt", LocalDateTime.now()); // 使用当前时间作为占位符
            usageInfo.put("orderId", orderId);
            usageInfo.put("userId", usage.getUserId());

            // 4. 添加优惠码详细信息（只添加存在的字段）
            usageInfo.put("codeUsedCount", code.getUsedCount());

            Map<String, Object> successDetails = new HashMap<>();
            successDetails.put("discountCodeId", discountCodeId);
            successDetails.put("orderId", orderId);
            successDetails.put("discountAmount", usage.getDiscountAmount());
            TraceUtils.recordBusinessEvent("GET_DISCOUNT_CODE_USAGE_INFO_SUCCESS", successDetails);

            return ApiResponse.success(usageInfo, "获取优惠码使用信息成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("discountCodeId", discountCodeId);
            errorDetails.put("orderId", orderId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("GET_DISCOUNT_CODE_USAGE_INFO_FAILED", errorDetails);
            log.error("获取优惠码使用信息失败", e);
            return ApiResponse.error(500, "获取优惠码使用信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证优惠码用于订单创建（不消费使用次数）
     */
    @Override
    public ApiResponse<DiscountCodeValidationDTO> validateDiscountCodeForOrder(String discountCode, Long packageId, 
                                                                              BigDecimal amount, Long userId) {
        try {
            Map<String, Object> startDetails = new HashMap<>();
            startDetails.put("discountCode", discountCode);
            startDetails.put("packageId", packageId);
            startDetails.put("amount", amount);
            startDetails.put("userId", userId);
            TraceUtils.recordBusinessEvent("DISCOUNT_CODE_VALIDATE_FOR_ORDER_START", startDetails);

            // 1. 查询优惠码
            DiscountCode code = getOne(
                    new QueryWrapper<DiscountCode>()
                    .eq("code", discountCode)
                    .eq("status", 1)
            );
            
            if (code == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCode", discountCode);
                TraceUtils.recordError("DISCOUNT_CODE_NOT_FOUND", errorDetails);
                return ApiResponse.error(404, "优惠码不存在或已失效");
            }

            // 2. 验证时间有效性
            LocalDateTime now = LocalDateTime.now();
            if (code.getStartTime() != null && now.isBefore(code.getStartTime())) {
                return ApiResponse.error(400, "优惠码尚未生效");
            }
            if (code.getEndTime() != null && now.isAfter(code.getEndTime())) {
                return ApiResponse.error(400, "优惠码已过期");
            }

            // 3. 验证最小金额
            if (amount.compareTo(code.getMinAmount()) < 0) {
                return ApiResponse.error(400, String.format("订单金额不满足优惠码使用条件，最低需要%.2f元", code.getMinAmount()));
            }

            // 4. 验证适用套餐
            if (code.getApplicablePackages() != null && 
                !code.getApplicablePackages().isEmpty() && 
                !code.getApplicablePackages().contains(packageId)) {
                return ApiResponse.error(400, "该优惠码不适用于当前套餐");
            }

            // 5. 验证用户使用限制（一码一用时）
            if ("SINGLE".equals(code.getUsageType())) {
                long userUsageCount = discountCodeUsageMapper.selectCount(
                    new QueryWrapper<DiscountCodeUsage>()
                        .eq("discount_code_id", code.getId())
                        .eq("user_id", userId)
                        .eq("status", "USED")
                );
                if (userUsageCount > 0) {
                    return ApiResponse.error(400, "您已使用过此优惠码");
                }
            }

            // 6. 检查使用次数（不消费，只检查）
            if (code.getUsedCount() >= code.getMaxUsage()) {
                return ApiResponse.error(400, "优惠码已用完");
            }

            // 7. 计算优惠金额
            BigDecimal discountAmount = calculateDiscountAmount(code, amount);
            BigDecimal finalAmount = amount.subtract(discountAmount);

            // 8. 构建返回结果
            DiscountCodeValidationDTO result = new DiscountCodeValidationDTO()
                .setDiscountCodeId(code.getId())
                .setDiscountCode(code.getCode())
                .setValid(true)
                .setDiscountType(code.getType())
                .setDiscountValue(code.getValue())
                .setDiscountAmount(discountAmount)
                .setOriginalAmount(amount)
                .setFinalAmount(finalAmount);

            Map<String, Object> successDetails = new HashMap<>();
            successDetails.put("discountCodeId", code.getId());
            successDetails.put("discountAmount", discountAmount);
            TraceUtils.recordBusinessEvent("DISCOUNT_CODE_VALIDATE_FOR_ORDER_SUCCESS", successDetails);

            return ApiResponse.success(result, "优惠码验证成功");

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("discountCode", discountCode);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("DISCOUNT_CODE_VALIDATE_FOR_ORDER_FAILED", errorDetails);
            log.error("验证优惠码失败", e);
            return ApiResponse.error(500, "验证优惠码失败: " + e.getMessage());
        }
    }

    /**
     * 原子性消费优惠码并创建使用记录
     */
    @Override
    @Transactional
    public boolean atomicUseDiscountCode(Long discountCodeId, Long userId, Long orderId, BigDecimal discountAmount) {
        try {
            Map<String, Object> startDetails = new HashMap<>();
            startDetails.put("discountCodeId", discountCodeId);
            startDetails.put("userId", userId);
            startDetails.put("orderId", orderId);
            startDetails.put("discountAmount", discountAmount);
            TraceUtils.recordBusinessEvent("ATOMIC_USE_DISCOUNT_CODE_START", startDetails);

            // 1. 获取优惠码信息
            DiscountCode code = getById(discountCodeId);
            if (code == null) {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCodeId", discountCodeId);
                TraceUtils.recordError("DISCOUNT_CODE_NOT_FOUND", errorDetails);
                return false;
            }

            // 2. 原子性检查并使用优惠码（防超售的关键）
            int updateResult = baseMapper.incrementUsedCountWithCheck(code.getId(), code.getMaxUsage());
            
            if (updateResult == 0) {
                // 更新失败，说明优惠码已用完
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("discountCodeId", code.getId());
                errorDetails.put("maxUsage", code.getMaxUsage());
                TraceUtils.recordError("DISCOUNT_CODE_OVERSOLD_PREVENTED", errorDetails);
                return false;
            }

            // 3. 创建使用记录
            DiscountCodeUsage usage = new DiscountCodeUsage();
            usage.setDiscountCodeId(code.getId());
            usage.setUserId(userId);
            usage.setOrderId(orderId);
            usage.setDiscountAmount(discountAmount);
            usage.setStatus("USED");
            discountCodeUsageMapper.insert(usage);

            Map<String, Object> successDetails = new HashMap<>();
            successDetails.put("discountCodeId", code.getId());
            successDetails.put("orderId", orderId);
            successDetails.put("discountAmount", discountAmount);
            TraceUtils.recordBusinessEvent("ATOMIC_USE_DISCOUNT_CODE_SUCCESS", successDetails);

            return true;

        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("discountCodeId", discountCodeId);
            errorDetails.put("orderId", orderId);
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("ATOMIC_USE_DISCOUNT_CODE_FAILED", errorDetails);
            log.error("原子性使用优惠码失败", e);
            return false;
        }
    }
} 