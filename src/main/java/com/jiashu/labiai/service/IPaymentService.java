package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.PaymentCreateResponseDTO;
import com.jiashu.labiai.dto.PaymentMethodDTO;
import com.jiashu.labiai.dto.PaymentStatusDTO;
import com.jiashu.labiai.entity.Order;
import com.jiashu.labiai.entity.Payment;
import com.jiashu.labiai.entity.PaymentMethod;

import java.math.BigDecimal;

/**
 * 支付服务接口
 */
public interface IPaymentService extends IService<Payment> {
    
    /**
     * 创建支付订单
     *
     * @param orderId 订单ID
     * @param paymentMethodCode 支付方式代码
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 支付创建结果
     */
    ApiResponse<PaymentCreateResponseDTO> createPayment(Long orderId, String paymentMethodCode, 
                                                       String clientIp, String userAgent);
    
    /**
     * 处理支付回调
     *
     * @param provider 支付提供商
     * @param callbackData 回调数据
     * @return 处理结果
     */
    ApiResponse<String> handlePaymentCallback(String provider, String callbackData);
    
    /**
     * 查询支付状态
     *
     * @param paymentNo 支付单号
     * @return 支付状态
     */
    ApiResponse<PaymentStatusDTO> queryPaymentStatus(String paymentNo);
    
    /**
     * 获取可用支付方式
     *
     * @param amount 支付金额
     * @return 支付方式列表
     */
    ApiResponse<java.util.List<PaymentMethodDTO>> getAvailablePaymentMethods(BigDecimal amount);
    
    /**
     * 根据支付单号查询支付记录
     *
     * @param paymentNo 支付单号
     * @return 支付记录
     */
    Payment findByPaymentNo(String paymentNo);
    
    /**
     * 根据商户订单号查询支付记录
     *
     * @param tradeOrderId 商户订单号
     * @return 支付记录
     */
    Payment findByTradeOrderId(String tradeOrderId);
    
    /**
     * 处理支付成功后的订阅业务
     *
     * @param order 订单
     */
    void processSubscription(Order order);
    
    /**
     * 记录优惠码使用
     *
     * @param order 订单
     */
//    void recordDiscountCodeUsage(Order order);
//
    /**
     * 发送支付成功通知
     *
     * @param order 订单
     */
    void sendPaymentSuccessNotification(Order order);
    
    /**
     * 更新支付记录
     *
     * @param payment 支付记录
     */
    void updatePayment(Payment payment);
    
    /**
     * 更新订单状态
     *
     * @param order 订单
     */
    void updateOrder(Order order);
    
    /**
     * 验证支付金额
     *
     * @param paymentMethod 支付方式
     * @param amount 金额
     * @return 是否有效
     */
    boolean validatePaymentAmount(PaymentMethod paymentMethod, BigDecimal amount);
    
    /**
     * 根据订单ID查询最新支付状态
     *
     * @param orderId 订单ID
     * @return 支付状态
     */
    ApiResponse<PaymentStatusDTO> queryPaymentStatusByOrderId(Long orderId);

    /**
     * 根据订单ID获取最新支付记录
     *
     * @param orderId 订单ID
     * @return 最新支付记录
     */
    Payment findLatestPaymentByOrderId(Long orderId);

    /**
     * 根据订单ID获取成功的支付记录
     *
     * @param orderId 订单ID
     * @return 成功的支付记录
     */
    Payment findSuccessPaymentByOrderId(Long orderId);
} 