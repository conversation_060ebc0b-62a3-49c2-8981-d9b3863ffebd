package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiashu.labiai.entity.SecurityEvents;
import com.jiashu.labiai.dto.response.SecurityEventResponse;
import com.jiashu.labiai.enums.SecurityEventType;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
public interface ISecurityEventsService extends IService<SecurityEvents> {

    /**
     * 记录安全事件 (使用枚举类型)
     *
     * @param eventType    安全事件类型枚举
     * @param title        事件标题
     * @param description  事件描述
     * @param userId       用户ID
     * @param deviceId     设备ID
     * @param sessionToken 会话Token
     * @param ipAddress    IP地址
     * @param loginMethod  登录方式
     * @param eventData    事件详细数据
     * @return 安全事件记录
     */
    SecurityEvents recordSecurityEvent(SecurityEventType eventType, String title,
                                       String description, Long userId, String deviceId,
                                       String sessionToken, String ipAddress, String loginMethod,
                                       Map<String, Object> eventData);

    /**
     * 记录安全事件 (字符串类型，向后兼容)
     *
     * @param eventType    事件类型
     * @param eventLevel   事件级别 (1:信息 2:警告 3:危险 4:严重)
     * @param title        事件标题
     * @param description  事件描述
     * @param userId       用户ID
     * @param deviceId     设备ID
     * @param sessionToken 会话Token
     * @param ipAddress    IP地址
     * @param loginMethod  登录方式
     * @param eventData    事件详细数据
     * @return 安全事件记录
     */
    SecurityEvents recordSecurityEvent(String eventType, Integer eventLevel, String title,
                                       String description, Long userId, String deviceId,
                                       String sessionToken, String ipAddress, String loginMethod,
                                       Map<String, Object> eventData);

    /**
     * 记录设备异常事件
     *
     * @param userId       用户ID
     * @param deviceId     设备ID
     * @param sessionToken 会话Token
     * @param ipAddress    IP地址
     * @param anomalyType  异常类型
     * @param description  异常描述
     * @param eventData    事件数据
     */
    void recordDeviceAnomalyEvent(Long userId, String deviceId, String sessionToken,
                                  String ipAddress, String anomalyType, String description,
                                  Map<String, Object> eventData);

    /**
     * 记录登录异常事件
     *
     * @param userId        用户ID
     * @param deviceId      设备ID
     * @param ipAddress     IP地址
     * @param loginMethod   登录方式
     * @param failureReason 失败原因
     * @param eventData     事件数据
     */
    void recordLoginAnomalyEvent(Long userId, String deviceId, String ipAddress,
                                 String loginMethod, String failureReason,
                                 Map<String, Object> eventData);

    /**
     * 记录设备指纹变化事件
     *
     * @param userId        用户ID
     * @param deviceId      设备ID
     * @param oldDeviceHash 旧设备指纹
     * @param newDeviceHash 新设备指纹
     * @param ipAddress     IP地址
     * @param eventData     事件数据
     */
    void recordDeviceFingerprintChangeEvent(Long userId, String deviceId, String oldDeviceHash,
                                            String newDeviceHash, String ipAddress,
                                            Map<String, Object> eventData);

    /**
     * 获取用户安全事件列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 安全事件列表
     */
    List<SecurityEventResponse> getUserSecurityEvents(Long userId, Integer limit);

    /**
     * 获取设备相关安全事件
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @param limit    限制数量
     * @return 安全事件列表
     */
    List<SecurityEventResponse> getDeviceSecurityEvents(Long userId, String deviceId, Integer limit);

    /**
     * 获取高风险事件列表
     *
     * @param eventLevel 事件级别阈值
     * @param limit      限制数量
     * @return 高风险事件列表
     */
    List<SecurityEventResponse> getHighRiskEvents(Integer eventLevel, Integer limit);

    /**
     * 处理安全事件
     *
     * @param eventId     事件ID
     * @param handledBy   处理人ID
     * @param handleNotes 处理备注
     * @return 是否处理成功
     */
    boolean handleSecurityEvent(Long eventId, Long handledBy, String handleNotes);

    /**
     * 批量处理安全事件
     *
     * @param eventIds    事件ID列表
     * @param handledBy   处理人ID
     * @param handleNotes 处理备注
     * @return 处理成功的数量
     */
    int batchHandleSecurityEvents(List<Long> eventIds, Long handledBy, String handleNotes);

    /**
     * 统计用户风险评分
     *
     * @param userId 用户ID
     * @param days   统计天数
     * @return 风险评分
     */
    int calculateUserRiskScore(Long userId, Integer days);

    /**
     * 统计设备风险评分
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @param days     统计天数
     * @return 风险评分
     */
    int calculateDeviceRiskScore(Long userId, String deviceId, Integer days);

    /**
     * 检查是否需要触发安全策略
     *
     * @param userId     用户ID
     * @param eventType  事件类型枚举
     * @param timeWindow 时间窗口(分钟)
     * @return 是否需要触发策略
     */
    boolean shouldTriggerSecurityPolicy(Long userId, SecurityEventType eventType, Integer timeWindow);

    /**
     * 检查是否需要触发安全策略 (字符串类型，向后兼容)
     *
     * @param userId     用户ID
     * @param eventType  事件类型
     * @param timeWindow 时间窗口(分钟)
     * @return 是否需要触发策略
     */
    boolean shouldTriggerSecurityPolicy(Long userId, String eventType, Integer timeWindow);
}
