package com.jiashu.labiai.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.jiashu.labiai.constants.RedisKeyConstants;
import com.jiashu.labiai.dto.response.auth.SessionInitResponse;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.exception.RateLimitException;
import com.jiashu.labiai.exception.SecurityException;
import com.jiashu.labiai.exception.ValidationException;
import com.jiashu.labiai.service.IRateLimitService;
import com.jiashu.labiai.service.SessionService;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 会话管理服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SessionServiceImpl implements SessionService {
    
    private final JwtUtil jwtUtil;
    private final IRateLimitService rateLimitService;
    private final RedisTemplate<String, String> redisTemplate;
    
    // 使用统一的Redis key常量
    private static final Pattern VISITOR_ID_PATTERN = Pattern.compile("^[0-9a-f]{32}$");
    private static final int NONCE_EXPIRE_SECONDS = 660; // 11分钟，比JWT稍长
    
    @Override
    public SessionInitResponse initSession() {
        // 1. 获取客户端信息
        String clientIp = TraceContext.getClientIPFromMDC();
        String visitorId = TraceContext.getVisitorId();
        String deviceHash = TraceContext.getDeviceHash();
        String userAgent = TraceContext.getUserAgent();

        // 2. 验证visitorId格式
        if (StrUtil.isBlank(visitorId) || !VISITOR_ID_PATTERN.matcher(visitorId).matches()) {
            throw new ValidationException(ResponseCode.DEVICE_FINGERPRINT_REQUIRED, "访客ID格式不正确");
        }
        
        // 3. 检查设备黑名单
        if (isDeviceBlacklisted(visitorId)) {
            throw new SecurityException(ResponseCode.DEVICE_BLACKLISTED, "设备已被拉黑");
        }
        
        // 4. 限流检查 - IP级别初始化限制
        String ipLimitKey = RedisKeyConstants.buildInitIpLimitKey(clientIp);
        if (!rateLimitService.isAllowed(ipLimitKey, 60, 10)) { // 每分钟最多10次初始化
            throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "会话初始化过于频繁，请稍后再试");
        }
        
        // 5. 限流检查 - 设备级别
        String deviceLimitKey = RedisKeyConstants.buildInitDeviceLimitKey(visitorId);
        if (!rateLimitService.isAllowed(deviceLimitKey, 300, 30)) { // 5分钟最多30次
            throw new RateLimitException(ResponseCode.RATE_LIMIT_EXCEEDED, "设备请求过于频繁，请稍后再试");
        }
        
        // 6. 生成JWT令牌
        String sessionToken = jwtUtil.generateSessionToken(visitorId, deviceHash, clientIp, userAgent);
        
        // 7. 从JWT中获取jti
        DecodedJWT decodedJWT = jwtUtil.verifyToken(sessionToken);
        String jti = decodedJWT.getId();
        
        // 8. 生成初始nonce
        String nonce = generateNonce(jti);
        
        // 9. 获取令牌有效期
        long expiresIn = jwtUtil.getRemainingSeconds(decodedJWT);
        
        log.info("会话初始化成功 - visitorId: {}, jti: {}, ip: {}", visitorId, jti, clientIp);
        
        return SessionInitResponse.builder()
                .sessionToken(sessionToken)
                .nonce(nonce)
                .expiresIn(expiresIn)
                .expiresAt(System.currentTimeMillis() / 1000 + expiresIn)
                .build();
    }
    
    @Override
    public String validateSessionAndNonce(String token, String nonce, String currentDeviceHash, String clientIp, String userAgent) {
        // 1. 验证JWT令牌
        DecodedJWT decodedJWT = jwtUtil.verifyToken(token);
        
        // 2. 检查令牌是否过期
        if (jwtUtil.isTokenExpired(decodedJWT)) {
            throw new SecurityException(ResponseCode.TOKEN_EXPIRED, "会话令牌已过期");
        }
        
        // 3. 获取会话信息
        Map<String, Object> sessionInfo = jwtUtil.getSessionInfo(decodedJWT);
        String jti = (String) sessionInfo.get("jti");
        String visitorId = (String) sessionInfo.get("visitorId");
        String tokenDeviceHash = (String) sessionInfo.get("deviceHash");
        String initialIp = (String) sessionInfo.get("initialIp");
        String initialUa = (String) sessionInfo.get("initialUa");
        
        // 4. 检查黑名单
        if (isDeviceBlacklisted(visitorId) || isSessionBlacklisted(jti)) {
            throw new SecurityException(ResponseCode.DEVICE_BLACKLISTED, "设备或会话已被拉黑");
        }
        
        // 5. 验证设备指纹绑定（如果提供了currentDeviceHash）
        if (currentDeviceHash != null && !tokenDeviceHash.equals(currentDeviceHash)) {
            log.warn("设备指纹不匹配 - jti: {}, expected: {}, actual: {}", jti, tokenDeviceHash, currentDeviceHash);
            throw new SecurityException(ResponseCode.DEVICE_MISMATCH, "设备指纹不匹配");
        }
        
        // 6. 验证nonce
        if (!validateNonce(jti, nonce)) {
            throw new SecurityException(ResponseCode.NONCE_INVALID, "无效的nonce");
        }
        
        // 7. IP网段检查（警告级别，不阻断）
        if (!isSameNetworkSegment(initialIp, clientIp)) {
            log.warn("IP网段变化 - jti: {}, initial: {}, current: {}", jti, initialIp, clientIp);
        }
        
        // 8. UA变化检查（警告级别，不阻断）
        if (!isSimilarUserAgent(initialUa, userAgent)) {
            log.warn("User-Agent变化 - jti: {}, initial: {}, current: {}", jti, initialUa, userAgent);
        }
        
        // 9. 生成新的nonce
        String newNonce = generateNonce(jti);
        
        log.debug("会话验证成功 - jti: {}, visitorId: {}", jti, visitorId);
        return newNonce;
    }
    
    @Override
    public Map<String, Object> validateSessionWithoutConsumingNonce(String token, String nonce, String currentDeviceHash, String clientIp, String userAgent) {
        // 1. 验证JWT令牌
        DecodedJWT decodedJWT = jwtUtil.verifyToken(token);
        
        // 2. 检查令牌是否过期
        if (jwtUtil.isTokenExpired(decodedJWT)) {
            throw new SecurityException(ResponseCode.TOKEN_EXPIRED, "会话令牌已过期");
        }
        
        // 3. 获取会话信息
        Map<String, Object> sessionInfo = jwtUtil.getSessionInfo(decodedJWT);
        String jti = (String) sessionInfo.get("jti");
        String visitorId = (String) sessionInfo.get("visitorId");
        String tokenDeviceHash = (String) sessionInfo.get("deviceHash");
        String initialIp = (String) sessionInfo.get("initialIp");
        String initialUa = (String) sessionInfo.get("initialUa");
        
        // 4. 检查黑名单
        if (isDeviceBlacklisted(visitorId) || isSessionBlacklisted(jti)) {
            throw new SecurityException(ResponseCode.DEVICE_BLACKLISTED, "设备或会话已被拉黑");
        }
        
        // 5. 验证设备指纹绑定（如果提供了currentDeviceHash）
        if (currentDeviceHash != null && !tokenDeviceHash.equals(currentDeviceHash)) {
            log.warn("设备指纹不匹配 - jti: {}, expected: {}, actual: {}", jti, tokenDeviceHash, currentDeviceHash);
            throw new SecurityException(ResponseCode.DEVICE_MISMATCH, "设备指纹不匹配");
        }
        
        // 6. 验证nonce（但不消费）
        if (!validateNonce(jti, nonce)) {
            throw new SecurityException(ResponseCode.NONCE_INVALID, "无效的nonce");
        }
        
        // 7. IP网段检查（警告级别，不阻断）
        if (!isSameNetworkSegment(initialIp, clientIp)) {
            log.warn("IP网段变化 - jti: {}, initial: {}, current: {}", jti, initialIp, clientIp);
        }
        
        // 8. UA变化检查（警告级别，不阻断）
        if (!isSimilarUserAgent(initialUa, userAgent)) {
            log.warn("User-Agent变化 - jti: {}, initial: {}, current: {}", jti, initialUa, userAgent);
        }
        
        log.debug("会话验证成功（未消费nonce） - jti: {}, visitorId: {}", jti, visitorId);
        return sessionInfo;
    }
    
        @Override
    public String generateNonce(String jti) {
        String nonce = IdUtil.simpleUUID();
        String redisKey = RedisKeyConstants.buildSessionNonceKey(jti);
        redisTemplate.opsForValue().set(redisKey, nonce, NONCE_EXPIRE_SECONDS, TimeUnit.SECONDS);
        return nonce;
    }

    @Override
    public boolean validateNonce(String jti, String nonce) {
        String redisKey = RedisKeyConstants.buildSessionNonceKey(jti);
        String storedNonce = redisTemplate.opsForValue().get(redisKey);
        return nonce != null && nonce.equals(storedNonce);
    }

    @Override
    public boolean isDeviceBlacklisted(String visitorId) {
        String redisKey = RedisKeyConstants.buildBlacklistDeviceKey(visitorId);
        return Boolean.TRUE.equals(redisTemplate.hasKey(redisKey));
    }

    @Override
    public boolean isSessionBlacklisted(String jti) {
        String redisKey = RedisKeyConstants.buildBlacklistSessionKey(jti);
        return Boolean.TRUE.equals(redisTemplate.hasKey(redisKey));
    }
    
    /**
     * 检查IP是否在同一网段
     */
    private boolean isSameNetworkSegment(String ip1, String ip2) {
        if (ip1 == null || ip2 == null) {
            return false;
        }
        
        try {
            // IPv4 比较前两段 (/16)
            if (ip1.contains(".") && ip2.contains(".")) {
                String[] segments1 = ip1.split("\\.");
                String[] segments2 = ip2.split("\\.");
                return segments1[0].equals(segments2[0]) && segments1[1].equals(segments2[1]);
            }
            
            // IPv6 比较前四段 (/64)
            if (ip1.contains(":") && ip2.contains(":")) {
                String[] segments1 = ip1.split(":");
                String[] segments2 = ip2.split(":");
                return segments1.length >= 4 && segments2.length >= 4 &&
                       segments1[0].equals(segments2[0]) && segments1[1].equals(segments2[1]) &&
                       segments1[2].equals(segments2[2]) && segments1[3].equals(segments2[3]);
            }
            
            return false;
        } catch (Exception e) {
            log.warn("IP网段比较失败: {} vs {}", ip1, ip2);
            return false;
        }
    }
    
    /**
     * 检查User-Agent是否相似
     */
    private boolean isSimilarUserAgent(String ua1, String ua2) {
        if (ua1 == null || ua2 == null) {
            return false;
        }
        
        try {
            // 简单的浏览器名称和主版本号比较
            String browser1 = extractBrowserInfo(ua1);
            String browser2 = extractBrowserInfo(ua2);
            return browser1.equals(browser2);
        } catch (Exception e) {
            log.warn("UA比较失败: {} vs {}", ua1, ua2);
            return false;
        }
    }
    
    /**
     * 提取浏览器信息
     */
    private String extractBrowserInfo(String userAgent) {
        if (userAgent == null) {
            return "";
        }
        
        // Chrome
        if (userAgent.contains("Chrome/")) {
            int start = userAgent.indexOf("Chrome/") + 7;
            int end = userAgent.indexOf(".", start);
            if (end > start) {
                return "Chrome/" + userAgent.substring(start, end);
            }
        }
        
        // Firefox
        if (userAgent.contains("Firefox/")) {
            int start = userAgent.indexOf("Firefox/") + 8;
            int end = userAgent.indexOf(".", start);
            if (end > start) {
                return "Firefox/" + userAgent.substring(start, end);
            }
        }
        
        // Safari
        if (userAgent.contains("Safari/") && userAgent.contains("Version/")) {
            int start = userAgent.indexOf("Version/") + 8;
            int end = userAgent.indexOf(".", start);
            if (end > start) {
                return "Safari/" + userAgent.substring(start, end);
            }
        }
        
        return "Unknown";
    }
    
        @Override
    public void recordVerificationCodeFailure(String jti) {
        String failureKey = RedisKeyConstants.buildVerifyCodeFailuresKey(jti);
        // 记录失败次数，5分钟过期
        redisTemplate.opsForValue().increment(failureKey);
        redisTemplate.expire(failureKey, Duration.ofMinutes(5));
    }

    @Override
    public boolean isVerificationCodeAttemptsExceeded(String jti) {
        String failureKey = RedisKeyConstants.buildVerifyCodeFailuresKey(jti);
        String count = redisTemplate.opsForValue().get(failureKey);
        // 同一个nonce最多允许5次验证码错误
        return count != null && Integer.parseInt(count) >= 5;
    }

    @Override
    public String getCurrentNonce(String jti) {
        String nonceKey = RedisKeyConstants.buildSessionNonceKey(jti);
        return redisTemplate.opsForValue().get(nonceKey);
    }
    
    @Override
    public String extractJti(String token) {
        return jwtUtil.extractJti(token);
    }
} 