package com.jiashu.labiai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.ApiResponse;
import com.jiashu.labiai.dto.response.user.CurrentSubscriptionDTO;
import com.jiashu.labiai.dto.response.user.SubscriptionHistoryDTO;
import com.jiashu.labiai.dto.response.user.UserSubscriptionInfoResponse;
import com.jiashu.labiai.entity.Order;
import com.jiashu.labiai.entity.PackagePrice;
import com.jiashu.labiai.entity.SubscriptionHistory;
import com.jiashu.labiai.entity.UserSubscription;
import com.jiashu.labiai.exception.SubscriptionException;
import com.jiashu.labiai.mapper.PackageMapper;
import com.jiashu.labiai.mapper.PackagePriceMapper;
import com.jiashu.labiai.mapper.SubscriptionHistoryMapper;
import com.jiashu.labiai.mapper.UserSubscriptionMapper;
import com.jiashu.labiai.service.IUserSubscriptionService;
import com.jiashu.labiai.util.TraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户订阅服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserSubscriptionServiceImpl extends ServiceImpl<UserSubscriptionMapper, UserSubscription> 
        implements IUserSubscriptionService {
    
    private final SubscriptionHistoryMapper subscriptionHistoryMapper;
    private final PackagePriceMapper packagePriceMapper;
    private final PackageMapper packageMapper;
    
    @Override
    public UserSubscription getCurrentSubscription(Long userId) {
        try {
            return getOne(new QueryWrapper<UserSubscription>()
                    .eq("user_id", userId)
                    .eq("status", "ACTIVE")
                    .gt("end_time", LocalDateTime.now())
                    .orderByDesc("end_time")
                    .last("LIMIT 1"));
        } catch (Exception e) {
            log.error("获取用户当前订阅失败，userId: {}", userId, e);
            return null;
        }
    }
    
    @Override
    @Transactional
    public ApiResponse<UserSubscription> processSubscription(Order order) {
        try {
            Long userId = order.getUserId();
            
            Map<String, Object> processDetails = new HashMap<>();
            processDetails.put("orderId", order.getId());
            processDetails.put("userId", userId);
            processDetails.put("packageId", order.getPackageId());
            
            // 1. 查询用户当前是否有有效订阅
            UserSubscription currentSubscription = getCurrentSubscription(userId);
            
            UserSubscription result;
            
            if (currentSubscription == null) {
                // 情况1：用户没有订阅 - 创建新订阅
                processDetails.put("action", "CREATE");
                result = createNewSubscription(order);
            } else {
                // 情况2：用户已有订阅 - 处理续费或升级
                if (currentSubscription.getPackageId().equals(order.getPackageId())) {
                    // 同套餐续费
                    processDetails.put("action", "RENEW");
                    result = renewSubscription(order, currentSubscription);
                } else {
                    // 不同套餐升级/降级
                    processDetails.put("action", "UPGRADE");
                    processDetails.put("fromPackageId", currentSubscription.getPackageId());
                    processDetails.put("toPackageId", order.getPackageId());
                    result = upgradeSubscription(order, currentSubscription);
                }
            }
            
            processDetails.put("subscriptionId", result.getId());
            processDetails.put("endTime", result.getEndTime());
            TraceUtils.recordBusinessEvent("SUBSCRIPTION_PROCESSED", processDetails);
            
            return ApiResponse.success(result, "订阅处理成功");
            
        } catch (Exception e) {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("orderId", order.getId());
            errorDetails.put("userId", order.getUserId());
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("SUBSCRIPTION_PROCESS_FAILED", errorDetails);
            
            log.error("处理订阅失败", e);
            return ApiResponse.error(500, "处理订阅失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public UserSubscription createNewSubscription(Order order) {
        try {
            // 获取套餐价格信息
            PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());
            if (packagePrice == null) {
                throw SubscriptionException.packagePriceNotFound();
            }
            
            // 创建新订阅
            UserSubscription subscription = new UserSubscription();
            subscription.setUserId(order.getUserId());
            subscription.setPackageId(order.getPackageId());
            subscription.setOrderId(order.getId());
            subscription.setStatus("ACTIVE");
            subscription.setStartTime(LocalDateTime.now());
            subscription.setEndTime(calculateEndTime(packagePrice));
            subscription.setAutoRenewal(0); // 默认不自动续费
            
            save(subscription);
            
            // 记录订阅历史
            recordSubscriptionHistory(subscription, order, "CREATE", null, null);
            
            Map<String, Object> createDetails = new HashMap<>();
            createDetails.put("subscriptionId", subscription.getId());
            createDetails.put("userId", order.getUserId());
            createDetails.put("packageId", order.getPackageId());
            createDetails.put("endTime", subscription.getEndTime());
            TraceUtils.recordBusinessEvent("SUBSCRIPTION_CREATED", createDetails);
            
            return subscription;
            
        } catch (Exception e) {
            log.error("创建新订阅失败", e);
            throw SubscriptionException.createFailed(e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public UserSubscription renewSubscription(Order order, UserSubscription currentSubscription) {
        try {
            // 获取套餐价格信息
            PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());
            if (packagePrice == null) {
                throw SubscriptionException.packagePriceNotFound();
            }
            
            // 记录原来的结束时间
            LocalDateTime originalEndTime = currentSubscription.getEndTime();
            
            // 计算新的结束时间（从当前结束时间开始延长，如果已过期则从现在开始）
            LocalDateTime baseTime = originalEndTime.isAfter(LocalDateTime.now()) ? 
                                   originalEndTime : LocalDateTime.now();
            LocalDateTime newEndTime = calculateEndTimeFromBase(packagePrice, baseTime);
            
            // 更新订阅
            currentSubscription.setEndTime(newEndTime);
            currentSubscription.setStatus("ACTIVE"); // 确保状态为活跃
            updateById(currentSubscription);
            
            // 记录订阅历史
            recordSubscriptionHistory(currentSubscription, order, "RENEW", originalEndTime, newEndTime);
            
            Map<String, Object> renewDetails = new HashMap<>();
            renewDetails.put("subscriptionId", currentSubscription.getId());
            renewDetails.put("userId", order.getUserId());
            renewDetails.put("packageId", order.getPackageId());
            renewDetails.put("originalEndTime", originalEndTime);
            renewDetails.put("newEndTime", newEndTime);
            TraceUtils.recordBusinessEvent("SUBSCRIPTION_RENEWED", renewDetails);
            
            return currentSubscription;
            
        } catch (Exception e) {
            log.error("续费订阅失败", e);
            throw SubscriptionException.renewFailed(e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public UserSubscription upgradeSubscription(Order order, UserSubscription currentSubscription) {
        try {
            // 获取套餐价格信息
            PackagePrice packagePrice = packagePriceMapper.selectById(order.getPackagePriceId());
            if (packagePrice == null) {
                throw SubscriptionException.packagePriceNotFound();
            }
            
            // 记录原来的信息
            Long originalPackageId = currentSubscription.getPackageId();
            LocalDateTime originalEndTime = currentSubscription.getEndTime();
            
            // 立即切换到新套餐，重新计算结束时间
            LocalDateTime newEndTime = calculateEndTime(packagePrice);
            
            currentSubscription.setPackageId(order.getPackageId());
            currentSubscription.setEndTime(newEndTime);
            currentSubscription.setStatus("ACTIVE");
            updateById(currentSubscription);
            
            // 记录订阅历史
            recordSubscriptionHistory(currentSubscription, order, "UPGRADE", originalEndTime, newEndTime);
            
            Map<String, Object> upgradeDetails = new HashMap<>();
            upgradeDetails.put("subscriptionId", currentSubscription.getId());
            upgradeDetails.put("userId", order.getUserId());
            upgradeDetails.put("fromPackageId", originalPackageId);
            upgradeDetails.put("toPackageId", order.getPackageId());
            upgradeDetails.put("originalEndTime", originalEndTime);
            upgradeDetails.put("newEndTime", newEndTime);
            TraceUtils.recordBusinessEvent("SUBSCRIPTION_UPGRADED", upgradeDetails);
            
            return currentSubscription;
            
        } catch (Exception e) {
            log.error("升级订阅失败", e);
            throw SubscriptionException.upgradeFailed(e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public void cancelSubscription(Long userId) {
        try {
            UserSubscription subscription = getCurrentSubscription(userId);
            if (subscription == null) {
                throw SubscriptionException.subscriptionNotFound();
            }
            
            subscription.setStatus("CANCELLED");
            updateById(subscription);
            
            // 记录取消历史
            recordSubscriptionHistory(subscription, null, "CANCEL", subscription.getEndTime(), null);
            
            Map<String, Object> cancelDetails = new HashMap<>();
            cancelDetails.put("subscriptionId", subscription.getId());
            cancelDetails.put("userId", userId);
            TraceUtils.recordBusinessEvent("SUBSCRIPTION_CANCELLED", cancelDetails);
            
            log.info("订阅取消成功，userId: {}, subscriptionId: {}", userId, subscription.getId());
            
        } catch (SubscriptionException e) {
            // 重新抛出订阅异常，让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            log.error("取消订阅失败", e);
            throw SubscriptionException.cancelFailed(e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public void checkAndUpdateExpiredSubscriptions() {
        try {
            LocalDateTime now = LocalDateTime.now();
            UpdateWrapper<UserSubscription> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("status", "EXPIRED")
                    .set("updated_at", now)
                    .eq("status", "ACTIVE")
                    .le("end_time", now);
            
            boolean updateResult = update(updateWrapper);
            
            if (updateResult) {
                Map<String, Object> expireDetails = new HashMap<>();
                expireDetails.put("updateResult", updateResult);
                expireDetails.put("checkTime", now);
                TraceUtils.recordBusinessEvent("SUBSCRIPTIONS_EXPIRED_UPDATED", expireDetails);
                
                log.info("更新过期订阅状态完成");
            }
            
        } catch (Exception e) {
            log.error("检查并更新过期订阅失败", e);
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("error", e.getMessage());
            TraceUtils.recordError("CHECK_EXPIRED_SUBSCRIPTIONS_FAILED", errorDetails);
        }
    }
    
    /**
     * 计算订阅结束时间
     */
    private LocalDateTime calculateEndTime(PackagePrice packagePrice) {
        return calculateEndTimeFromBase(packagePrice, LocalDateTime.now());
    }
    
    /**
     * 从指定基准时间计算结束时间
     */
    private LocalDateTime calculateEndTimeFromBase(PackagePrice packagePrice, LocalDateTime baseTime) {
        String billingCycle = packagePrice.getBillingCycle();
        Integer cycleCount = packagePrice.getCycleCount();
        
        switch (billingCycle) {
            case "DAY":
                return baseTime.plusDays(cycleCount);
            case "MONTH":
                return baseTime.plusMonths(cycleCount);
            case "QUARTER":
                return baseTime.plusMonths(cycleCount * 3);
            case "YEAR":
                return baseTime.plusYears(cycleCount);
            default:
                return baseTime.plusMonths(1); // 默认1个月
        }
    }
    
    /**
     * 记录订阅历史
     */
    private void recordSubscriptionHistory(UserSubscription subscription, Order order, String action,
                                         LocalDateTime fromEndTime, LocalDateTime toEndTime) {
        try {
            SubscriptionHistory history = new SubscriptionHistory();
            history.setUserId(subscription.getUserId());
            history.setSubscriptionId(subscription.getId());
            history.setOrderId(order != null ? order.getId() : null);
            history.setAction(action);
            history.setToPackageId(subscription.getPackageId());
            history.setFromEndTime(fromEndTime);
            history.setToEndTime(toEndTime != null ? toEndTime : subscription.getEndTime());
            
            // 如果是升级操作，需要设置原套餐ID
            if ("UPGRADE".equals(action) && order != null) {
                // 从当前订阅获取原套餐ID（在升级前应该先保存）
                // 这里简化处理，实际应该从参数传入
                history.setFromPackageId(subscription.getPackageId());
            }
            
            subscriptionHistoryMapper.insert(history);
            
        } catch (Exception e) {
            log.warn("记录订阅历史失败", e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    @Override
    public UserSubscriptionInfoResponse getUserSubscriptionInfo(Long userId) {
        try {
            UserSubscriptionInfoResponse response = new UserSubscriptionInfoResponse();
            response.setUserId(userId);
            
            // 1. 获取当前订阅信息
            UserSubscription currentSubscription = getCurrentSubscription(userId);
            if (currentSubscription != null) {
                CurrentSubscriptionDTO currentDTO = buildCurrentSubscriptionDTO(currentSubscription);
                response.setCurrentSubscription(currentDTO);
                response.setHasActiveSubscription(true);
            } else {
                response.setHasActiveSubscription(false);
            }
            
            // 2. 获取订阅历史记录（最新10条）
            List<SubscriptionHistoryDTO> historyList = getSubscriptionHistory(userId, 10);
            response.setHistoryList(historyList);
            
            // 3. 获取历史记录总数
            Long totalCount = getSubscriptionHistoryCount(userId);
            response.setTotalHistoryCount(totalCount);
            
            return response;
            
        } catch (Exception e) {
            log.error("获取用户订阅信息失败，userId: {}", userId, e);
            throw SubscriptionException.of("获取用户订阅信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建当前订阅DTO
     */
    private CurrentSubscriptionDTO buildCurrentSubscriptionDTO(UserSubscription subscription) {
        CurrentSubscriptionDTO dto = new CurrentSubscriptionDTO();
        dto.setId(subscription.getId());
        dto.setPackageId(subscription.getPackageId());
        dto.setStatus(subscription.getStatus());
        dto.setStartTime(subscription.getStartTime());
        dto.setEndTime(subscription.getEndTime());
        dto.setAutoRenewal(subscription.getAutoRenewal());
        
        // 获取套餐信息
        com.jiashu.labiai.entity.Package pkg = packageMapper.selectById(subscription.getPackageId());
        if (pkg != null) {
            dto.setPackageName(pkg.getDisplayName() != null ? pkg.getDisplayName() : pkg.getName());
            dto.setPackageType(pkg.getName()); // 使用name作为type
            // limits字段需要从其他地方获取，这里暂时设置为null
            dto.setLimits(null);
        }
        
        // 计算剩余天数
        if (subscription.getEndTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            if (subscription.getEndTime().isAfter(now)) {
                long remainingDays = ChronoUnit.DAYS.between(now, subscription.getEndTime());
                dto.setRemainingDays(remainingDays);
                dto.setExpiringSoon(remainingDays <= 7);
            } else {
                dto.setRemainingDays(0L);
                dto.setExpiringSoon(false);
            }
        }
        
        return dto;
    }
    
    /**
     * 获取订阅历史记录
     */
    private List<SubscriptionHistoryDTO> getSubscriptionHistory(Long userId, int limit) {
        try {
            QueryWrapper<SubscriptionHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                    .orderByDesc("created_at")
                    .last("LIMIT " + limit);
            
            List<SubscriptionHistory> historyList = subscriptionHistoryMapper.selectList(queryWrapper);
            List<SubscriptionHistoryDTO> dtoList = new ArrayList<>();
            
            for (SubscriptionHistory history : historyList) {
                SubscriptionHistoryDTO dto = buildSubscriptionHistoryDTO(history);
                dtoList.add(dto);
            }
            
            return dtoList;
            
        } catch (Exception e) {
            log.error("获取订阅历史失败，userId: {}", userId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取订阅历史记录总数
     */
    private Long getSubscriptionHistoryCount(Long userId) {
        try {
            QueryWrapper<SubscriptionHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            return subscriptionHistoryMapper.selectCount(queryWrapper);
        } catch (Exception e) {
            log.error("获取订阅历史总数失败，userId: {}", userId, e);
            return 0L;
        }
    }
    
    /**
     * 构建订阅历史DTO
     */
    private SubscriptionHistoryDTO buildSubscriptionHistoryDTO(SubscriptionHistory history) {
        SubscriptionHistoryDTO dto = new SubscriptionHistoryDTO();
        dto.setId(history.getId());
        dto.setAction(history.getAction());
        dto.setOrderId(history.getOrderId());
        dto.setFromPackageId(history.getFromPackageId());
        dto.setToPackageId(history.getToPackageId());
        dto.setFromEndTime(history.getFromEndTime());
        dto.setToEndTime(history.getToEndTime());
        dto.setCreatedAt(history.getCreatedAt());
        
        // 设置操作描述
        dto.setActionDescription(getActionDescription(history.getAction()));
        
        // 获取套餐名称
        if (history.getFromPackageId() != null) {
            com.jiashu.labiai.entity.Package fromPkg = packageMapper.selectById(history.getFromPackageId());
            if (fromPkg != null) {
                dto.setFromPackageName(fromPkg.getDisplayName() != null ? fromPkg.getDisplayName() : fromPkg.getName());
            }
        }
        
        if (history.getToPackageId() != null) {
            com.jiashu.labiai.entity.Package toPkg = packageMapper.selectById(history.getToPackageId());
            if (toPkg != null) {
                dto.setToPackageName(toPkg.getDisplayName() != null ? toPkg.getDisplayName() : toPkg.getName());
            }
        }
        
        // 获取支付金额（通过订单ID查询）
        if (history.getOrderId() != null) {
            // 这里需要注入OrderMapper或OrderService来查询订单金额
            // 暂时设置为null，后续可以完善
            dto.setPaymentAmount(null);
        }
        
        return dto;
    }
    
    /**
     * 获取操作描述
     */
    private String getActionDescription(String action) {
        switch (action) {
            case "CREATE":
                return "开通订阅";
            case "RENEW":
                return "续费订阅";
            case "UPGRADE":
                return "升级套餐";
            case "CANCEL":
                return "取消订阅";
            default:
                return "未知操作";
        }
    }
} 