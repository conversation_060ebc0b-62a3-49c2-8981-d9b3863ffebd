package com.jiashu.labiai.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.entity.UserThirdPartyAccounts;
import com.jiashu.labiai.mapper.UserThirdPartyAccountsMapper;
import com.jiashu.labiai.service.IUserThirdPartyAccountsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Service
public class UserThirdPartyAccountsServiceImpl extends ServiceImpl<UserThirdPartyAccountsMapper, UserThirdPartyAccounts> implements IUserThirdPartyAccountsService {

}
