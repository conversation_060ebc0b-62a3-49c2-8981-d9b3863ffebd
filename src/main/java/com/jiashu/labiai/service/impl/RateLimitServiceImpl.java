package com.jiashu.labiai.service.impl;

import com.jiashu.labiai.constants.RedisKeyConstants;
import com.jiashu.labiai.service.IRateLimitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 频率限制服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateLimitServiceImpl implements IRateLimitService {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    // 登录频率限制配置
    private static final int USER_LOGIN_WINDOW = 60; // 1分钟
    private static final int USER_LOGIN_MAX_ATTEMPTS = 5; // 每分钟最多5次

    // 登录失败限制配置
    private static final int USER_FAILED_LOGIN_WINDOW = 1800; // 30分钟
    private static final int USER_FAILED_LOGIN_MAX_ATTEMPTS = 5; // 30分钟内最多5次失败

    private static final int IP_LOGIN_WINDOW = 300; // 5分钟
    private static final int IP_LOGIN_MAX_ATTEMPTS = 20; // 每5分钟最多20次
    
    private static final int DEVICE_LOGIN_WINDOW = 60; // 1分钟
    private static final int DEVICE_LOGIN_MAX_ATTEMPTS = 10; // 每分钟最多10次
    
    @Override
    public boolean isAllowed(String key, int windowSeconds, int maxRequests) {
        try {
            String redisKey = RedisKeyConstants.buildRateLimitKey(key);
            long currentTime = System.currentTimeMillis();
            long windowStart = currentTime - (windowSeconds * 1000L);
            
            // 清理过期的记录
            redisTemplate.opsForZSet().removeRangeByScore(redisKey, 0, windowStart);
            
            // 获取当前窗口内的请求数
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            
            if (count != null && count >= maxRequests) {
                return false;
            }
            
            // 添加当前请求记录
            redisTemplate.opsForZSet().add(redisKey, String.valueOf(currentTime), currentTime);
            
            // 设置过期时间
            redisTemplate.expire(redisKey, Duration.ofSeconds(windowSeconds));
            
            return true;
            
        } catch (Exception e) {
            log.error("频率限制检查异常: {}", e.getMessage(), e);
            // 异常情况下允许请求
            return true;
        }
    }
    
    @Override
    public Long getNextAllowedTime(String key, int windowSeconds) {
        try {
            String redisKey = RedisKeyConstants.buildRateLimitKey(key);
            long currentTime = System.currentTimeMillis();
            long windowStart = currentTime - (windowSeconds * 1000L);
            
            // 获取最早的请求时间
            var range = redisTemplate.opsForZSet().rangeWithScores(redisKey, 0, 0);
            if (range != null && !range.isEmpty()) {
                Double earliestScore = range.iterator().next().getScore();
                if (earliestScore != null) {
                    return earliestScore.longValue() + (windowSeconds * 1000L);
                }
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("获取下次允许时间异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public void clearLimit(String key) {
        try {
            String redisKey = RedisKeyConstants.buildRateLimitKey(key);
            redisTemplate.delete(redisKey);
        } catch (Exception e) {
            log.error("清除限流记录异常: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public int getCurrentCount(String key, int windowSeconds) {
        try {
            String redisKey = RedisKeyConstants.buildRateLimitKey(key);
            long currentTime = System.currentTimeMillis();
            long windowStart = currentTime - (windowSeconds * 1000L);
            
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("获取当前请求数异常: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 检查用户登录频率
     */
    @Override
    public boolean checkUserLoginRate(Long userId) {
        return isAllowed(RedisKeyConstants.USER_LOGIN_LIMIT_PREFIX + userId, USER_LOGIN_WINDOW, USER_LOGIN_MAX_ATTEMPTS);
    }
    
    /**
     * 检查IP登录频率
     */
    @Override
    public boolean checkIpLoginRate(String ip) {
        return isAllowed(RedisKeyConstants.IP_LOGIN_LIMIT_PREFIX + ip, IP_LOGIN_WINDOW, IP_LOGIN_MAX_ATTEMPTS);
    }
    
    /**
     * 检查设备登录频率
     */
    @Override
    public boolean checkDeviceLoginRate(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return true; // 如果没有设备ID，允许请求
        }
        return isAllowed(RedisKeyConstants.DEVICE_LOGIN_LIMIT_PREFIX + deviceId, DEVICE_LOGIN_WINDOW, DEVICE_LOGIN_MAX_ATTEMPTS);
    }
    
    /**
     * 增加用户登录失败次数
     * 
     * @param userId 用户ID
     * @return 当前失败次数
     */
    @Override
    public int incrementUserLoginFailure(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildUserLoginFailureKey(userId);
            long currentTime = System.currentTimeMillis();
            
            // 添加失败记录
            redisTemplate.opsForZSet().add(redisKey, String.valueOf(currentTime), currentTime);
            
            // 设置过期时间
            redisTemplate.expire(redisKey, Duration.ofSeconds(USER_FAILED_LOGIN_WINDOW));
            
            // 获取当前失败次数
            long windowStart = currentTime - (USER_FAILED_LOGIN_WINDOW * 1000L);
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("增加用户登录失败次数异常: userId={}, error={}", userId, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 增加IP登录失败次数
     * 
     * @param ip IP地址
     * @return 当前失败次数
     */
    @Override
    public int incrementIpLoginFailure(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return 0;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildIpLoginFailureKey(ip);
            long currentTime = System.currentTimeMillis();
            
            // 添加失败记录
            redisTemplate.opsForZSet().add(redisKey, String.valueOf(currentTime), currentTime);
            
            // 设置过期时间
            redisTemplate.expire(redisKey, Duration.ofSeconds(USER_FAILED_LOGIN_WINDOW));
            
            // 获取当前失败次数
            long windowStart = currentTime - (USER_FAILED_LOGIN_WINDOW * 1000L);
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("增加IP登录失败次数异常: ip={}, error={}", ip, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 增加设备登录失败次数
     * 
     * @param deviceId 设备ID
     * @return 当前失败次数
     */
    @Override
    public int incrementDeviceLoginFailure(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return 0;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildDeviceLoginFailureKey(deviceId);
            long currentTime = System.currentTimeMillis();
            
            // 添加失败记录
            redisTemplate.opsForZSet().add(redisKey, String.valueOf(currentTime), currentTime);
            
            // 设置过期时间
            redisTemplate.expire(redisKey, Duration.ofSeconds(USER_FAILED_LOGIN_WINDOW));
            
            // 获取当前失败次数
            long windowStart = currentTime - (USER_FAILED_LOGIN_WINDOW * 1000L);
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("增加设备登录失败次数异常: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 重置用户登录失败次数
     * 
     * @param userId 用户ID
     */
    @Override
    public void resetUserLoginFailure(Long userId) {
        if (userId == null) {
            return;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildUserLoginFailureKey(userId);
            redisTemplate.delete(redisKey);
            log.debug("已重置用户登录失败次数: userId={}", userId);
        } catch (Exception e) {
            log.error("重置用户登录失败次数异常: userId={}, error={}", userId, e.getMessage(), e);
        }
    }
    
    /**
     * 重置IP登录失败次数
     * 
     * @param ip IP地址
     */
    @Override
    public void resetIpLoginFailure(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildIpLoginFailureKey(ip);
            redisTemplate.delete(redisKey);
            log.debug("已重置IP登录失败次数: ip={}", ip);
        } catch (Exception e) {
            log.error("重置IP登录失败次数异常: ip={}, error={}", ip, e.getMessage(), e);
        }
    }
    
    /**
     * 重置设备登录失败次数
     * 
     * @param deviceId 设备ID
     */
    @Override
    public void resetDeviceLoginFailure(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildDeviceLoginFailureKey(deviceId);
            redisTemplate.delete(redisKey);
            log.debug("已重置设备登录失败次数: deviceId={}", deviceId);
        } catch (Exception e) {
            log.error("重置设备登录失败次数异常: deviceId={}, error={}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 获取用户登录失败次数
     * 
     * @param userId 用户ID
     * @return 失败次数
     */
    @Override
    public int getUserLoginFailureCount(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildUserLoginFailureKey(userId);
            long currentTime = System.currentTimeMillis();
            long windowStart = currentTime - (USER_FAILED_LOGIN_WINDOW * 1000L);
            
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("获取用户登录失败次数异常: userId={}, error={}", userId, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取IP登录失败次数
     * 
     * @param ip IP地址
     * @return 失败次数
     */
    @Override
    public int getIpLoginFailureCount(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return 0;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildIpLoginFailureKey(ip);
            long currentTime = System.currentTimeMillis();
            long windowStart = currentTime - (USER_FAILED_LOGIN_WINDOW * 1000L);
            
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("获取IP登录失败次数异常: ip={}, error={}", ip, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取设备登录失败次数
     * 
     * @param deviceId 设备ID
     * @return 失败次数
     */
    @Override
    public int getDeviceLoginFailureCount(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return 0;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildDeviceLoginFailureKey(deviceId);
            long currentTime = System.currentTimeMillis();
            long windowStart = currentTime - (USER_FAILED_LOGIN_WINDOW * 1000L);
            
            Long count = redisTemplate.opsForZSet().count(redisKey, windowStart, currentTime);
            return count != null ? count.intValue() : 0;
            
        } catch (Exception e) {
            log.error("获取设备登录失败次数异常: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 检查用户是否达到最大失败次数
     * 
     * @param userId 用户ID
     * @return 是否达到最大失败次数
     */
    @Override
    public boolean isUserLoginLocked(Long userId) {
        int failureCount = getUserLoginFailureCount(userId);
        return failureCount >= USER_FAILED_LOGIN_MAX_ATTEMPTS;
    }
} 