package com.jiashu.labiai.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.constants.RedisKeyConstants;
import com.jiashu.labiai.dto.request.DeviceManageRequest;
import com.jiashu.labiai.dto.response.DeviceInfoResponse;
import com.jiashu.labiai.dto.response.DeviceRiskResponse;
import com.jiashu.labiai.entity.IpInfo;
import com.jiashu.labiai.entity.UserDevices;
import com.jiashu.labiai.enums.DeviceStatus;
import com.jiashu.labiai.enums.DeviceType;
import com.jiashu.labiai.mapper.UserDevicesMapper;
import com.jiashu.labiai.service.IUserDevicesService;
import com.jiashu.labiai.trace.TraceContext;
import com.jiashu.labiai.util.DeviceInfo;
import com.jiashu.labiai.util.DeviceUtil;
import com.jiashu.labiai.util.IpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户设备管理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDevicesServiceImpl extends ServiceImpl<UserDevicesMapper, UserDevices> implements IUserDevicesService {


    private final IpUtil ipUtil;
//    private final ISecurityEventsService securityEventsService;
    private final RedisTemplate<String,String> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDevices registerOrUpdateDevice(Long userId, String deviceId, String deviceHash, HttpServletRequest request) {
        try {
            // 1. 查找现有设备记录
            UserDevices existingDevice = getOne(new LambdaQueryWrapper<UserDevices>()
                    .eq(UserDevices::getUserId, userId)
                    .eq(UserDevices::getDeviceId, deviceId));

            String clientIP = TraceContext.getClientIPFromMDC();
            String userAgent = TraceContext.getUserAgent();
            DeviceInfo deviceInfo = DeviceUtil.parseUserAgent(userAgent);
            
            // 2. 获取地理位置信息
            IpInfo info = IpUtil.parseIp(clientIP);
            String location = info.toString();

            if (existingDevice != null) {
                // 更新现有设备
                return updateExistingDevice(existingDevice, deviceHash, clientIP, location, deviceInfo);
            } else {
                // 创建新设备
                return createNewDevice(userId, deviceId, deviceHash, clientIP, location, deviceInfo);
            }

        } catch (Exception e) {
            log.error("注册或更新设备失败: userId={}, deviceId={}, error={}", userId, deviceId, e.getMessage(), e);
            throw new RuntimeException("设备注册失败", e);
        }
    }

    @Override
    public boolean validateDeviceSecurity(Long userId, String deviceId, String deviceHash, String clientIP) {
        try {
            UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                    .eq(UserDevices::getUserId, userId)
                    .eq(UserDevices::getDeviceId, deviceId));

            if (device == null) {
                log.warn("设备未注册: userId={}, deviceId={}", userId, deviceId);
                return false;
            }

            // 检查设备状态
            if (device.getStatus() == DeviceStatus.LOCKED ||
                device.getStatus() == DeviceStatus.DELETED) {
                log.warn("设备已被锁定或删除: userId={}, deviceId={}, status={}", 
                    userId, deviceId, device.getStatus());
                return false;
            }

            // 检查设备指纹
            if (StrUtil.isNotBlank(deviceHash) && !Objects.equals(device.getDeviceHash(), deviceHash)) {
                log.warn("设备指纹不匹配: userId={}, deviceId={}, expected={}, actual={}", 
                    userId, deviceId, device.getDeviceHash(), deviceHash);

//                Map<String, Object> eventDate = new HashMap<>();
//                eventDate.put("oldDeviceHash", device.getDeviceHash());
//                eventDate.put("newDeviceHash", deviceHash);
//
//                // 记录指纹变化事件
//                securityEventsService.recordDeviceFingerprintChangeEvent(
//                    userId, deviceId, device.getDeviceHash(), deviceHash, clientIP,
//                    eventDate
//                );
                
                return false;
            }

            // 检查异常行为
            String anomaly = checkDeviceAnomaly(userId, deviceId, deviceHash, clientIP);
            if (anomaly != null) {
                log.warn("检测到设备异常: userId={}, deviceId={}, anomaly={}", 
                    userId, deviceId, anomaly);

//                Map<String, Object> eventDate = new HashMap<>();
//
//                // 记录异常事件
//                securityEventsService.recordDeviceAnomalyEvent(
//                    userId, deviceId, null, clientIP, "DEVICE_ANOMALY", anomaly,
//                    Map.of("anomalyType", "SECURITY_VALIDATION", "severity", "MEDIUM")
//                );
                
                return device.getTrustLevel() >= 70; // 高信任度设备允许通过
            }

            return true;

        } catch (Exception e) {
            log.error("设备安全验证异常: userId={}, deviceId={}, error={}", 
                userId, deviceId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<DeviceInfoResponse> getUserDevices(Long userId) {
        List<UserDevices> devices = list(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .ne(UserDevices::getStatus, DeviceStatus.DELETED.getCode())
                .orderByDesc(UserDevices::getLastLoginAt));

        return devices.stream()
                .map(this::convertToDeviceInfoResponse)
                .collect(Collectors.toList());
    }

    @Override
    public DeviceInfoResponse getDeviceInfo(Long userId, String deviceId) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, deviceId));

        return device != null ? convertToDeviceInfoResponse(device) : null;
    }

    @Override
    public DeviceRiskResponse assessDeviceRisk(Long userId, String deviceId, String clientIP) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, deviceId));

//        if (device == null) {
//            return DeviceRiskResponse.builder()
//                    .deviceId(deviceId)
//                    .riskScore(100)
//                    .riskLevel(DeviceRiskResponse.RiskLevel.CRITICAL)
//                    .trustLevel(0)
//                    .recommendTrust(false)
//                    .riskFactors(List.of(
//                        DeviceRiskResponse.RiskFactor.builder()
//                                .factorType("DEVICE_NOT_FOUND")
//                                .description("设备未在系统中注册")
//                                .weight(100)
//                                .impactLevel("CRITICAL")
//                                .build()
//                    ))
//                    .securityRecommendations(List.of("拒绝访问", "要求重新认证"))
//                    .build();
//        }

        return calculateDeviceRisk(device, clientIP);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeviceTrust(Long userId, String deviceId, boolean trusted, String reason) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, deviceId));

        if (device == null) {
            return false;
        }

        device.setIsTrusted(trusted);
        device.setTrustReason(reason);
        device.setTrustLevel(trusted ? Math.max(device.getTrustLevel(), 70) : 
                            Math.min(device.getTrustLevel(), 30));
        device.setUpdatedAt(LocalDateTime.now());

        boolean updated = updateById(device);

        if (updated) {
            // 记录信任状态变化事件
//            securityEventsService.recordSecurityEvent(
//                "DEVICE_TRUST_CHANGED",
//                1, // 信息级别
//                trusted ? "设备已设为信任" : "设备信任已取消",
//                String.format("用户手动%s设备信任状态，原因：%s",
//                    trusted ? "启用" : "取消", reason),
//                userId, deviceId, null, null, null,
//                Map.of("trusted", trusted, "reason", reason, "trustLevel", device.getTrustLevel())
//            );
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDevice(Long userId, String deviceId) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, deviceId));

        if (device == null) {
            return false;
        }

        device.setStatus(DeviceStatus.DELETED);
        device.setUpdatedAt(LocalDateTime.now());

        boolean removed = updateById(device);

        if (removed) {
            // 记录设备删除事件
//            securityEventsService.recordSecurityEvent(
//                "DEVICE_DELETED",
//                1, // 信息级别
//                "设备已删除",
//                String.format("用户删除了设备：%s", device.getDeviceName()),
//                userId, deviceId, null, null, null,
//                Map.of("deviceName", device.getDeviceName(), "lastLoginAt", device.getLastLoginAt())
//            );
        }

        return removed;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manageDevice(Long userId, DeviceManageRequest request) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, request.getDeviceId()));

        if (device == null) {
            return false;
        }

        switch (request.getAction()) {
            case TRUST:
                return updateDeviceTrust(userId, request.getDeviceId(), true, request.getReason());
            case UNTRUST:
                return updateDeviceTrust(userId, request.getDeviceId(), false, request.getReason());
            case LOCK:
                device.setStatus(DeviceStatus.LOCKED);
                break;
            case UNLOCK:
                device.setStatus(DeviceStatus.NORMAL);
                break;
            case DELETE:
                return removeDevice(userId, request.getDeviceId());
            case RENAME:
                if (StrUtil.isNotBlank(request.getDeviceName())) {
                    device.setDeviceName(request.getDeviceName());
                }
                break;
            default:
                return false;
        }

        device.setUpdatedAt(LocalDateTime.now());
        boolean updated = updateById(device);

        if (updated) {
            // 记录设备管理事件
//            securityEventsService.recordSecurityEvent(
//                "DEVICE_MANAGED",
//                1, // 信息级别
//                "设备管理操作",
//                String.format("用户执行了设备管理操作：%s，原因：%s",
//                    request.getAction(), request.getReason()),
//                userId, request.getDeviceId(), null, null, null,
//                Map.of("action", request.getAction(), "reason", request.getReason())
//            );
        }

        return updated;
    }

    @Override
    public int calculateTrustLevel(UserDevices device, String clientIP) {
        int trustLevel = 0;

        // 基础信任度
        trustLevel += 20;

        // 登录次数加成
        if (device.getLoginCount() >= 50) {
            trustLevel += 30;
        } else if (device.getLoginCount() >= 20) {
            trustLevel += 20;
        } else if (device.getLoginCount() >= 5) {
            trustLevel += 10;
        }

        // 设备使用时长加成
        if (device.getFirstLoginAt() != null) {
            long daysDiff = DateUtil.betweenDay(
                DateUtil.date(device.getFirstLoginAt()),
                DateUtil.date(),
                false
            );
            if (daysDiff >= 90) {
                trustLevel += 25;
            } else if (daysDiff >= 30) {
                trustLevel += 15;
            } else if (daysDiff >= 7) {
                trustLevel += 10;
            }
        }

        // IP地址稳定性加成
        if (StrUtil.isNotBlank(clientIP) && StrUtil.isNotBlank(device.getLastLoginIp())) {
            if (Objects.equals(clientIP, device.getLastLoginIp())) {
                trustLevel += 10;
            } else if (isSameIPNetwork(clientIP, device.getLastLoginIp())) {
                trustLevel += 5;
            }
        }

        // 手动信任加成
        if (device.getIsTrusted() != null && device.getIsTrusted()) {
            trustLevel += 15;
        }

        // 风险评分扣减
        if (device.getRiskScore() != null) {
            trustLevel -= device.getRiskScore() / 4;
        }

        return Math.max(0, Math.min(100, trustLevel));
    }

    @Override
    public void updateDeviceActivity(Long userId, String deviceId, String clientIP, String location) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, deviceId));

        if (device != null) {
            device.setLastLoginAt(LocalDateTime.now());
            device.setLastLoginIp(clientIP);
            device.setLastLocation(location);
            device.setLoginCount(device.getLoginCount() + 1);
            device.setTrustLevel(calculateTrustLevel(device, clientIP));

            // 更新位置历史
            updateLocationHistory(device, clientIP, location);

            updateById(device);
        }
    }

    @Override
    public String checkDeviceAnomaly(Long userId, String deviceId, String deviceHash, String clientIP) {
        UserDevices device = getOne(new LambdaQueryWrapper<UserDevices>()
                .eq(UserDevices::getUserId, userId)
                .eq(UserDevices::getDeviceId, deviceId));

        if (device == null) {
            return "设备未注册";
        }

        List<String> anomalies = new ArrayList<>();

        // 1. 检查设备指纹变化
        if (StrUtil.isNotBlank(deviceHash) && !Objects.equals(device.getDeviceHash(), deviceHash)) {
            anomalies.add("设备指纹发生变化");
        }

        // 2. 检查IP地址异常变化
        if (StrUtil.isNotBlank(clientIP) && StrUtil.isNotBlank(device.getLastLoginIp())) {
            if (!isSameIPNetwork(clientIP, device.getLastLoginIp())) {
                // 检查是否是异常的地理位置变化
                String currentLocation = IpUtil.parseIp(clientIP).toString();
                if (StrUtil.isNotBlank(currentLocation) && StrUtil.isNotBlank(device.getLastLocation())) {
                    if (!Objects.equals(currentLocation, device.getLastLocation())) {
                        anomalies.add("检测到异常的地理位置变化");
                    }
                }
            }
        }

        // 3. 检查登录频率异常
        if (device.getLastLoginAt() != null) {
            LocalDateTime lastLoginAt = device.getLastLoginAt();
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(lastLoginAt.plusMinutes(1))) {
                anomalies.add("登录频率异常（过于频繁）");
            }
        }

        // 4. 检查设备信任度
        if (device.getTrustLevel() < 30) {
            anomalies.add("设备信任度过低");
        }

        return anomalies.isEmpty() ? null : String.join("; ", anomalies);
    }

    /**
     * 更新现有设备
     */
    private UserDevices updateExistingDevice(UserDevices device, String deviceHash, 
                                           String clientIP, String location, DeviceInfo deviceInfo) {
        boolean needUpdate = false;

        // 检查设备指纹是否变化
        if (StrUtil.isNotBlank(deviceHash) && !Objects.equals(device.getDeviceHash(), deviceHash)) {
            log.warn("设备指纹发生变化: deviceId={}, old={}, new={}", 
                device.getDeviceId(), device.getDeviceHash(), deviceHash);
            
            // 降低信任度
            device.setTrustLevel(Math.max(0, device.getTrustLevel() - 20));
            device.setDeviceHash(deviceHash);
            needUpdate = true;
        }

        // 更新设备信息
        if (deviceInfo != null) {
            if (!Objects.equals(device.getPlatform(), deviceInfo.getPlatform())) {
                device.setPlatform(deviceInfo.getPlatform());
                needUpdate = true;
            }
            if (!Objects.equals(device.getBrowser(), deviceInfo.getBrowser())) {
                device.setBrowser(deviceInfo.getBrowser());
                needUpdate = true;
            }
        }

        // 更新活动信息
        device.setLastLoginAt(LocalDateTime.now());
        device.setLastLoginIp(clientIP);
        device.setLastLocation(location);
        device.setLoginCount(device.getLoginCount() + 1);
        device.setUpdatedAt(LocalDateTime.now());
        
        // 重新计算信任度
        device.setTrustLevel(calculateTrustLevel(device, clientIP));
        
        // 更新位置历史
        updateLocationHistory(device, clientIP, location);

        if (updateById(device)) {
            return device;
        } else {
            throw new RuntimeException("更新设备信息失败");
        }
    }

    /**
     * 创建新设备
     */
    private UserDevices createNewDevice(Long userId, String deviceId, String deviceHash, 
                                      String clientIP, String location, DeviceInfo deviceInfo) {
        UserDevices device = new UserDevices();
        device.setUserId(userId);
        device.setDeviceId(deviceId);
        device.setDeviceHash(deviceHash);
        device.setDeviceName(generateDeviceName(deviceInfo));
        // 处理设备类型枚举转换
        if (deviceInfo != null && deviceInfo.getDeviceType() != null) {
            try {
                device.setDeviceType(DeviceType.fromCode(deviceInfo.getDeviceType()));
            } catch (IllegalArgumentException e) {
                device.setDeviceType(DeviceType.UNKNOWN);
            }
        } else {
            device.setDeviceType(DeviceType.UNKNOWN);
        }
        device.setPlatform(deviceInfo != null ? deviceInfo.getPlatform() : "Unknown");
        device.setBrowser(deviceInfo != null ? deviceInfo.getBrowser() : "Unknown");
        device.setIsTrusted(false);
        device.setTrustLevel(calculateInitialTrustLevel());
        device.setFirstLoginAt(LocalDateTime.now());
        device.setLastLoginAt(LocalDateTime.now());
        device.setLastLoginIp(clientIP);
        device.setLastLocation(location);
        device.setLoginCount(1);
        device.setRiskScore(0);
        device.setStatus(DeviceStatus.NORMAL);
        device.setCreatedAt(LocalDateTime.now());
        device.setUpdatedAt(LocalDateTime.now());

        // 初始化位置历史
        if (StrUtil.isNotBlank(location)) {
            List<Map<String, Object>> locationHistory = new ArrayList<>();

            Map<String, Object> locationHistoryMap = new HashMap<>();
            locationHistoryMap.put("location", location);
            locationHistoryMap.put("ipAddress", clientIP);
            locationHistoryMap.put("timestamp", LocalDateTime.now().toString());

            locationHistory.add(locationHistoryMap);
            device.setLocationHistory(JSONUtil.toJsonStr(locationHistory));
        }

        if (save(device)) {
            // 记录新设备注册事件
//            securityEventsService.recordSecurityEvent(
//                "NEW_DEVICE_REGISTERED",
//                2, // 警告级别
//                "新设备注册",
//                String.format("检测到新设备登录：%s", device.getDeviceName()),
//                userId, deviceId, null, clientIP, null,
//                Map.of("deviceName", device.getDeviceName(), "platform", device.getPlatform(),
//                       "browser", device.getBrowser(), "location", location)
//            );
            
            return device;
        } else {
            throw new RuntimeException("创建设备记录失败");
        }
    }

    /**
     * 计算设备风险
     */
    private DeviceRiskResponse calculateDeviceRisk(UserDevices device, String clientIP) {
        List<DeviceRiskResponse.RiskFactor> riskFactors = new ArrayList<>();
        List<String> recommendations = new ArrayList<>();
        List<DeviceRiskResponse.Anomaly> anomalies = new ArrayList<>();
        
        int riskScore = 0;

        // 1. 新设备风险
        if (device.getLoginCount() < 3) {
            riskScore += 30;

//            riskFactors.add(DeviceRiskResponse.RiskFactor.builder()
//                    .factorType("NEW_DEVICE")
//                    .description("新设备或使用次数较少")
//                    .weight(30)
//                    .impactLevel("MEDIUM")
//                    .details( MapUtil.builder().put("loginCount", device.getLoginCount()).build())
//                    .build());
            recommendations.add("建议启用二次验证");
        }

        // 2. 地理位置风险
        if (StrUtil.isNotBlank(clientIP) && StrUtil.isNotBlank(device.getLastLoginIp())) {
            if (!isSameIPNetwork(clientIP, device.getLastLoginIp())) {
                riskScore += 20;
//                riskFactors.add(DeviceRiskResponse.RiskFactor.builder()
//                        .factorType("LOCATION_CHANGE")
//                        .description("检测到地理位置变化")
//                        .weight(20)
//                        .impactLevel("MEDIUM")
//                        .details(Map.of("currentIP", clientIP, "lastIP", device.getLastLoginIp()))
//                        .build());
                recommendations.add("验证当前位置的合法性");
            }
        }

        // 3. 设备信任度风险
        if (device.getTrustLevel() < 50) {
            int trustRisk = 50 - device.getTrustLevel();
            riskScore += trustRisk;
//            riskFactors.add(DeviceRiskResponse.RiskFactor.builder()
//                    .factorType("LOW_TRUST")
//                    .description("设备信任度较低")
//                    .weight(trustRisk)
//                    .impactLevel(trustRisk > 30 ? "HIGH" : "MEDIUM")
//                    .details(Map.of("trustLevel", device.getTrustLevel()))
//                    .build());
            recommendations.add("建议提升设备信任度");
        }

        // 4. 设备状态风险
        if (device.getStatus() == DeviceStatus.ABNORMAL) {
            riskScore += 40;
            riskFactors.add(DeviceRiskResponse.RiskFactor.builder()
                    .factorType("DEVICE_STATUS")
                    .description("设备状态异常")
                    .weight(40)
                    .impactLevel("HIGH")
                    .build());
            recommendations.add("检查设备安全状态");
        }

        // 5. 历史风险评分
        if (device.getRiskScore() != null && device.getRiskScore() > 0) {
            riskScore += device.getRiskScore() / 2;
//            riskFactors.add(DeviceRiskResponse.RiskFactor.builder()
//                    .factorType("HISTORICAL_RISK")
//                    .description("历史风险记录")
//                    .weight(device.getRiskScore() / 2)
//                    .impactLevel("MEDIUM")
//                    .details(Map.of("historicalRisk", device.getRiskScore()))
//                    .build());
        }

        // 检查异常
        String anomalyDesc = checkDeviceAnomaly(device.getUserId(), device.getDeviceId(), 
                                              device.getDeviceHash(), clientIP);
        if (anomalyDesc != null) {
            anomalies.add(DeviceRiskResponse.Anomaly.builder()
                    .anomalyType("DEVICE_ANOMALY")
                    .description(anomalyDesc)
                    .severity("MEDIUM")
                    .detectedAt(LocalDateTime.now().toString())
                    .build());
        }

        // 限制风险评分范围
        riskScore = Math.max(0, Math.min(100, riskScore));

        // 确定风险等级
        DeviceRiskResponse.RiskLevel riskLevel;
        if (riskScore <= 25) {
            riskLevel = DeviceRiskResponse.RiskLevel.LOW;
        } else if (riskScore <= 50) {
            riskLevel = DeviceRiskResponse.RiskLevel.MEDIUM;
        } else if (riskScore <= 75) {
            riskLevel = DeviceRiskResponse.RiskLevel.HIGH;
        } else {
            riskLevel = DeviceRiskResponse.RiskLevel.CRITICAL;
        }

        // 推荐是否信任
        boolean recommendTrust = riskScore < 30 && device.getTrustLevel() > 60;

        return DeviceRiskResponse.builder()
                .deviceId(device.getDeviceId())
                .riskScore(riskScore)
                .riskLevel(riskLevel)
                .trustLevel(device.getTrustLevel())
                .recommendTrust(recommendTrust)
                .riskFactors(riskFactors)
                .securityRecommendations(recommendations)
                .anomalies(anomalies)
                .build();
    }

    /**
     * 转换为设备信息响应
     */
    private DeviceInfoResponse convertToDeviceInfoResponse(UserDevices device) {
        List<DeviceInfoResponse.LocationRecord> locationHistory = new ArrayList<>();
        
        if (StrUtil.isNotBlank(device.getLocationHistory())) {
            try {
//                List<Map<String, Object>> locations = JSONUtil.toList(device.getLocationHistory(), Map.class);
//                locationHistory = locations.stream()
//                        .map(loc -> DeviceInfoResponse.LocationRecord.builder()
//                                .location((String) loc.get("location"))
//                                .ipAddress((String) loc.get("ipAddress"))
//                                .timestamp(LocalDateTime.parse((String) loc.get("timestamp")))
//                                .build())
//                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.warn("解析位置历史失败: deviceId={}", device.getDeviceId());
            }
        }

        // 解析风险因素
        List<String> riskFactors = new ArrayList<>();
        if (StrUtil.isNotBlank(device.getRiskFactors())) {
            try {
                riskFactors = JSONUtil.toList(device.getRiskFactors(), String.class);
            } catch (Exception e) {
                log.warn("解析风险因素失败: deviceId={}", device.getDeviceId());
            }
        }

        return DeviceInfoResponse.builder()
                .deviceId(device.getDeviceId())
                .deviceHash(device.getDeviceHash())
                .deviceName(device.getDeviceName())
                .deviceType(device.getDeviceType() != null ? device.getDeviceType().getCode() : "unknown")
                .platform(device.getPlatform())
                .browser(device.getBrowser())
                .isTrusted(device.getIsTrusted())
                .trustLevel(device.getTrustLevel())
                .trustReason(device.getTrustReason())
                .firstLoginAt(device.getFirstLoginAt())
                .lastLoginAt(device.getLastLoginAt())
                .lastLoginIp(device.getLastLoginIp())
                .loginCount(device.getLoginCount())
                .lastLocation(device.getLastLocation())
                .locationHistory(locationHistory)
                .riskScore(device.getRiskScore())
                .riskFactors(riskFactors)
                .status(device.getStatus().getCode())
                .statusDescription(getStatusDescription(device.getStatus().getCode()))
                .isCurrent(false) // 需要通过其他方式判断
                .createdAt(device.getCreatedAt())
                .updatedAt(device.getUpdatedAt())
                .build();
    }

    /**
     * 生成设备名称
     */
    private String generateDeviceName(DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return "Unknown Device";
        }
        return DeviceUtil.generateDeviceName(deviceInfo.getPlatform(),
                                           deviceInfo.getBrowser(), 
                                           deviceInfo.getDeviceType());
    }

    /**
     * 计算初始信任度
     */
    private int calculateInitialTrustLevel() {
        return 20; // 新设备初始信任度
    }

    /**
     * 更新位置历史
     */
    private void updateLocationHistory(UserDevices device, String clientIP, String location) {
        if (StrUtil.isBlank(location)) {
            return;
        }

        List<Map> locationHistory = new ArrayList<>();
        
        // 解析现有位置历史
        if (StrUtil.isNotBlank(device.getLocationHistory())) {
            try {
                locationHistory = JSONUtil.toList(device.getLocationHistory(), Map.class);
            } catch (Exception e) {
                log.warn("解析位置历史失败: deviceId={}", device.getDeviceId());
            }
        }
        Map<String,Object> locationInfo = new HashMap<>();
        locationInfo.put("location", location);
        locationInfo.put("ipAddress", clientIP);
        locationInfo.put("timestamp", LocalDateTime.now().toString());

        // 添加新位置记录
        locationHistory.add(0, locationInfo);

        // 保留最近20条记录
        if (locationHistory.size() > 20) {
            locationHistory = locationHistory.subList(0, 20);
        }

        device.setLocationHistory(JSONUtil.toJsonStr(locationHistory));
    }

    /**
     * 判断是否为同一IP网段
     */
    private boolean isSameIPNetwork(String ip1, String ip2) {
        try {
            String[] parts1 = ip1.split("\\.");
            String[] parts2 = ip2.split("\\.");
            
            if (parts1.length >= 3 && parts2.length >= 3) {
                return parts1[0].equals(parts2[0]) && 
                       parts1[1].equals(parts2[1]) && 
                       parts1[2].equals(parts2[2]);
            }
            
            return Objects.equals(ip1, ip2);
        } catch (Exception e) {
            return Objects.equals(ip1, ip2);
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case 1: return "正常";
            case 2: return "异常";
            case 3: return "锁定";
            case 4: return "拉黑";
            case 5: return "已删除";
            default: return "未知";
        }
    }

    @Override
    public boolean isDeviceBlacklisted(Long userId, String deviceId) {
        if (userId == null || !StringUtils.hasText(deviceId)) {
            return false;
        }
        
        try {
            String redisKey = RedisKeyConstants.buildBlacklistDeviceKey(deviceId);
            boolean black = redisTemplate.hasKey(redisKey);
            if (black) {
                return true;
            }

            UserDevices device = this.baseMapper.selectOne(
                new QueryWrapper<UserDevices>()
                    .eq("user_id", userId)
                    .eq("device_id", deviceId)
                    .eq("status", DeviceStatus.BLACKLISTED.getCode())
            );

            
            return device != null;
        } catch (Exception e) {
            log.error("检查设备拉黑状态失败: userId={}, deviceId={}", userId, deviceId, e);
            return false;
        }
    }

    @Override
    public void recordUserDevice(Long userId, String visitorId, String deviceHash, String clientIP, String userAgent) {
        LambdaQueryWrapper<UserDevices> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDevices::getUserId, userId)
                    .eq(UserDevices::getDeviceId, visitorId);
        UserDevices devices = this.baseMapper.selectOne(queryWrapper);
        if (devices == null) {
            UserDevices device = new UserDevices();
            device.setUserId(userId);
            device.setDeviceId(visitorId);
            device.setDeviceHash(deviceHash);
            device.setLastLoginIp(clientIP);
            device.setLastLoginAt(LocalDateTime.now());
            device.setLoginCount(1);
            device.setTrustLevel(calculateInitialTrustLevel());
            device.setRiskScore(0);
            device.setStatus(DeviceStatus.NORMAL);
            device.setCreatedAt(LocalDateTime.now());
            device.setUpdatedAt(LocalDateTime.now());
            JSONArray locationHistory = new JSONArray();
            String location = IpUtil.parseIp(clientIP).toString();
            if (StrUtil.isNotBlank(location)) {
                JSONObject locationInfo = new JSONObject();
                locationInfo.set("location", location);
                locationInfo.set("ipAddress", clientIP);
                locationInfo.set("timestamp", LocalDateTime.now().toString());
                locationHistory.add(locationInfo);
            }
            device.setLocationHistory(locationHistory.toString());

            save(device);
        } else {
            devices.setLastLoginIp(clientIP);
            devices.setLastLoginAt(LocalDateTime.now());
            devices.setLoginCount(devices.getLoginCount() + 1);
            devices.setUpdatedAt(LocalDateTime.now());
            JSONArray locationHistory = JSONUtil.parseArray(devices.getLocationHistory());
            String location = IpUtil.parseIp(clientIP).toString();
            if (StrUtil.isNotBlank(location)) {
                JSONObject locationInfo = new JSONObject();
                locationInfo.set("location", location);
                locationInfo.set("ipAddress", clientIP);
                locationInfo.set("timestamp", LocalDateTime.now().toString());
                locationHistory.add(0, locationInfo);
            }
            devices.setLocationHistory(locationHistory.toString());
            updateById(devices);
        }



    }
}
