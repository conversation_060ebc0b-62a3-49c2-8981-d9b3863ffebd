package com.jiashu.labiai.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jiashu.labiai.dto.request.auth.UserRegisterRequest;
import com.jiashu.labiai.dto.response.auth.UserRegisterResponse;
import com.jiashu.labiai.entity.Users;
import com.jiashu.labiai.enums.*;
import com.jiashu.labiai.exception.BusinessException;
import com.jiashu.labiai.service.IUsersService;
import com.jiashu.labiai.service.UserRegisterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户注册服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegisterServiceImpl implements UserRegisterService {
    
    private final IUsersService usersService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserRegisterResponse registerUser(UserRegisterRequest request) {
        
        // 1. 二次检查邮箱是否已存在（防止并发注册）
        if (isEmailExists(request.getEmail())) {
            throw BusinessException.of(ResponseCode.EMAIL_ALREADY_EXISTS);
        }
        
        // 2. 创建用户对象
        Users user = buildNewUser(request);
        
        // 3. 保存用户
        boolean saved = usersService.save(user);
        if (!saved) {
            log.error("用户注册失败 - 数据库保存失败，邮箱: {}", request.getEmail());
            throw BusinessException.of(ResponseCode.INTERNAL_SERVER_ERROR, "用户注册失败");
        }
        
        log.info("用户注册成功 - userId: {}, email: {}", user.getId(), user.getEmail());
        
        // 4. 构建响应
        return UserRegisterResponse.builder()
                .message("注册成功")
                .userId(user.getId().toString())
                .email(user.getEmail())
                .registeredAt(user.getCreatedAt())
                .build();
    }
    
    @Override
    public boolean isEmailExists(String email) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Users::getEmail, email);
        
        return usersService.exists(wrapper);
    }
    
    @Override
    public boolean isPhoneExists(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Users::getPhone, phone);
        
        return usersService.exists(wrapper);
    }
    
    @Override
    public String generateDefaultNickname(String email) {
        if (StrUtil.isBlank(email)) {
            return "用户" + System.currentTimeMillis();
        }
        
        // 从邮箱提取用户名部分作为昵称
        String username = email.substring(0, email.indexOf('@'));
        
        // 检查昵称是否已存在，如果存在则添加随机数字
        String baseNickname = username;
        String finalNickname = baseNickname;
        int suffix = 1;
        
        while (isNicknameExists(finalNickname)) {
            finalNickname = baseNickname + suffix++;
            if (suffix > 999) { // 防止无限循环
                finalNickname = baseNickname + System.currentTimeMillis();
                break;
            }
        }
        
        return finalNickname;
    }
    

    
    /**
     * 构建新用户对象
     */
    private Users buildNewUser(UserRegisterRequest request) {
        Users user = new Users();
        
        // 基本信息
        user.setEmail(request.getEmail());
        user.setNickname(generateDefaultNickname(request.getEmail()));
        
        // 密码哈希（使用MD5，生产环境建议使用BCrypt）
        String passwordHash = DigestUtil.md5Hex(request.getPassword());
        user.setPasswordHash(passwordHash);
        
        // 枚举类型设置 - 现在直接使用枚举对象！！！
        // MyBatis-Plus会自动序列化@EnumValue标注的字段到数据库
        user.setUserType(UserType.NORMAL);  // 直接使用枚举对象
        
        // 其他字段设置默认值
        user.setPrimaryLoginType(LoginMethod.PASSWORD);
        user.setAccountSource("registration");
        user.setStatus(UserStatus.NORMAL);  // 直接使用枚举对象
        user.setEmailVerified(true);  // 注册时已验证邮箱（通过验证码）
        user.setPhoneVerified(false);
//        user.setLoginFailedCount(0);
        user.setSecurityLevel(SecurityLevel.NORMAL);  // 直接使用枚举对象
        user.setTwoFactorEnabled(false);
        user.setLoginNotificationEnabled(false);
        user.setProfilePublic(false);
        user.setAllowThirdPartyBind(true);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        return user;
    }
    

    
    /**
     * 检查昵称是否已存在
     */
    private boolean isNicknameExists(String nickname) {
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Users::getNickname, nickname);
        return usersService.exists(wrapper);
    }
} 