package com.jiashu.labiai.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiashu.labiai.dto.response.SecurityEventResponse;
import com.jiashu.labiai.entity.SecurityEvents;
import com.jiashu.labiai.enums.SecurityEventLevel;
import com.jiashu.labiai.enums.SecurityEventStatus;
import com.jiashu.labiai.enums.SecurityEventType;
import com.jiashu.labiai.mapper.SecurityEventsMapper;
import com.jiashu.labiai.service.ISecurityEventsService;
import com.jiashu.labiai.service.IUserDevicesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 安全事件服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityEventsServiceImpl extends ServiceImpl<SecurityEventsMapper, SecurityEvents> implements ISecurityEventsService {

    private final IUserDevicesService userDevicesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityEvents recordSecurityEvent(SecurityEventType eventType, String title,
                                            String description, Long userId, String deviceId,
                                            String sessionToken, String ipAddress, String loginMethod,
                                            Map<String, Object> eventData) {
        try {
            SecurityEvents event = new SecurityEvents();
            event.setEventType(eventType.getCode());
            event.setEventTypeName(eventType.getName());
            
            // 根据枚举中定义的级别设置事件级别
            SecurityEventLevel level;
            switch (eventType.getLevel()) {
                case 1:
                    level = SecurityEventLevel.INFO;
                    break;
                case 2:
                    level = SecurityEventLevel.WARNING;
                    break;
                case 3:
                    level = SecurityEventLevel.DANGER;
                    break;
                case 4:
                    level = SecurityEventLevel.CRITICAL;
                    break;
                default:
                    level = SecurityEventLevel.INFO;
            }
            event.setEventLevel(level);
            
            event.setEventTitle(title);
            event.setEventDescription(description);
            event.setUserId(userId);
            event.setDeviceId(deviceId);
            event.setSessionToken(sessionToken);
            event.setIpAddress(ipAddress);
            event.setEventData(eventData != null ? JSONUtil.toJsonStr(eventData) : null);
            event.setStatus(SecurityEventStatus.PENDING); // 待处理
            event.setCreatedAt(LocalDateTime.now());

            save(event);
            
            log.info("安全事件已记录: eventType={}, eventLevel={}, userId={}, deviceId={}", 
                eventType.getCode(), eventType.getLevel(), userId, deviceId);
            
            return event;
            
        } catch (Exception e) {
            log.error("记录安全事件失败: eventType={}, userId={}, error={}", 
                eventType.getCode(), userId, e.getMessage(), e);
            throw new RuntimeException("记录安全事件失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityEvents recordSecurityEvent(String eventType, Integer eventLevel, String title,
                                            String description, Long userId, String deviceId,
                                            String sessionToken, String ipAddress, String loginMethod,
                                            Map<String, Object> eventData) {
        try {
            SecurityEvents event = new SecurityEvents();
            event.setEventType(eventType);
            
            // 尝试从SecurityEventType枚举中获取事件类型名称
            try {
                SecurityEventType securityEventType = SecurityEventType.fromCode(eventType);
                event.setEventTypeName(securityEventType.getName());
            } catch (Exception e) {
                // 如果找不到对应的枚举值，使用原始事件类型作为名称
                event.setEventTypeName(eventType);
                log.warn("未找到事件类型对应的枚举值: {}", eventType);
            }
            
            // 根据传入的级别设置事件级别
            SecurityEventLevel level;
            switch (eventLevel) {
                case 1:
                    level = SecurityEventLevel.INFO;
                    break;
                case 2:
                    level = SecurityEventLevel.WARNING;
                    break;
                case 3:
                    level = SecurityEventLevel.DANGER;
                    break;
                case 4:
                    level = SecurityEventLevel.CRITICAL;
                    break;
                default:
                    level = SecurityEventLevel.INFO;
            }
            event.setEventLevel(level);
            
            event.setEventTitle(title);
            event.setEventDescription(description);
            event.setUserId(userId);
            event.setDeviceId(deviceId);
            event.setSessionToken(sessionToken);
            event.setIpAddress(ipAddress);
            event.setEventData(eventData != null ? JSONUtil.toJsonStr(eventData) : null);
            event.setStatus(SecurityEventStatus.PENDING); // 待处理
            event.setCreatedAt(LocalDateTime.now());

            save(event);
            
            log.info("安全事件已记录: eventType={}, eventLevel={}, userId={}, deviceId={}", 
                eventType, eventLevel, userId, deviceId);
            
            return event;
            
        } catch (Exception e) {
            log.error("记录安全事件失败: eventType={}, userId={}, error={}", 
                eventType, userId, e.getMessage(), e);
            throw new RuntimeException("记录安全事件失败", e);
        }
    }

    @Override
    public void recordDeviceAnomalyEvent(Long userId, String deviceId, String sessionToken,
                                       String ipAddress, String anomalyType, String description,
                                       Map<String, Object> eventData) {
        recordSecurityEvent(
            SecurityEventType.DEVICE_ANOMALY,
            "设备异常检测",
            description,
            userId,
            deviceId,
            sessionToken,
            ipAddress,
            null,
            eventData
        );
    }

    @Override
    public void recordLoginAnomalyEvent(Long userId, String deviceId, String ipAddress,
                                      String loginMethod, String failureReason,
                                      Map<String, Object> eventData) {
        recordSecurityEvent(
            SecurityEventType.LOGIN_FAILED,
            "登录异常",
            String.format("登录异常：%s", failureReason),
            userId,
            deviceId,
            null,
            ipAddress,
            loginMethod,
            eventData
        );
    }

    @Override
    public void recordDeviceFingerprintChangeEvent(Long userId, String deviceId, String oldDeviceHash,
                                                 String newDeviceHash, String ipAddress,
                                                 Map<String, Object> eventData) {
        String description = String.format("设备指纹发生变化，旧指纹：%s，新指纹：%s", 
            oldDeviceHash != null ? oldDeviceHash.substring(0, Math.min(8, oldDeviceHash.length())) + "..." : "无",
            newDeviceHash != null ? newDeviceHash.substring(0, Math.min(8, newDeviceHash.length())) + "..." : "无");
            
        recordSecurityEvent(
            SecurityEventType.DEVICE_HASH_MISMATCH,
            "设备指纹变化",
            description,
            userId,
            deviceId,
            null,
            ipAddress,
            null,
            eventData
        );
    }

    @Override
    public List<SecurityEventResponse> getUserSecurityEvents(Long userId, Integer limit) {
        List<SecurityEvents> events = list(new LambdaQueryWrapper<SecurityEvents>()
                .eq(SecurityEvents::getUserId, userId)
                .orderByDesc(SecurityEvents::getCreatedAt)
                .last(limit != null ? "LIMIT " + limit : "LIMIT 50"));

        return events.stream()
                .map(this::convertToSecurityEventResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<SecurityEventResponse> getDeviceSecurityEvents(Long userId, String deviceId, Integer limit) {
        List<SecurityEvents> events = list(new LambdaQueryWrapper<SecurityEvents>()
                .eq(SecurityEvents::getUserId, userId)
                .eq(SecurityEvents::getDeviceId, deviceId)
                .orderByDesc(SecurityEvents::getCreatedAt)
                .last(limit != null ? "LIMIT " + limit : "LIMIT 20"));

        return events.stream()
                .map(this::convertToSecurityEventResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<SecurityEventResponse> getHighRiskEvents(Integer eventLevel, Integer limit) {
        List<SecurityEvents> events = list(new LambdaQueryWrapper<SecurityEvents>()
                .ge(SecurityEvents::getEventLevel, eventLevel != null ? eventLevel : 3)
                .eq(SecurityEvents::getStatus, 1) // 待处理
                .orderByDesc(SecurityEvents::getCreatedAt)
                .last(limit != null ? "LIMIT " + limit : "LIMIT 100"));

        return events.stream()
                .map(this::convertToSecurityEventResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleSecurityEvent(Long eventId, Long handledBy, String handleNotes) {
        SecurityEvents event = getById(eventId);
        if (event == null) {
            return false;
        }

        event.setStatus(SecurityEventStatus.HANDLED);
        event.setHandledBy(handledBy);
        event.setHandledAt(LocalDateTime.now());
        event.setHandleNotes(handleNotes);

        return updateById(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchHandleSecurityEvents(List<Long> eventIds, Long handledBy, String handleNotes) {
        int successCount = 0;
        
        for (Long eventId : eventIds) {
            try {
                if (handleSecurityEvent(eventId, handledBy, handleNotes)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量处理安全事件失败: eventId={}, error={}", eventId, e.getMessage());
            }
        }
        
        return successCount;
    }

    @Override
    public int calculateUserRiskScore(Long userId, Integer days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days != null ? days : 7);
        
        List<SecurityEvents> events = list(new LambdaQueryWrapper<SecurityEvents>()
                .eq(SecurityEvents::getUserId, userId)
                .ge(SecurityEvents::getCreatedAt, startTime));

        int riskScore = 0;
        for (SecurityEvents event : events) {
            // 根据事件级别计算风险分数
            switch (event.getEventLevel().getCode()) {
                case 1: riskScore += 1; break;  // 信息
                case 2: riskScore += 5; break;  // 警告
                case 3: riskScore += 15; break; // 危险
                case 4: riskScore += 30; break; // 严重
            }
        }

        return Math.min(100, riskScore);
    }

    @Override
    public int calculateDeviceRiskScore(Long userId, String deviceId, Integer days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days != null ? days : 7);
        
        List<SecurityEvents> events = list(new LambdaQueryWrapper<SecurityEvents>()
                .eq(SecurityEvents::getUserId, userId)
                .eq(SecurityEvents::getDeviceId, deviceId)
                .ge(SecurityEvents::getCreatedAt, startTime));

        int riskScore = 0;
        for (SecurityEvents event : events) {
//            switch (event.getEventLevel()) {
//                case 1: riskScore += 2; break;  // 信息
//                case 2: riskScore += 8; break;  // 警告
//                case 3: riskScore += 20; break; // 危险
//                case 4: riskScore += 40; break; // 严重
//            }
        }

        return Math.min(100, riskScore);
    }

    @Override
    public boolean shouldTriggerSecurityPolicy(Long userId, SecurityEventType eventType, Integer timeWindow) {
        return shouldTriggerSecurityPolicy(userId, eventType.getCode(), timeWindow);
    }

    @Override
    public boolean shouldTriggerSecurityPolicy(Long userId, String eventType, Integer timeWindow) {
        // 获取指定时间窗口内的事件数量
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(timeWindow != null ? timeWindow : 60);
        
        long count = count(new LambdaQueryWrapper<SecurityEvents>()
                .eq(SecurityEvents::getUserId, userId)
                .eq(SecurityEvents::getEventType, eventType)
                .ge(SecurityEvents::getCreatedAt, startTime));
                
        // 获取该事件类型的阈值
        int threshold = getEventThreshold(eventType);
        
        return count >= threshold;
    }

    /**
     * 转换为安全事件响应DTO
     */
    private SecurityEventResponse convertToSecurityEventResponse(SecurityEvents event) {
        try {
            SecurityEventResponse response = SecurityEventResponse.builder()
                    .id(event.getId())
                    .eventType(event.getEventType())
                    .eventTypeName(event.getEventTypeName())
                    .eventTypeEnum(SecurityEventType.fromCode(event.getEventType()))
                    .eventLevel(event.getEventLevel())
                    .eventTitle(event.getEventTitle())
                    .eventDescription(event.getEventDescription())
                    .userId(event.getUserId())
                    .deviceId(event.getDeviceId())
                    .sessionToken(event.getSessionToken())
                    .ipAddress(event.getIpAddress())
                    .loginMethod(event.getLoginMethod() != null ? event.getLoginMethod().name() : null)
                    .thirdPartyProvider(event.getThirdPartyProvider() != null ? event.getThirdPartyProvider().name() : null)
                    .status(event.getStatus())
                    .handledBy(event.getHandledBy())
                    .handledAt(event.getHandledAt())
                    .handleNotes(event.getHandleNotes())
                    .createdAt(event.getCreatedAt())
                    .build();
            
            // 解析事件数据
            if (StrUtil.isNotBlank(event.getEventData())) {
                try {
                    response.setEventData(JSONUtil.toBean(event.getEventData(), Map.class));
                } catch (Exception e) {
                    log.warn("解析事件数据失败: eventId={}, error={}", event.getId(), e.getMessage());
                }
            }
            
            // 解析触发规则
            if (StrUtil.isNotBlank(event.getTriggeredRules())) {
                try {
                    response.setTriggeredRules(JSONUtil.toList(event.getTriggeredRules(), String.class));
                } catch (Exception e) {
                    log.warn("解析触发规则失败: eventId={}, error={}", event.getId(), e.getMessage());
                }
            }
            
            // 尝试获取设备名称
            if (StrUtil.isNotBlank(event.getDeviceId()) && event.getUserId() != null) {
                try {

//                    UserDevices device = userDevicesService.getUserDevice(event.getUserId(), event.getDeviceId());
//                    if (device != null) {
//                        response.setDeviceName(device.getDeviceName());
//                    }
                } catch (Exception e) {
                    log.warn("获取设备名称失败: userId={}, deviceId={}, error={}", 
                        event.getUserId(), event.getDeviceId(), e.getMessage());
                }
            }
            
            // 计算风险评分和推荐操作
            response.setRiskScore(calculateEventRiskScore(event));
            response.setRequiresImmediateAction(event.getEventLevel() != null && event.getEventLevel().getCode() >= 3);
            response.setRecommendedActions(generateRecommendedActions(event));
            
            return response;
        } catch (Exception e) {
            log.error("转换安全事件响应失败: eventId={}, error={}", event.getId(), e.getMessage(), e);
            return SecurityEventResponse.builder()
                    .id(event.getId())
                    .eventTitle("转换失败")
                    .eventDescription("数据转换异常: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 计算事件风险评分
     */
    private Integer calculateEventRiskScore(SecurityEvents event) {
        int baseScore = 0;
        
        // 根据事件级别设置基础分数
        if (event.getEventLevel() != null) {
            switch (event.getEventLevel()) {
                case INFO: baseScore = 10; break;
                case WARNING: baseScore = 30; break;
                case DANGER: baseScore = 60; break;
                case CRITICAL: baseScore = 90; break;
            }
        }
        
        // 根据事件类型调整
        String eventType = event.getEventType();
        if (eventType != null) {
            if (eventType.contains("BLACKLISTED")) {
                baseScore += 20;
            } else if (eventType.contains("UNAUTHORIZED")) {
                baseScore += 15;
            } else if (eventType.contains("ANOMALY")) {
                baseScore += 10;
            }
        }
        
        // 如果是待处理状态，增加分数
        if (event.getStatus() == SecurityEventStatus.PENDING) {
            baseScore += 10;
        }
        
        int riskScore = baseScore;
        return Math.min(100, riskScore);
    }

    /**
     * 生成推荐处理方式
     */
    private List<String> generateRecommendedActions(SecurityEvents event) {
        List<String> actions = new java.util.ArrayList<>();
        
        // 根据事件级别添加通用建议
        if (event.getEventLevel() != null) {
            switch (event.getEventLevel()) {
                case INFO:
                    actions.add("记录日志");
                    break;
                case WARNING:
                    actions.add("监控用户行为");
                    actions.add("发送安全提醒");
                    break;
                case DANGER:
                    actions.add("要求二次验证");
                    actions.add("临时限制访问");
                    actions.add("联系用户确认");
                    break;
                case CRITICAL:
                    actions.add("立即锁定账户");
                    actions.add("强制下线所有会话");
                    actions.add("人工审核");
                    break;
            }
        }
        
        // 根据事件类型添加特定建议
        String eventType = event.getEventType();
        if (eventType != null) {
            if (eventType.equals(SecurityEventType.DEVICE_ID_MISMATCH.getCode())) {
                actions.add("检查设备指纹算法");
                actions.add("验证用户身份");
            } else if (eventType.equals(SecurityEventType.BLACKLISTED_DEVICE_ACCESS.getCode())) {
                actions.add("检查拉黑原因");
                actions.add("审核设备历史记录");
            } else if (eventType.equals(SecurityEventType.IP_CHANGE_UNTRUSTED_DEVICE.getCode())) {
                actions.add("验证用户位置变更");
                actions.add("检查是否使用VPN");
            }
        }
        
        return actions;
    }

    /**
     * 获取事件阈值
     */
    private int getEventThreshold(String eventType) {
        // 根据不同事件类型设置阈值
        if (eventType == null) {
            return 3; // 默认阈值
        }
        
        // 使用SecurityEventType的代码进行比较
        if (SecurityEventType.LOGIN_FAILED.getCode().equals(eventType)) {
            return 5; // 登录失败5次触发
        } else if (SecurityEventType.DEVICE_HASH_MISMATCH.getCode().equals(eventType)) {
            return 3; // 设备指纹变化3次触发
        } else if (SecurityEventType.DEVICE_ID_MISMATCH.getCode().equals(eventType)) {
            return 2; // 设备ID不匹配2次触发
        } else if (SecurityEventType.IP_CHANGE_UNTRUSTED_DEVICE.getCode().equals(eventType)) {
            return 3; // IP变化3次触发
        } else if (SecurityEventType.BLACKLISTED_DEVICE_ACCESS.getCode().equals(eventType)) {
            return 1; // 拉黑设备访问1次触发
        } else if (SecurityEventType.UNAUTHORIZED_ACCESS.getCode().equals(eventType)) {
            return 2; // 越权访问2次触发
        } else if (SecurityEventType.API_ABUSE.getCode().equals(eventType)) {
            return 10; // API滥用10次触发
        }
        
        return 3; // 默认阈值
    }
}
