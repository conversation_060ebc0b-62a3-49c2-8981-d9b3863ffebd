package com.jiashu.labiai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiashu.labiai.dto.*;
import com.jiashu.labiai.entity.Order;

import java.util.List;
import java.util.Map;

/**
 * 订单服务接口
 */
public interface IOrderService extends IService<Order> {
    
    /**
     * 创建订单
     *
     * @param userId 用户ID
     * @param packagePriceId 套餐价格ID
     * @param discountCode 优惠码（可选）
     * @return 订单信息
     */
    ApiResponse<OrderDTO> createOrder(Long userId, Long packagePriceId, String discountCode);
    
    /**
     * 根据订单号查询订单详情
     *
     * @param orderNo 订单号
     * @return 订单详情
     */
    ApiResponse<OrderDTO> getOrderByOrderNo(String orderNo);
    
    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    ApiResponse<OrderDTO> getOrderById(Long orderId);
    
    /**
     * 获取用户订单列表
     *
     * @param userId 用户ID
     * @param status 订单状态（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 订单列表
     */
    ApiResponse<List<OrderDTO>> getUserOrders(Long userId, String status, Integer page, Integer size);
    
    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 操作结果
     */
    ApiResponse<Void> cancelOrder(Long orderId, Long userId);
    
    /**
     * 删除订单（逻辑删除）
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 操作结果
     */
    ApiResponse<Void> deleteOrder(Long orderId, Long userId);
    
    /**
     * 检查订单是否属于用户
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 是否属于用户
     */
    boolean checkOrderOwnership(Long orderId, Long userId);

    /**
     * 预览订单优惠（不创建订单，仅用于前端显示）
     *
     * @param userId 用户ID
     * @param packagePriceId 套餐价格ID
     * @param discountCode 优惠码
     * @return 优惠预览结果
     */
    ApiResponse<DiscountCodeValidationDTO> previewOrderWithDiscount(Long userId, Long packagePriceId, String discountCode);

    /**
     * 获取订单优惠码使用详情
     *
     * @param orderId 订单ID
     * @return 优惠码使用详情
     */
    ApiResponse<Map<String, Object>> getOrderDiscountInfo(Long orderId);

    /**
     * 预览续费价格
     *
     * @param userId 用户ID
     * @param packagePriceId 套餐价格ID
     * @param discountCode 优惠码（可选）
     * @return 续费预览结果
     */
    ApiResponse<RenewalPreviewDTO> previewRenewal(Long userId, Long packagePriceId, String discountCode);

    /**
     * 创建续费订单
     *
     * @param userId 用户ID
     * @param packagePriceId 套餐价格ID
     * @param discountCode 优惠码（可选）
     * @return 续费订单信息
     */
    ApiResponse<OrderDTO> createRenewalOrder(Long userId, Long packagePriceId, String discountCode);

    /**
     * 预览升级价格
     *
     * @param userId 用户ID
     * @param targetPackagePriceId 目标套餐价格ID
     * @param discountCode 优惠码（可选）
     * @return 升级预览结果
     */
    ApiResponse<UpgradePreviewDTO> previewUpgrade(Long userId, Long targetPackagePriceId, String discountCode);

    /**
     * 创建升级订单
     *
     * @param userId 用户ID
     * @param targetPackagePriceId 目标套餐价格ID
     * @param discountCode 优惠码（可选）
     * @return 升级订单信息
     */
    ApiResponse<OrderDTO> createUpgradeOrder(Long userId, Long targetPackagePriceId, String discountCode);
}