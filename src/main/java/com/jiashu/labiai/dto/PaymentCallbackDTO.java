package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 支付回调响应DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentCallbackDTO {
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 商户订单号
     */
    private String tradeOrderId;
    
    /**
     * 支付单号
     */
    private String paymentNo;
    
    /**
     * 时间戳
     */
    private Long timestamp;
} 