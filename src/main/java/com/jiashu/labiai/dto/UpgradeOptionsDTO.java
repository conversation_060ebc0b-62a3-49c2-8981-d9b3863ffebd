package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 升级选项DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpgradeOptionsDTO {

    /**
     * 当前订阅信息
     */
    private CurrentSubscriptionInfo currentSubscription;

    /**
     * 升级选项列表
     */
    private List<UpgradeOption> upgradeOptions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrentSubscriptionInfo {
        // 移除订阅ID，避免暴露敏感的自增主键
        // 保留套餐ID，前端需要用来识别当前套餐

        /**
         * 套餐ID
         */
        private Long packageId;

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 到期时间
         */
        private LocalDateTime endTime;

        /**
         * 剩余天数
         */
        private Long remainingDays;

        /**
         * 剩余价值
         */
        private BigDecimal remainingValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpgradeOption {
        /**
         * 套餐ID
         */
        private Long packageId;

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 套餐描述
         */
        private String description;

        /**
         * 功能特性
         */
        private List<String> features;

        /**
         * 价格选项
         */
        private List<PriceOption> prices;

        /**
         * 升级类型
         */
        private String upgradeType;

        /**
         * 预估升级费用
         */
        private BigDecimal estimatedCost;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceOption {
        /**
         * 套餐价格ID
         */
        private Long packagePriceId;

        /**
         * 计费周期
         */
        private String billingCycle;

        /**
         * 原价
         */
        private BigDecimal originalPrice;

        /**
         * 售价
         */
        private BigDecimal salePrice;

        /**
         * 显示文本
         */
        private String displayText;
    }
}
