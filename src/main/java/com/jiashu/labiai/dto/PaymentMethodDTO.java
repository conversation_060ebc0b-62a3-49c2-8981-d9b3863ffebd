package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 支付方式DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentMethodDTO {
    
    /**
     * 支付方式代码
     */
    private String methodCode;
    
    /**
     * 支付方式名称
     */
    private String methodName;


    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * 最小支付金额
     */
    private BigDecimal minAmount;
    
    /**
     * 最大支付金额
     */
    private BigDecimal maxAmount;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 是否推荐
     */
    private Boolean recommended;
} 