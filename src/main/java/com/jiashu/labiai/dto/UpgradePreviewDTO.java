package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 升级预览DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpgradePreviewDTO {

    /**
     * 升级类型
     */
    private String upgradeType;

    /**
     * 当前套餐信息
     */
    private CurrentPackageInfo currentPackage;

    /**
     * 目标套餐信息
     */
    private TargetPackageInfo targetPackage;

    /**
     * 费用计算详情
     */
    private CalculationInfo calculation;

    /**
     * 优惠码信息
     */
    private DiscountInfo discountInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrentPackageInfo {
        /**
         * 套餐ID
         */
        private Long packageId;

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 剩余天数
         */
        private Long remainingDays;

        /**
         * 剩余价值
         */
        private BigDecimal remainingValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TargetPackageInfo {
        /**
         * 套餐ID
         */
        private Long packageId;

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 套餐价格ID
         */
        private Long packagePriceId;

        /**
         * 月均价格
         */
        private BigDecimal monthlyPrice;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalculationInfo {
        /**
         * 当前套餐剩余价值
         */
        private BigDecimal currentRemainingValue;

        /**
         * 目标套餐剩余时间对应价值
         */
        private BigDecimal targetRemainingValue;

        /**
         * 需要补的差价
         */
        private BigDecimal upgradeDifference;

        /**
         * 优惠金额
         */
        private BigDecimal discountAmount;

        /**
         * 最终需要支付的金额
         */
        private BigDecimal finalAmount;

        /**
         * 到期时间（升级后保持不变）
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 剩余天数
         */
        private Long remainingDays;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiscountInfo {
        /**
         * 优惠码
         */
        private String discountCode;

        /**
         * 优惠类型
         */
        private String discountType;

        /**
         * 优惠值
         */
        private BigDecimal discountValue;
    }
}
