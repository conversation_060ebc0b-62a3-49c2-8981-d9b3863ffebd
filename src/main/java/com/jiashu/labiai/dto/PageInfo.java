package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.io.Serializable;

/**
 * 分页信息DTO
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 当前页码（从1开始）
     */
    private Integer page;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否为第一页
     */
    private Boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private Boolean isLast;
    
    // 私有构造函数
    private PageInfo() {}
    
    /**
     * 手动创建分页信息
     * @param page 当前页码（从1开始）
     * @param size 每页大小
     * @param total 总记录数
     * @return PageInfo实例
     */
    public static PageInfo of(int page, int size, long total) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.page = page;
        pageInfo.size = size;
        pageInfo.total = total;
        
        // 计算总页数
        pageInfo.pages = (int) Math.ceil((double) total / size);
        
        // 计算分页状态
        pageInfo.hasPrevious = page > 1;
        pageInfo.hasNext = page < pageInfo.pages;
        pageInfo.isFirst = page == 1;
        pageInfo.isLast = page >= pageInfo.pages && pageInfo.pages > 0;
        
        return pageInfo;
    }
    
    /**
     * 从Spring Data Page对象创建分页信息
     * @param springPage Spring Data Page对象
     * @return PageInfo实例
     */
    public static PageInfo fromPage(Page<?> springPage) {
        PageInfo pageInfo = new PageInfo();
        
        // Spring Data的页码从0开始，转换为从1开始
        pageInfo.page = springPage.getNumber() + 1;
        pageInfo.size = springPage.getSize();
        pageInfo.total = springPage.getTotalElements();
        pageInfo.pages = springPage.getTotalPages();
        
        pageInfo.hasPrevious = springPage.hasPrevious();
        pageInfo.hasNext = springPage.hasNext();
        pageInfo.isFirst = springPage.isFirst();
        pageInfo.isLast = springPage.isLast();
        
        return pageInfo;
    }
    
    /**
     * 创建空分页信息（用于没有数据的情况）
     * @param page 当前页码
     * @param size 每页大小
     * @return PageInfo实例
     */
    public static PageInfo empty(int page, int size) {
        return of(page, size, 0);
    }
    
    // ================== Getter/Setter ==================

    @Override
    public String toString() {
        return "PageInfo{" +
                "page=" + page +
                ", size=" + size +
                ", total=" + total +
                ", pages=" + pages +
                ", hasPrevious=" + hasPrevious +
                ", hasNext=" + hasNext +
                ", isFirst=" + isFirst +
                ", isLast=" + isLast +
                '}';
    }
} 