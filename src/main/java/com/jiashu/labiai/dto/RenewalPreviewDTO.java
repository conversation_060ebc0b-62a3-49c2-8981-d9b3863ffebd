package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 续费预览DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RenewalPreviewDTO {

    /**
     * 套餐价格ID
     */
    private Long packagePriceId;

    /**
     * 原价
     */
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 货币
     */
    private String currency;

    /**
     * 计费信息
     */
    private String billingInfo;

    /**
     * 当前到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentEndTime;

    /**
     * 续费后新到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime newEndTime;

    /**
     * 优惠码信息
     */
    private DiscountInfo discountInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiscountInfo {
        /**
         * 优惠码
         */
        private String discountCode;

        /**
         * 优惠类型
         */
        private String discountType;

        /**
         * 优惠值
         */
        private BigDecimal discountValue;
    }
}
