package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 套餐DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PackageDTO {
    
    /**
     * 套餐ID
     */
    private Long id;
    
    /**
     * 套餐名称
     */
    private String name;
    
    /**
     * 显示名称
     */
    private String displayName;
    
    /**
     * 套餐描述
     */
    private String description;
    
    /**
     * 功能特性列表
     */
    private List<String> features;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 价格列表
     */
    private List<PackagePriceDTO> prices;
} 