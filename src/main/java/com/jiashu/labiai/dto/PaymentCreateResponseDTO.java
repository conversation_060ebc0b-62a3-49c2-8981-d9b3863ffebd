package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付创建响应DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentCreateResponseDTO {
    
    /**
     * 支付单号
     */
    private String paymentNo;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 支付跳转URL
     */
    private String paymentUrl;
    
    /**
     * 二维码URL
     */
    private String qrCodeUrl;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiredAt;
    

} 