package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderDTO {
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 套餐名称
     */
    private String packageName;
    
    /**
     * 套餐功能
     */
    private List<String> packageFeatures;
    
    /**
     * 计费信息
     */
    private String billingInfo;
    
    /**
     * 原价
     */
    private BigDecimal originalAmount;
    
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 最终金额
     */
    private BigDecimal finalAmount;
    
    /**
     * 货币
     */
    private String currency;
    
    /**
     * 订单状态
     */
    private String status;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiredAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    

} 