package com.jiashu.labiai.dto.response.user;

import lombok.Data;

import java.util.List;

/**
 * 用户订阅信息响应DTO
 */
@Data
public class UserSubscriptionInfoResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 当前订阅信息
     */
    private CurrentSubscriptionDTO currentSubscription;
    
    /**
     * 是否有有效订阅
     */
    private Boolean hasActiveSubscription;
    
    /**
     * 订阅历史记录（最新10条）
     */
    private List<SubscriptionHistoryDTO> historyList;
    
    /**
     * 历史记录总数
     */
    private Long totalHistoryCount;
} 