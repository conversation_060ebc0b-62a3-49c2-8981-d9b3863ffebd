package com.jiashu.labiai.dto.response.auth;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户注册响应DTO
 */
@Data
@Builder
public class UserRegisterResponse {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 注册邮箱
     */
    private String email;
    
    /**
     * 注册时间
     */
    private LocalDateTime registeredAt;
    
    /**
     * 用户ID
     */
    private String userId;
} 