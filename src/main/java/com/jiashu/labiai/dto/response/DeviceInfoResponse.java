package com.jiashu.labiai.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备信息响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfoResponse {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备指纹哈希
     */
    private String deviceHash;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备类型：desktop, mobile, tablet
     */
    private String deviceType;
    
    /**
     * 操作系统平台
     */
    private String platform;
    
    /**
     * 浏览器信息
     */
    private String browser;
    
    /**
     * 屏幕分辨率
     */
    private String screenResolution;
    
    /**
     * 时区
     */
    private String timezone;
    
    /**
     * 是否受信任
     */
    private Boolean isTrusted;
    
    /**
     * 信任度评分 (0-100)
     */
    private Integer trustLevel;
    
    /**
     * 信任原因
     */
    private String trustReason;
    
    /**
     * 首次登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstLoginAt;
    
    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginAt;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 登录次数
     */
    private Integer loginCount;
    
    /**
     * 最后登录地理位置
     */
    private String lastLocation;
    
    /**
     * 位置历史记录
     */
    private List<LocationRecord> locationHistory;
    
    /**
     * 风险评分 (0-100)
     */
    private Integer riskScore;
    
    /**
     * 风险因素
     */
    private List<String> riskFactors;
    
    /**
     * 设备状态：1:正常 2:异常 3:锁定 4:删除
     */
    private Integer status;
    
    /**
     * 设备状态描述
     */
    private String statusDescription;
    
    /**
     * 是否当前设备
     */
    private Boolean isCurrent;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 位置记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationRecord {
        private String location;
        private String ipAddress;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
    }
} 