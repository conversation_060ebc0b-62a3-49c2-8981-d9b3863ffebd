package com.jiashu.labiai.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 当前订阅信息DTO
 */
@Data
public class CurrentSubscriptionDTO {
    
    // 移除订阅ID和套餐ID，避免暴露数据库自增主键
    // 使用套餐名称和类型来标识套餐
    
    /**
     * 套餐名称
     */
    private String packageName;
    
    /**
     * 套餐类型（例如：FREE, PLUS, PRO）
     */
    private String packageType;
    
    /**
     * 订阅状态（ACTIVE, EXPIRED, CANCELLED）
     */
    private String status;
    
    /**
     * 订阅开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 订阅结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 是否自动续费（0=否，1=是）
     */
    private Integer autoRenewal;
    
    /**
     * 剩余天数
     */
    private Long remainingDays;
    
    /**
     * 是否即将过期（7天内过期）
     */
    private Boolean expiringSoon;
    
    /**
     * 套餐权限配置（JSON格式的限制信息）
     */
    private String limits;
} 