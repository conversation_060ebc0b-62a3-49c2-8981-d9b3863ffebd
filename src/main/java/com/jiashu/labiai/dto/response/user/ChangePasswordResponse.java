package com.jiashu.labiai.dto.response.user;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 修改密码响应DTO
 */
@Data
@Builder
public class ChangePasswordResponse {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 密码修改时间
     */
    private LocalDateTime changedAt;
    
    /**
     * 是否需要重新登录
     */
    private Boolean requireRelogin;
} 