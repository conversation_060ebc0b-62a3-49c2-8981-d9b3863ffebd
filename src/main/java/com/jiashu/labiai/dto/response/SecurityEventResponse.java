package com.jiashu.labiai.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiashu.labiai.enums.SecurityEventLevel;
import com.jiashu.labiai.enums.SecurityEventStatus;
import com.jiashu.labiai.enums.SecurityEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 安全事件响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecurityEventResponse {
    
    /**
     * 事件ID
     */
    private Long id;
    
    /**
     * 事件类型代码
     */
    private String eventType;
    
    /**
     * 事件类型名称
     */
    private String eventTypeName;
    
    /**
     * 事件类型枚举（前端展示用）
     */
    private SecurityEventType eventTypeEnum;
    
    /**
     * 事件级别枚举
     */
    private SecurityEventLevel eventLevel;
    
    /**
     * 事件级别描述
     */
    private String eventLevelDescription;
    
    /**
     * 事件标题
     */
    private String eventTitle;
    
    /**
     * 事件描述
     */
    private String eventDescription;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 会话Token
     */
    private String sessionToken;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 地理位置
     */
    private String location;
    
    /**
     * 登录方式
     */
    private String loginMethod;
    
    /**
     * 第三方提供商
     */
    private String thirdPartyProvider;
    
    /**
     * 事件详细数据
     */
    private Map<String, Object> eventData;
    
    /**
     * 触发的规则
     */
    private List<String> triggeredRules;
    
    /**
     * 事件状态枚举
     */
    private SecurityEventStatus status;
    
    /**
     * 事件状态描述
     */
    private String statusDescription;
    
    /**
     * 处理人ID
     */
    private Long handledBy;
    
    /**
     * 处理人姓名
     */
    private String handlerName;
    
    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handledAt;
    
    /**
     * 处理备注
     */
    private String handleNotes;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 风险评分
     */
    private Integer riskScore;
    
    /**
     * 是否需要立即处理
     */
    private Boolean requiresImmediateAction;
    
    /**
     * 推荐处理方式
     */
    private List<String> recommendedActions;
} 