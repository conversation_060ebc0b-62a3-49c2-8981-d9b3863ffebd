package com.jiashu.labiai.dto.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订阅历史DTO
 */
@Data
public class SubscriptionHistoryDTO {
    
    // 移除历史记录ID，避免暴露数据库自增主键
    
    /**
     * 操作类型（CREATE=创建, RENEW=续费, UPGRADE=升级, CANCEL=取消）
     */
    private String action;
    
    /**
     * 操作描述
     */
    private String actionDescription;
    
    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 原套餐ID（升级时使用）
     */
    private Long fromPackageId;
    
    /**
     * 原套餐名称
     */
    private String fromPackageName;
    
    /**
     * 目标套餐ID
     */
    private Long toPackageId;
    
    /**
     * 目标套餐名称
     */
    private String toPackageName;
    
    /**
     * 原结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fromEndTime;
    
    /**
     * 新结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime toEndTime;
    
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
} 