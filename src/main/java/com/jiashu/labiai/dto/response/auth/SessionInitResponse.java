package com.jiashu.labiai.dto.response.auth;

import lombok.Builder;
import lombok.Data;

/**
 * 会话初始化响应DTO
 */
@Data
@Builder
public class SessionInitResponse {
    
    /**
     * 会话令牌
     */
    private String sessionToken;
    
    /**
     * 当前nonce
     */
    private String nonce;
    
    /**
     * 过期时间（秒）
     */
    private Long expiresIn;
    
    /**
     * 过期时间戳
     */
    private Long expiresAt;
} 