package com.jiashu.labiai.dto.response.auth;

import lombok.Builder;
import lombok.Data;

/**
 * 发送验证码响应DTO
 */
@Data
@Builder
public class SendVerificationCodeResponse {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 新的nonce
     */
    private String nonce;
    
    /**
     * 下次允许发送时间戳
     */
    private Long nextAllowedAt;
    
    /**
     * 剩余尝试次数
     */
    private Integer remainingAttempts;
} 