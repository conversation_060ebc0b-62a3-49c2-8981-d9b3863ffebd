package com.jiashu.labiai.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 设备风险评估响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceRiskResponse {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 总体风险评分 (0-100)
     */
    private Integer riskScore;
    
    /**
     * 风险等级：LOW, MEDIUM, HIGH, CRITICAL
     */
    private RiskLevel riskLevel;
    
    /**
     * 信任度评分 (0-100)
     */
    private Integer trustLevel;
    
    /**
     * 是否建议信任
     */
    private Boolean recommendTrust;
    
    /**
     * 风险因素列表
     */
    private List<RiskFactor> riskFactors;
    
    /**
     * 安全建议
     */
    private List<String> securityRecommendations;
    
    /**
     * 检测到的异常
     */
    private List<Anomaly> anomalies;
    
    /**
     * 风险等级枚举
     */
    public enum RiskLevel {
        LOW,      // 低风险 (0-25)
        MEDIUM,   // 中风险 (26-50)
        HIGH,     // 高风险 (51-75)
        CRITICAL  // 极高风险 (76-100)
    }
    
    /**
     * 风险因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        /**
         * 风险因素类型
         */
        private String factorType;
        
        /**
         * 风险因素描述
         */
        private String description;
        
        /**
         * 风险权重 (0-100)
         */
        private Integer weight;
        
        /**
         * 影响等级：LOW, MEDIUM, HIGH
         */
        private String impactLevel;
        
        /**
         * 详细信息
         */
        private Map<String, Object> details;
    }
    
    /**
     * 异常信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Anomaly {
        /**
         * 异常类型
         */
        private String anomalyType;
        
        /**
         * 异常描述
         */
        private String description;
        
        /**
         * 严重程度：LOW, MEDIUM, HIGH, CRITICAL
         */
        private String severity;
        
        /**
         * 检测时间
         */
        private String detectedAt;
        
        /**
         * 异常详情
         */
        private Map<String, Object> details;
    }
} 