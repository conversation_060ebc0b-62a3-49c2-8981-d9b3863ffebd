package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 续费选项DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RenewalOptionsDTO {

    /**
     * 当前订阅信息
     */
    private CurrentSubscriptionInfo currentSubscription;

    /**
     * 续费选项列表
     */
    private List<RenewalOption> renewalOptions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrentSubscriptionInfo {
        // 移除订阅ID，避免暴露敏感的自增主键

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 到期时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 订阅状态
         */
        private String status;

        /**
         * 剩余天数
         */
        private Long daysRemaining;

        /**
         * 是否即将到期（7天内）
         */
        private Boolean isExpiringSoon;

        /**
         * 是否已过期但在宽限期内
         */
        private Boolean isInGracePeriod;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RenewalOption {
        /**
         * 套餐价格ID
         */
        private Long packagePriceId;

        /**
         * 计费周期
         */
        private String billingCycle;

        /**
         * 周期数量
         */
        private Integer cycleCount;

        /**
         * 原价
         */
        private BigDecimal originalPrice;

        /**
         * 售价
         */
        private BigDecimal salePrice;

        /**
         * 货币
         */
        private String currency;

        /**
         * 显示文本
         */
        private String displayText;

        /**
         * 续费后的新到期时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime newEndTime;

        /**
         * 折扣百分比
         */
        private Integer discountPercent;
    }
}
