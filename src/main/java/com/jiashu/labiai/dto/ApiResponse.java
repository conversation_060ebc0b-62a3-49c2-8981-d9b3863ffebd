package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jiashu.labiai.enums.ResponseCode;
import com.jiashu.labiai.trace.TraceContext;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一响应DTO
 * @param <T> 响应数据类型
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应状态
     */
    private Boolean success;
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 链路追踪ID
     */
    private String traceId;
    
    /**
     * 分页信息（可选）
     */
    private PageInfo pagination;
    
    /**
     * 扩展信息（可选）
     */
    private Map<String, Object> extra;
    
    // 私有构造函数
    private ApiResponse() {
        this.timestamp = Instant.now().toEpochMilli();
    }
    
    // ================== 成功响应静态方法 ==================
    
    /**
     * 简单成功响应
     */
    public static <T> ApiResponse<T> success() {
        return success(null, "操作成功");
    }
    
    /**
     * 带数据的成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return success(data, "操作成功");
    }
    
    /**
     * 带数据和自定义消息的成功响应
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.success = true;
        response.code = ResponseCode.SUCCESS.getCode();
        response.message = message;
        response.data = data;
        return response;
    }
    
    /**
     * 分页数据响应
     */
    public static <T> ApiResponse<T> success(T data, PageInfo pagination) {
        ApiResponse<T> response = success(data, "查询成功");
        response.pagination = pagination;
        return response;
    }
    
    /**
     * 分页数据响应（带自定义消息）
     */
    public static <T> ApiResponse<T> success(T data, PageInfo pagination, String message) {
        ApiResponse<T> response = success(data, message);
        response.pagination = pagination;
        return response;
    }
    
    // ================== 失败响应静态方法 ==================
    
    /**
     * 使用预定义错误码的失败响应
     */
    public static <T> ApiResponse<T> error(ResponseCode responseCode) {
        return error(responseCode.getCode(), responseCode.getMessage());
    }
    
    /**
     * 使用预定义错误码和数据的失败响应
     */
    public static <T> ApiResponse<T> error(ResponseCode responseCode, T data) {
        return error(responseCode.getCode(), responseCode.getMessage(), data);
    }
    
    /**
     * 自定义错误码和消息的失败响应
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return error(code, message, null);
    }
    
    /**
     * 自定义错误码、消息和数据的失败响应
     */
    public static <T> ApiResponse<T> error(Integer code, String message, T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.success = false;
        response.code = code;
        response.message = message;
        response.data = data;
        return response;
    }
    
    // ================== 链式调用方法 ==================
    
    /**
     * 设置链路追踪ID
     */
    public ApiResponse<T> withTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }

    public ApiResponse<T> withTraceId() {
        this.traceId = TraceContext.getTraceId();
        return this;
    }
    
    /**
     * 添加扩展信息
     */
    public ApiResponse<T> withExtra(String key, Object value) {
        if (this.extra == null) {
            this.extra = new HashMap<>();
        }
        this.extra.put(key, value);
        return this;
    }
    
    /**
     * 批量添加扩展信息
     */
    public ApiResponse<T> withExtra(Map<String, Object> extra) {
        if (this.extra == null) {
            this.extra = new HashMap<>();
        }
        this.extra.putAll(extra);
        return this;
    }
    
    /**
     * 设置分页信息
     */
    public ApiResponse<T> withPagination(PageInfo pagination) {
        this.pagination = pagination;
        return this;
    }
    
    // ================== Getter/Setter ==================

    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                ", traceId='" + traceId + '\'' +
                ", pagination=" + pagination +
                ", extra=" + extra +
                '}';
    }
} 