package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 套餐价格DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PackagePriceDTO {
    
    /**
     * 价格ID
     */
    private Long id;
    
    /**
     * 计费周期
     */
    private String billingCycle;
    
    /**
     * 周期数量
     */
    private Integer cycleCount;
    
    /**
     * 原价
     */
    private BigDecimal originalPrice;
    
    /**
     * 售价
     */
    private BigDecimal salePrice;
    
    /**
     * 货币
     */
    private String currency;
    
    /**
     * 折扣百分比
     */
    private Integer discountPercent;
    
    /**
     * 显示文本
     */
    private String displayText;
    
    /**
     * 套餐名称（在价格详情中使用）
     */
    private String packageName;
    
    /**
     * 套餐描述（在价格详情中使用）
     */
    private String packageDescription;
    
    /**
     * 套餐功能（在价格详情中使用）
     */
    private List<String> packageFeatures;
} 