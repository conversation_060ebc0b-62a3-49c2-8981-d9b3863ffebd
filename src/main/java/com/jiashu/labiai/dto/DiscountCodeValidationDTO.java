package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 优惠码验证DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DiscountCodeValidationDTO {
    
    /**
     * 优惠码ID
     */
    private Long discountCodeId;
    
    /**
     * 优惠码
     */
    private String discountCode;
    
    /**
     * 是否有效
     */
    private Boolean valid;
    
    /**
     * 优惠类型（FIXED-固定金额，PERCENTAGE-百分比）
     */
    private String discountType;
    
    /**
     * 优惠值
     */
    private BigDecimal discountValue;
    
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 原始金额
     */
    private BigDecimal originalAmount;
    
    /**
     * 最终金额
     */
    private BigDecimal finalAmount;
    
    /**
     * 套餐名称
     */
    private String packageName;
    
    /**
     * 计费信息
     */
    private String billingInfo;
    
    /**
     * 失败原因
     */
    private String failureReason;
} 