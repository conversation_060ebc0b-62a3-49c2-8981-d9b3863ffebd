package com.jiashu.labiai.dto.request;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备管理请求DTO
 */
@Data
public class DeviceManageRequest {
    
    /**
     * 设备ID
     */
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;
    
    /**
     * 操作类型: TRUST, UNTRUST, LOCK, UNLOCK, DELETE, RENAME
     */
    @NotNull(message = "操作类型不能为空")
    private DeviceManageAction action;
    
    /**
     * 设备名称（重命名时使用）
     */
    private String deviceName;
    
    /**
     * 操作原因
     */
    private String reason;
    
    /**
     * 是否信任设备（信任/取消信任时使用）
     */
    private Boolean trusted;
    
    public enum DeviceManageAction {
        TRUST,      // 信任设备
        UNTRUST,    // 取消信任
        LOCK,       // 锁定设备
        UNLOCK,     // 解锁设备
        DELETE,     // 删除设备
        RENAME      // 重命名设备
    }
} 