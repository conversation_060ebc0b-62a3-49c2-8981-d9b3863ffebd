package com.jiashu.labiai.dto.request.user;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 修改密码请求DTO
 */
@Data
public class ChangePasswordRequest {
    @NotBlank(message = "当前密码不能为空")
    @Length(min = 6, max = 20, message = "当前密码长度必须在6-20位之间")
    private String currentPassword;
    
    @NotBlank(message = "新密码不能为空")
    @Length(min = 6, max = 20, message = "新密码长度必须在6-20位之间")
    private String newPassword;
} 