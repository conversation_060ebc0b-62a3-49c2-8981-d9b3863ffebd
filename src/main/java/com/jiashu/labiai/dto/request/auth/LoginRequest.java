package com.jiashu.labiai.dto.request.auth;

import lombok.Data;
import org.springframework.util.StringUtils;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 登录请求DTO
 */
@Data
public class LoginRequest {
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String password;
    
    private boolean rememberMe = false;
    
    /**
     * 双因子验证码（可选）
     */
    private String twoFactorCode;
    
    public boolean hasTwoFactorCode() {
        return StringUtils.hasText(twoFactorCode);
    }
} 