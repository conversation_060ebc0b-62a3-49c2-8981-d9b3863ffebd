package com.jiashu.labiai.dto.request.auth;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 发送验证码请求DTO
 */
@Data
public class SendVerificationCodeRequest {
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Length(max = 255, message = "邮箱长度不能超过255个字符")
    private String email;
} 