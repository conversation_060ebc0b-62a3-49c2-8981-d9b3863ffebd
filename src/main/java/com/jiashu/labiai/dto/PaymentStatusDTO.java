package com.jiashu.labiai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付状态DTO
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentStatusDTO {
    
    /**
     * 支付单号
     */
    private String paymentNo;
    
    /**
     * 支付状态
     */
    private String status;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 支付时间
     */
    private LocalDateTime paidAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 