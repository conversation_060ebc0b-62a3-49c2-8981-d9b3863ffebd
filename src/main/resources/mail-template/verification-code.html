<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册验证码 - 蜡笔AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background: linear-gradient(135deg, #f1f5f9 0%, #ddd6fe 50%, #fce7f3 100%);
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
            padding: 25px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .logo {
            position: relative;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 0 auto 10px;
            transform: rotate(12deg);
            transition: transform 0.3s ease;
            animation: logoFloat 3s ease-in-out infinite;
            position: relative;
        }
        
        .logo-icon::after {
            content: '';
            position: absolute;
            top: -3px;
            right: -3px;
            width: 12px;
            height: 12px;
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            border-radius: 50%;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes logoFloat {
            0%, 100% { 
                transform: rotate(12deg) translateY(0px);
            }
            50% { 
                transform: rotate(12deg) translateY(-3px);
            }
        }
        
        @keyframes sparkle {
            0%, 100% { 
                opacity: 1;
                transform: scale(1);
            }
            50% { 
                opacity: 0.6;
                transform: scale(1.2);
            }
        }
        
        .logo-text {
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }
        
        .logo-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            margin-top: 3px;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .verification-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
            border: 2px solid #e0e7ff;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }
        
        .verification-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(
                circle at var(--x, 50%) var(--y, 50%),
                rgba(139, 92, 246, 0.1) 0%,
                rgba(236, 72, 153, 0.05) 30%,
                transparent 70%
            );
            animation: floatingGlow 4s ease-in-out infinite;
            z-index: 0;
        }
        
        .verification-card::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, 
                #8b5cf6, #ec4899, #06b6d4, #10b981, #f59e0b, #8b5cf6
            );
            background-size: 400% 400%;
            border-radius: 18px;
            z-index: -1;
            animation: gradientShift 6s ease infinite;
        }
        
        @keyframes floatingGlow {
            0%, 100% { 
                --x: 20%; --y: 30%;
                opacity: 0.3;
            }
            25% { 
                --x: 80%; --y: 20%;
                opacity: 0.6;
            }
            50% { 
                --x: 70%; --y: 80%;
                opacity: 0.4;
            }
            75% { 
                --x: 30%; --y: 70%;
                opacity: 0.7;
            }
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .verification-label {
            font-size: 14px;
            color: #8b5cf6;
            font-weight: 600;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            z-index: 1;
        }
        
        .verification-code {
            font-size: 36px;
            font-weight: bold;
            color: #1f2937;
            font-family: 'Courier New', monospace;
            letter-spacing: 8px;
            margin: 15px 0;
            position: relative;
            z-index: 1;
        }
        
        .verification-note {
            font-size: 13px;
            color: #6b7280;
            position: relative;
            z-index: 1;
        }
        
        .warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            display: flex;
            align-items: flex-start;
        }
        
        .warning-icon {
            font-size: 20px;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .warning-text {
            font-size: 14px;
            color: #92400e;
            line-height: 1.5;
        }
        
        .footer {
            background: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        
        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-link {
            display: inline-block;
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            line-height: 36px;
            margin: 0 5px;
            font-size: 18px;
            transition: transform 0.3s ease;
        }
        
        .social-link:hover {
            transform: translateY(-2px);
        }
        
        .copyright {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 20px;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 16px;
            }
            
            .header {
                padding: 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .verification-code {
                font-size: 28px;
                letter-spacing: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                <div class="logo-icon">蜡</div>
                <h1 class="logo-text">蜡笔AI</h1>
                <p class="logo-subtitle">专业AI工具平台</p>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="content">
            <div class="greeting">欢迎加入蜡笔AI！👋</div>
            
            <div class="message">
                感谢您选择蜡笔AI作为您的AI工具平台。为了完成注册流程，请使用以下验证码验证您的邮箱地址：
            </div>
            
            <!-- 验证码卡片 -->
            <div class="verification-card">
                <div class="verification-label">🔐 注册验证码</div>
                <div class="verification-code">{{VERIFICATION_CODE}}</div>
                <div class="verification-note">验证码有效期：5分钟</div>
            </div>
            
            <!-- 安全提醒 -->
            <div class="warning">
                <div class="warning-icon">⚠️</div>
                <div class="warning-text">
                    <strong>注册提示：</strong><br>
                    • 请勿将验证码透露给他人<br>
                    • 验证码仅用于完成注册，请在5分钟内使用<br>
                    • 如非本人注册，请忽略此邮件
                </div>
            </div>
            
            <div class="message">
                完成邮箱验证后，您将可以享受蜡笔AI的全部功能，包括智能创作、AI对话、数据分析等强大工具。如有任何疑问，欢迎随时联系我们的客服团队。
            </div>
        </div>
        
        <!-- 底部 -->
        <div class="footer">
            <div class="footer-text">
                欢迎加入蜡笔AI大家庭，开启您的AI创作之旅！✨
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link">🐧</a>
                <a href="#" class="social-link">📱</a>
                <a href="#" class="social-link">📧</a>
            </div>
            
            <div class="copyright">
                © 2024 蜡笔AI. 保留所有权利.<br>
                本邮件由系统自动发送，请勿直接回复。
            </div>
        </div>
    </div>
</body>
</html> 