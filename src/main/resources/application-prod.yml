server:
  port: 8080

spring:
  application:
    name: chatgpt
  datasource:
    url: *************************************************************************************************************************************************
    username: root
    password: b_Mwjf!di!rh3wf
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 5   # 最小空闲连接数量
      idle-timeout: 180000   #  空闲连接存活最大时间，默认600000（10分钟）
      maximum-pool-size: 20 # 连接池最大连接数，默认是10
      auto-commit: true # 控制从池返回的链接的默认自动提交行为，默认值：true
      pool-name: Hikari # 连接池名称
      max-lifetime: 1800000 # 控制池中链接的最长生命周期，值0表示无限生命周期，默认1800000，即30分钟
      connection-timeout: 30000 # 数据库链接超时时间，默认30秒，即30000
      connection-test-query: SELECT 1
      # 多久检查一次连接的活性
      # 检查时会先把连接从池中拿出来（空闲的话），然后调用isValid()或执行connectionTestQuery来校验活性，如果通过校验，则放回池里。
      # 默认 0 （不启用），最小值为 30000 ms，必须小于 maxLifetime。支持 JMX 动态修改
      keepalive-time: 30000

  redis:
    database: 0
    port: 6379
    host: 127.0.0.1
    password: QW_379JNmENHaaa
    lettuce:
      pool:
        #连接池中最大空闲连接数为 30。这意味着连接池可以保持最多 30 个空闲的 Redis 连接，以便在需要时重用。
        max-idle: 30
        #连接池中最小空闲连接数为 10。这表示连接池至少会保持 10 个空闲连接，以便在需要时快速获取可用连接。
        min-idle: 10
        #连接池中的最大活动连接数为 30。这是指连接池在同一时间可以支持的最大活动（使用中）连接数量。
        max-active: 30
        #当连接池已用尽且达到最大活动连接数时，从连接池获取连接的最大等待时间为 10,000 毫秒（10 秒）。如果在等待时间内没有可用连接，将抛出连接超时异常。
        max-wait: 10000
      # 应用程序关闭时Lettuce 将等待最多 3 秒钟来完成关闭操作。如果超过这个时间仍未完成，则会强制关闭连接。
      shutdown-timeout: 3000

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

my-config:
  proxyDomain: https://chatgtp.chat
  larkInfoBotId: 24c6f5d2-58f0-4656-9462-6363c6394ed2
  larkErrorBotId: 773d1938-99ef-4c65-8ca8-e5c4c6674717
  # 用户登录
  larkLoginBotId: fa14356f-0e4b-4452-b42e-3328561dbee1
  # 用户注册消息推送
  larkRegisterBotId: f326d74e-362d-43be-b677-792e9bbcb830
  # 管理员用户操作消息推送
  larkAdminBotIt: 512a5ee6-2ebb-4fad-8b74-a9b0f0e66b6e
  verification-token: NnKeuHu7b9urFL91atJ0WgmU6wLUgyNM
  encryption-key: zhangjiashu
  app-id: cli_a65b28980af0100d
  app-secret: QE4s2hfq6UsvB8CMRIOHUf4mNSGvjCAA

# MailGun 邮件服务配置
mailgun:
  #  **************************************************
  #  **************************************************
  # MailGun Private API Key - 在 Mailgun 控制台的 Settings -> API Keys 中获取
  api-key: **************************************************
  # MailGun Domain - 在 Mailgun 控制台中配置的域名
  domain: mail.jiashu.site
  # 发件人邮箱
  from-email: <EMAIL>
  # 发件人名称
  from-name: 蜡笔AI系统邮件
  # 是否使用欧盟服务器（默认使用美国服务器）
  use-eu-server: false
  # 验证码过期时间（分钟）
  verification-expire-minutes: 5
  # 验证码邮件主题格式 (default/bracket/simple/short)
  subject-format: default
  # 验证码发送频率限制配置
  # 同一邮箱发送验证码的最小间隔时间（秒）
  email-interval: 60
  # 同一设备指纹发送验证码的最小间隔时间（秒）
  fingerprint-interval: 60
  # 同一邮箱每小时最大发送次数
  email-hourly-limit: 10
  # 同一设备指纹每小时最大发送次数
  fingerprint-hourly-limit: 10
  # 同一邮箱每天最大发送次数
  email-daily-limit: 15
  # 同一设备指纹每天最大发送次数
  fingerprint-daily-limit: 15

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:mapper/*.xml


sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: access_token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: true
#  max-login-count: -1

# 支付系统配置
payment:
  # 默认配置
  default-expire-minutes: 30
  max-retry-count: 3
  # 回调基础URL
  callback-base-url: https://www.labiai.com


# 订阅配置
subscription:
  expire-notice-days: [7, 3, 1]
  grace-period-days: 3

# 优惠码配置
discount:
  max-usage-per-user: 5
  code-length: 8
  code-prefix: "SAVE"


#logging:
#  level:
#    root: INFO
#    com.jiashu: DEBUG
#    cn.dev33.satoken: INFO
#    org.springframework.data.redis: DEBUG
#    com.baomidou.mybatisplus: INFO
#  pattern:
#    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n'

