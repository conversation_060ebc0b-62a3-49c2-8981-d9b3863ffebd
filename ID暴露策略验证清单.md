# ID暴露策略验证清单

## 🎯 验证目标

确保接口中只暴露必要的业务ID，隐藏敏感的自增主键ID。

## ✅ 应该暴露的ID

### 1. 订阅ID (subscriptionId)
**原因**: 用户核心业务数据，有访问控制保护，前端可能需要

**验证接口**:
- `GET /user/subscription-info` - 当前订阅信息
- `GET /packages/{packageId}/renewal-options` - 续费选项
- `GET /packages/upgrade-options` - 升级选项

**验证点**:
```json
{
  "currentSubscription": {
    "subscriptionId": 789,  // ✅ 应该存在
    "packageId": 1,
    "packageName": "基础版"
  }
}
```

### 2. 套餐ID (packageId)
**原因**: 业务稳定，前端需要用来创建订单

**验证接口**:
- `GET /user/subscription-info` - 当前订阅信息
- `GET /packages/upgrade-options` - 升级选项
- `POST /orders/preview-renewal` - 续费预览（参数）
- `POST /orders/create-renewal` - 创建续费订单（参数）

**验证点**:
```json
{
  "currentSubscription": {
    "packageId": 1,  // ✅ 应该存在
    "packageName": "基础版"
  }
}
```

### 2. 套餐价格ID (packagePriceId)
**原因**: 前端选择价格选项时需要

**验证接口**:
- `GET /packages/{packageId}/renewal-options` - 续费选项
- `GET /packages/upgrade-options` - 升级选项
- `POST /orders/preview-renewal` - 续费预览（参数）
- `POST /orders/create-renewal` - 创建续费订单（参数）

**验证点**:
```json
{
  "renewalOptions": [
    {
      "packagePriceId": 1,  // ✅ 应该存在
      "billingCycle": "MONTH",
      "salePrice": 89.00
    }
  ]
}
```

### 3. 订单号 (orderNo)
**原因**: 业务标识符，用户友好

**验证接口**:
- `POST /orders/create-renewal` - 创建续费订单
- `POST /orders/create-upgrade` - 创建升级订单
- `GET /orders/by-order-no/{orderNo}` - 根据订单号查询
- `GET /user/subscription-info` - 订阅历史中的订单号

**验证点**:
```json
{
  "data": {
    "orderNo": "RNW20241220143025ABC124",  // ✅ 应该存在
    "packageName": "基础版",
    "finalAmount": 89.00
  }
}
```

## ❌ 不应该暴露的ID

### 1. 订单ID (orderId)
**原因**: 自增ID，容易被遍历攻击

**验证接口**:
- `POST /orders/create-renewal` - 创建续费订单
- `POST /orders/create-upgrade` - 创建升级订单
- `GET /user/subscription-info` - 订阅历史

**验证点**:
```json
{
  "data": {
    "orderId": 12345,  // ❌ 不应该存在
    "orderNo": "RNW20241220143025ABC124"
  }
}
```

### 2. 历史记录ID
**原因**: 纯内部ID，无业务意义

**验证接口**:
- `GET /user/subscription-info` - 订阅历史

**验证点**:
```json
{
  "historyList": [
    {
      "id": 123,  // ❌ 不应该存在
      "action": "RENEW",
      "orderNo": "RNW20241220143025ABC124"
    }
  ]
}
```



## 🧪 测试用例

### 测试用例1: 获取当前订阅信息
```bash
curl -X GET "http://localhost:8080/user/subscription-info" \
  -H "Cookie: access_token=test_token"
```

**验证清单**:
- [ ] 响应中包含 `subscriptionId`
- [ ] 响应中包含 `packageId`
- [ ] 历史记录中不包含 `id` 字段
- [ ] 历史记录中包含 `orderNo`

### 测试用例2: 获取续费选项
```bash
curl -X GET "http://localhost:8080/packages/1/renewal-options" \
  -H "Cookie: access_token=test_token"
```

**验证清单**:
- [ ] 续费选项中包含 `packagePriceId`
- [ ] 当前订阅信息中包含 `subscriptionId`
- [ ] 当前订阅信息中包含套餐名称

### 测试用例3: 创建续费订单
```bash
curl -X POST "http://localhost:8080/orders/create-renewal" \
  -H "Cookie: access_token=test_token" \
  -d "packagePriceId=1"
```

**验证清单**:
- [ ] 响应中包含 `orderNo`
- [ ] 响应中不包含 `orderId`
- [ ] 订单号格式正确（RNW开头）

### 测试用例4: 获取升级选项
```bash
curl -X GET "http://localhost:8080/packages/upgrade-options" \
  -H "Cookie: access_token=test_token"
```

**验证清单**:
- [ ] 当前订阅中包含 `subscriptionId`
- [ ] 当前订阅中包含 `packageId`
- [ ] 升级选项中包含 `packageId`
- [ ] 价格选项中包含 `packagePriceId`

### 测试用例5: 创建升级订单
```bash
curl -X POST "http://localhost:8080/orders/create-upgrade" \
  -H "Cookie: access_token=test_token" \
  -d "targetPackagePriceId=3"
```

**验证清单**:
- [ ] 响应中包含 `orderNo`
- [ ] 响应中不包含 `orderId`
- [ ] 订单号格式正确（UPG开头）

## 🔍 安全验证

### 1. 遍历攻击防护
尝试通过ID遍历获取其他用户数据：

```bash
# 这些请求应该失败或返回空结果
curl -X GET "http://localhost:8080/orders/12345"  # 直接访问订单ID
curl -X GET "http://localhost:8080/subscriptions/789"  # 直接访问订阅ID
```

### 2. 信息泄露检查
检查响应中是否包含敏感ID：

```bash
# 检查所有接口响应
grep -r "orderId\|subscriptionId\|\"id\":" response_logs/
```

### 3. 业务功能验证
确保移除ID后业务功能正常：

- [ ] 续费功能正常工作
- [ ] 升级功能正常工作
- [ ] 订单查询正常工作
- [ ] 支付流程正常工作

## 📋 验证报告模板

### 接口验证结果

| 接口 | 应该包含的ID | 不应该包含的ID | 验证结果 | 备注 |
|------|-------------|---------------|----------|------|
| GET /user/subscription-info | subscriptionId, packageId | historyId | ✅/❌ | |
| GET /packages/{id}/renewal-options | subscriptionId, packagePriceId | - | ✅/❌ | |
| POST /orders/create-renewal | orderNo | orderId | ✅/❌ | |
| GET /packages/upgrade-options | subscriptionId, packageId, packagePriceId | - | ✅/❌ | |
| POST /orders/create-upgrade | orderNo | orderId | ✅/❌ | |

### 安全验证结果

| 验证项 | 结果 | 说明 |
|--------|------|------|
| 遍历攻击防护 | ✅/❌ | 无法通过ID遍历获取其他用户数据 |
| 信息泄露检查 | ✅/❌ | 响应中不包含敏感ID |
| 业务功能完整性 | ✅/❌ | 所有业务功能正常工作 |

### 总结

- **通过验证的接口数**: X/Y
- **发现的问题**: 
- **修复建议**: 
- **整体评估**: 安全/需要改进

这个验证清单确保了ID暴露策略的正确实施，既保护了敏感信息，又保持了业务功能的完整性。
