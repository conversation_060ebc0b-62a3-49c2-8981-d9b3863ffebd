# LabIAI API 接口清单

## 基础信息
- **Base URL**: `https://www.labiai.com`
- **认证方式**: Sa-Token (Cookie-based)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 1. 认证相关接口 `/auth`

### 1.1 用户登录
- **接口**: `POST /auth/login`
- **描述**: 用户邮箱密码登录
- **认证**: 无需认证
- **参数**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }
  ```
- **响应**: 登录成功后设置Cookie

### 1.2 用户注销
- **接口**: `POST /auth/logout`
- **描述**: 用户注销登录
- **认证**: 需要登录
- **参数**: 无
- **响应**: 清除Cookie

### 1.3 强制下线其他会话
- **接口**: `POST /auth/logout-others`
- **描述**: 强制下线除当前会话外的所有其他会话
- **认证**: 需要登录
- **参数**: 无

## 2. 用户注册接口 `/register`

### 2.1 初始化会话
- **接口**: `POST /register/init-session`
- **描述**: 初始化注册会话，获取会话令牌
- **认证**: 无需认证
- **请求头**: 需要设备指纹信息
- **响应**: 返回sessionToken和nonce

### 2.2 发送验证码
- **接口**: `POST /register/send-verification-code`
- **描述**: 向指定邮箱发送注册验证码
- **认证**: 需要会话令牌
- **参数**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```

### 2.3 完成注册
- **接口**: `POST /register/submit`
- **描述**: 验证验证码并创建用户账户
- **认证**: 需要会话令牌
- **参数**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "code": "123456"
  }
  ```

## 3. 用户管理接口 `/user`

### 3.1 获取用户信息
- **接口**: `GET /user/me`
- **描述**: 获取当前用户信息
- **认证**: 需要登录
- **参数**: 无

### 3.2 修改密码
- **接口**: `POST /user/change-password`
- **描述**: 修改用户密码
- **认证**: 需要登录
- **参数**:
  ```json
  {
    "oldPassword": "oldpass123",
    "newPassword": "newpass123",
    "confirmPassword": "newpass123"
  }
  ```

### 3.3 获取用户订阅信息
- **接口**: `GET /user/subscription`
- **描述**: 获取用户当前订阅信息
- **认证**: 需要登录
- **参数**: 无

## 4. 设备管理接口 `/devices`

### 4.1 获取设备列表
- **接口**: `GET /devices`
- **描述**: 获取用户所有设备列表
- **认证**: 需要登录
- **参数**: 
  - `page` (可选): 页码，默认1
  - `size` (可选): 每页数量，默认10

### 4.2 获取设备详情
- **接口**: `GET /devices/{deviceId}`
- **描述**: 获取指定设备详细信息
- **认证**: 需要登录
- **参数**: 路径参数 `deviceId`

### 4.3 设置设备信任状态
- **接口**: `PUT /devices/{deviceId}/trust`
- **描述**: 设置设备为受信任或不受信任
- **认证**: 需要登录
- **参数**:
  ```json
  {
    "trusted": true,
    "reason": "常用设备"
  }
  ```

### 4.4 删除设备
- **接口**: `DELETE /devices/{deviceId}`
- **描述**: 删除指定设备
- **认证**: 需要登录
- **参数**: 路径参数 `deviceId`

### 4.5 获取设备指纹
- **接口**: `GET /devices/fingerprint`
- **描述**: 获取当前设备指纹信息
- **认证**: 无需认证
- **参数**: 无

## 5. 套餐相关接口 `/packages`

### 5.1 获取套餐列表
- **接口**: `GET /packages`
- **描述**: 获取所有可用套餐及价格
- **认证**: 无需认证
- **参数**: 无

### 5.2 获取套餐详情
- **接口**: `GET /packages/{id}`
- **描述**: 获取指定套餐详情
- **认证**: 无需认证
- **参数**: 路径参数 `id`

### 5.3 获取套餐价格选项
- **接口**: `GET /packages/{id}/prices`
- **描述**: 获取指定套餐的价格选项
- **认证**: 无需认证
- **参数**: 路径参数 `id`

## 6. 订单管理接口 `/orders`

### 6.1 预验证优惠码
- **接口**: `POST /orders/preview-discount`
- **描述**: 在创建订单前预验证优惠码
- **认证**: 需要登录
- **参数**:
  ```
  Content-Type: application/x-www-form-urlencoded
  discountCode=SAVE20&packagePriceId=123
  ```

### 6.2 创建订单
- **接口**: `POST /orders/create`
- **描述**: 创建新订单，可选择使用优惠码
- **认证**: 需要登录
- **参数**:
  ```
  Content-Type: application/x-www-form-urlencoded
  packagePriceId=123&discountCode=SAVE20
  ```

### 6.3 获取订单列表
- **接口**: `GET /orders`
- **描述**: 获取当前用户的订单列表
- **认证**: 需要登录
- **参数**:
  - `status` (可选): 订单状态筛选
  - `page` (可选): 页码，默认1
  - `size` (可选): 每页数量，默认10

### 6.4 根据订单ID查询
- **接口**: `GET /orders/{orderId}`
- **描述**: 根据订单ID查询订单详情
- **认证**: 需要登录
- **参数**: 路径参数 `orderId`

### 6.5 根据订单号查询
- **接口**: `GET /orders/order-no/{orderNo}`
- **描述**: 根据订单号查询订单详情
- **认证**: 需要登录
- **参数**: 路径参数 `orderNo`

### 6.6 取消订单
- **接口**: `PUT /orders/{orderId}/cancel`
- **描述**: 取消指定的订单
- **认证**: 需要登录
- **参数**: 路径参数 `orderId`

### 6.7 删除订单
- **接口**: `DELETE /orders/{orderId}`
- **描述**: 删除指定的订单（逻辑删除）
- **认证**: 需要登录
- **参数**: 路径参数 `orderId`

### 6.8 获取订单优惠码详情
- **接口**: `GET /orders/{orderId}/discount-info`
- **描述**: 获取订单的优惠码使用详情
- **认证**: 需要登录
- **参数**: 路径参数 `orderId`

## 7. 支付相关接口 `/payment`

### 7.1 获取支付方式
- **接口**: `GET /payment/methods`
- **描述**: 获取可用支付方式列表
- **认证**: 无需认证
- **参数**:
  - `amount` (可选): 支付金额，默认0.01

### 7.2 创建支付
- **接口**: `POST /payment/create`
- **描述**: 创建支付订单
- **认证**: 需要登录
- **参数**:
  ```
  Content-Type: application/x-www-form-urlencoded
  packagePriceId=1&methodCode=xunhupay_auto
  ```

### 7.3 支付成功回跳
- **接口**: `GET /payment/success`
- **描述**: 支付成功后的回跳页面
- **认证**: 无需认证
- **参数**:
  - `trade_order_id` (可选): 商户订单号

### 7.4 支付取消回跳
- **接口**: `GET /payment/cancel`
- **描述**: 支付取消后的回跳页面
- **认证**: 无需认证
- **参数**:
  - `trade_order_id` (可选): 商户订单号

### 7.5 支付回调（内部）
- **接口**: `POST /payment/notify/xunhupay`
- **描述**: 虎皮椒支付平台回调接口
- **认证**: 签名验证
- **参数**: 支付平台回调参数

## 8. 优惠码相关接口 `/discount`

### 8.1 验证优惠码
- **接口**: `POST /discount/validate`
- **描述**: 验证优惠码有效性
- **认证**: 需要登录
- **参数**:
  ```json
  {
    "code": "SAVE20",
    "packagePriceId": 123
  }
  ```

## 请求头说明

### 通用请求头
```http
Content-Type: application/json
User-Agent: <浏览器用户代理>
```

### 设备指纹请求头（注册流程必需）
```http
X-Visitor-ID: <32位十六进制设备指纹ID>
X-Device-Hash: <SHA-256设备哈希>
```

### 会话认证请求头（注册流程）
```http
Authorization: Bearer <sessionToken>
X-Session-Nonce: <当前nonce>
```

## 响应格式

### 成功响应
```json
{
  "code": 20000,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

### 错误响应
```json
{
  "code": 40001,
  "message": "参数校验失败",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

## 状态码说明

- **2xxxx**: 成功状态
- **4xxxx**: 客户端错误
- **5xxxx**: 服务端错误
- **6xxxx**: 业务逻辑错误

详细错误码请参考主文档的错误码体系部分。
