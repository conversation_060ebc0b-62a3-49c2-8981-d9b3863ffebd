# 续费与订阅升级功能 - 后端接口开发清单

## 开发概述

基于现有的订阅页面功能，需要新增续费和订阅升级相关的后端接口。本文档列出了所有需要开发的接口、优先级和实现要点。

## 接口开发清单

### 🔥 Phase 1: 核心续费功能（高优先级）

#### 1.1 获取续费选项
- **接口**: `GET /subscription/renewal-options`
- **功能**: 获取当前订阅的续费选项和价格
- **认证**: 需要登录
- **实现要点**:
  - 检查用户是否有活跃订阅
  - 返回同套餐的不同计费周期选项
  - 计算续费后的新到期时间
  - 显示到期提醒状态

#### 1.2 预览续费价格
- **接口**: `POST /subscription/preview-renewal`
- **功能**: 预览续费价格，支持优惠码验证
- **认证**: 需要登录
- **参数**: `packagePriceId`, `discountCode`(可选)
- **实现要点**:
  - 验证套餐价格有效性
  - 应用优惠码折扣
  - 计算最终支付金额
  - 返回详细价格明细

#### 1.3 创建续费订单
- **接口**: `POST /subscription/create-renewal-order`
- **功能**: 创建续费订单
- **认证**: 需要登录
- **参数**: `packagePriceId`, `discountCode`(可选)
- **实现要点**:
  - 创建 `RENEWAL` 类型订单
  - 关联当前订阅ID
  - 消费优惠码使用次数
  - 设置订单过期时间

#### 1.4 增强订阅详情接口
- **接口**: `GET /subscription/details` (新增或增强现有接口)
- **功能**: 获取详细订阅信息，包含续费提示
- **认证**: 需要登录
- **实现要点**:
  - 返回订阅状态和剩余天数
  - 显示是否需要续费提醒
  - 提供快捷操作按钮状态
  - 包含使用情况统计

### 🚀 Phase 2: 订阅升级功能（中优先级）

#### 2.1 获取升级选项
- **接口**: `GET /subscription/upgrade-options`
- **功能**: 获取当前订阅可升级的套餐选项
- **认证**: 需要登录
- **实现要点**:
  - 获取比当前套餐更高级的选项
  - 计算升级所需费用
  - 提供降级选项（下个周期生效）
  - 显示权益对比

#### 2.2 预览升级价格
- **接口**: `POST /subscription/preview-upgrade`
- **功能**: 预览升级价格和时间计算
- **认证**: 需要登录
- **参数**: `targetPackageId`, `targetPackagePriceId`, `discountCode`(可选)
- **实现要点**:
  - 计算当前订阅剩余价值
  - 计算升级差价
  - 应用优惠码
  - 返回详细计算过程

#### 2.3 创建升级订单
- **接口**: `POST /subscription/create-upgrade-order`
- **功能**: 创建升级订单
- **认证**: 需要登录
- **参数**: `targetPackageId`, `targetPackagePriceId`, `discountCode`(可选)
- **实现要点**:
  - 创建 `UPGRADE` 类型订单
  - 记录原套餐和目标套餐
  - 计算补差价金额
  - 设置立即生效标识

### ⚙️ Phase 3: 订阅管理功能（中优先级）

#### 3.1 设置自动续费
- **接口**: `PUT /subscription/auto-renewal`
- **功能**: 开启或关闭自动续费
- **认证**: 需要登录
- **参数**: `autoRenewal`, `renewalPackagePriceId`(可选)
- **实现要点**:
  - 更新订阅的自动续费设置
  - 记录续费套餐选择
  - 发送设置确认通知

#### 3.2 取消订阅
- **接口**: `POST /subscription/cancel`
- **功能**: 取消订阅（下个计费周期生效）
- **认证**: 需要登录
- **参数**: `reason`, `feedback`(可选)
- **实现要点**:
  - 设置订阅在到期后不续费
  - 记录取消原因
  - 发送取消确认邮件
  - 可选：提供挽留优惠

#### 3.3 获取订阅历史
- **接口**: `GET /subscription/history`
- **功能**: 获取用户订阅变更历史
- **认证**: 需要登录
- **参数**: `page`, `size`
- **实现要点**:
  - 分页返回订阅历史记录
  - 包含操作类型和时间
  - 显示套餐变更详情

### 🔧 Phase 4: 支持功能（低优先级）

#### 4.1 订阅使用统计
- **接口**: `GET /subscription/usage-stats`
- **功能**: 获取订阅使用情况统计
- **认证**: 需要登录
- **实现要点**:
  - 返回各项功能使用情况
  - 计算使用率和剩余额度
  - 提供使用趋势分析

#### 4.2 续费提醒设置
- **接口**: `PUT /subscription/reminder-settings`
- **功能**: 设置续费提醒偏好
- **认证**: 需要登录
- **参数**: `emailReminder`, `smsReminder`, `reminderDays`
- **实现要点**:
  - 保存用户提醒偏好
  - 支持自定义提醒时间
  - 支持多种提醒方式

## 数据库变更需求

### 1. 订单表增强
```sql
-- 为 orders 表添加新字段
ALTER TABLE orders ADD COLUMN order_type ENUM('NEW','RENEWAL','UPGRADE','DOWNGRADE') DEFAULT 'NEW' COMMENT '订单类型';
ALTER TABLE orders ADD COLUMN from_subscription_id BIGINT NULL COMMENT '原订阅ID（续费/升级时）';
ALTER TABLE orders ADD COLUMN target_package_id BIGINT NULL COMMENT '目标套餐ID（升级时）';
ALTER TABLE orders ADD COLUMN effective_immediately TINYINT(1) DEFAULT 1 COMMENT '是否立即生效';

-- 添加索引
ALTER TABLE orders ADD INDEX idx_order_type (order_type);
ALTER TABLE orders ADD INDEX idx_from_subscription_id (from_subscription_id);
```

### 2. 订阅表增强
```sql
-- 为 user_subscriptions 表添加字段
ALTER TABLE user_subscriptions ADD COLUMN auto_renewal TINYINT(1) DEFAULT 0 COMMENT '是否自动续费';
ALTER TABLE user_subscriptions ADD COLUMN renewal_package_price_id BIGINT NULL COMMENT '续费套餐价格ID';
ALTER TABLE user_subscriptions ADD COLUMN cancel_at_period_end TINYINT(1) DEFAULT 0 COMMENT '是否在周期结束时取消';
ALTER TABLE user_subscriptions ADD COLUMN cancellation_reason VARCHAR(500) NULL COMMENT '取消原因';

-- 添加索引
ALTER TABLE user_subscriptions ADD INDEX idx_auto_renewal (auto_renewal);
ALTER TABLE user_subscriptions ADD INDEX idx_cancel_at_period_end (cancel_at_period_end);
```

### 3. 新增提醒设置表
```sql
CREATE TABLE subscription_reminder_settings (
    id BIGINT NOT NULL AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    email_reminder TINYINT(1) DEFAULT 1 COMMENT '邮件提醒',
    sms_reminder TINYINT(1) DEFAULT 0 COMMENT '短信提醒',
    reminder_days JSON DEFAULT NULL COMMENT '提醒天数设置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    CONSTRAINT fk_reminder_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订阅提醒设置表';
```

## 核心业务逻辑

### 1. 续费价格计算
```java
public class RenewalCalculator {
    /**
     * 计算续费价格
     * @param currentSubscription 当前订阅
     * @param packagePrice 续费套餐价格
     * @param discountCode 优惠码
     * @return 续费价格详情
     */
    public RenewalPriceDetail calculateRenewalPrice(
        UserSubscription currentSubscription,
        PackagePrice packagePrice,
        String discountCode
    ) {
        // 1. 基础价格
        BigDecimal basePrice = packagePrice.getSalePrice();
        
        // 2. 应用优惠码
        BigDecimal discountAmount = applyDiscountCode(discountCode, basePrice);
        
        // 3. 计算最终价格
        BigDecimal finalPrice = basePrice.subtract(discountAmount);
        
        // 4. 计算新到期时间
        LocalDateTime newEndTime = calculateNewEndTime(currentSubscription, packagePrice);
        
        return RenewalPriceDetail.builder()
            .basePrice(basePrice)
            .discountAmount(discountAmount)
            .finalPrice(finalPrice)
            .newEndTime(newEndTime)
            .build();
    }
}
```

### 2. 升级价格计算
```java
public class UpgradeCalculator {
    /**
     * 计算升级价格
     * @param currentSubscription 当前订阅
     * @param targetPackagePrice 目标套餐价格
     * @return 升级价格详情
     */
    public UpgradePriceDetail calculateUpgradePrice(
        UserSubscription currentSubscription,
        PackagePrice targetPackagePrice
    ) {
        // 1. 计算当前订阅剩余价值
        BigDecimal remainingValue = calculateRemainingValue(currentSubscription);
        
        // 2. 计算目标套餐对应时间的价格
        BigDecimal targetPrice = calculateProportionalPrice(targetPackagePrice, getRemainingDays(currentSubscription));
        
        // 3. 计算差价
        BigDecimal priceDifference = targetPrice.subtract(remainingValue);
        
        return UpgradePriceDetail.builder()
            .remainingValue(remainingValue)
            .targetPrice(targetPrice)
            .priceDifference(priceDifference)
            .build();
    }
}
```

### 3. 订阅状态管理
```java
public class SubscriptionStatusManager {
    /**
     * 处理续费后的订阅更新
     */
    public void processRenewalSuccess(Order renewalOrder) {
        UserSubscription subscription = getSubscriptionById(renewalOrder.getFromSubscriptionId());
        PackagePrice packagePrice = getPackagePriceById(renewalOrder.getPackagePriceId());
        
        // 更新到期时间
        LocalDateTime newEndTime = calculateNewEndTime(subscription, packagePrice);
        subscription.setEndTime(newEndTime);
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        
        // 保存更新
        updateSubscription(subscription);
        
        // 记录历史
        recordSubscriptionHistory(subscription, renewalOrder, SubscriptionAction.RENEW);
    }
    
    /**
     * 处理升级后的订阅更新
     */
    public void processUpgradeSuccess(Order upgradeOrder) {
        UserSubscription subscription = getSubscriptionById(upgradeOrder.getFromSubscriptionId());
        
        // 更新套餐
        subscription.setPackageId(upgradeOrder.getTargetPackageId());
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        
        // 保存更新
        updateSubscription(subscription);
        
        // 记录历史
        recordSubscriptionHistory(subscription, upgradeOrder, SubscriptionAction.UPGRADE);
    }
}
```

## 定时任务需求

### 1. 到期提醒任务
```java
@Scheduled(cron = "0 0 9 * * ?") // 每天9点执行
public void sendExpirationReminders() {
    // 查询需要提醒的订阅
    List<UserSubscription> subscriptions = findSubscriptionsNeedingReminder();
    
    for (UserSubscription subscription : subscriptions) {
        sendReminderEmail(subscription);
        sendReminderNotification(subscription);
    }
}
```

### 2. 自动续费任务
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天2点执行
public void processAutoRenewals() {
    // 查询需要自动续费的订阅
    List<UserSubscription> subscriptions = findAutoRenewalSubscriptions();
    
    for (UserSubscription subscription : subscriptions) {
        try {
            processAutoRenewal(subscription);
        } catch (Exception e) {
            handleAutoRenewalFailure(subscription, e);
        }
    }
}
```

## 开发优先级建议

### 第一阶段（1-2周）
1. ✅ 续费选项接口
2. ✅ 续费预览接口  
3. ✅ 续费订单创建
4. ✅ 订阅详情增强

### 第二阶段（2-3周）
1. ✅ 升级选项接口
2. ✅ 升级预览接口
3. ✅ 升级订单创建
4. ✅ 支付成功后的续费/升级处理

### 第三阶段（1-2周）
1. ✅ 自动续费设置
2. ✅ 订阅取消功能
3. ✅ 订阅历史查询
4. ✅ 到期提醒任务

### 第四阶段（1周）
1. ✅ 使用统计接口
2. ✅ 提醒设置接口
3. ✅ 自动续费任务
4. ✅ 监控和告警

## 测试要点

### 1. 功能测试
- 续费价格计算准确性
- 升级价格计算准确性
- 优惠码应用正确性
- 订阅状态流转正确性

### 2. 边界测试
- 订阅已过期的续费
- 套餐不存在的升级
- 优惠码过期或无效
- 并发订单创建

### 3. 集成测试
- 支付成功后的订阅更新
- 定时任务执行正确性
- 邮件通知发送
- 数据一致性验证

这个开发清单提供了完整的实现路径，建议按照优先级逐步开发，确保核心功能优先上线。
