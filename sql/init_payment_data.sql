-- 支付系统初始化数据
-- 在执行此脚本前，请确保已经执行了 payment_tables.sql

USE labiai;

-- 初始化虎皮椒支付方式配置
-- 请将 your_xunhupay_app_id 和 your_xunhupay_app_secret 替换为真实的虎皮椒配置
UPDATE payment_methods 
SET api_config = JSON_OBJECT(
    'app_id', 'your_xunhupay_app_id',
    'app_secret', 'your_xunhupay_app_secret', 
    'api_url', 'https://api.xunhupay.com/payment/do.html'
)
WHERE method_code = 'xunhupay_auto';

-- 可以添加更多支付方式配置示例
-- 微信支付（如果需要单独配置）
INSERT INTO payment_methods (
    method_code, method_name, method_type, provider, 
    icon_url, description, api_config, webhook_config, 
    min_amount, max_amount, status, priority, sort_order
) VALUES (
    'xunhupay_wechat', 
    '微信支付', 
    'WECHAT', 
    'xunhupay',
    'https://example.com/icons/wechat.png',
    '微信扫码支付',
    '{"app_id": "your_wechat_app_id", "app_secret": "your_wechat_app_secret", "api_url": "https://api.xunhupay.com/payment/do.html"}',
    '{"notify_url": "/api/payment/notify/xunhupay", "verify_sign": true}',
    0.01, 50000.00, 1, 90, 2
) ON DUPLICATE KEY UPDATE
    method_name = VALUES(method_name),
    api_config = VALUES(api_config);

-- 支付宝（如果需要单独配置）
INSERT INTO payment_methods (
    method_code, method_name, method_type, provider, 
    icon_url, description, api_config, webhook_config, 
    min_amount, max_amount, status, priority, sort_order
) VALUES (
    'xunhupay_alipay', 
    '支付宝', 
    'ALIPAY', 
    'xunhupay',
    'https://example.com/icons/alipay.png',
    '支付宝扫码支付',
    '{"app_id": "your_alipay_app_id", "app_secret": "your_alipay_app_secret", "api_url": "https://api.xunhupay.com/payment/do.html"}',
    '{"notify_url": "/api/payment/notify/xunhupay", "verify_sign": true}',
    0.01, 50000.00, 1, 85, 3
) ON DUPLICATE KEY UPDATE
    method_name = VALUES(method_name),
    api_config = VALUES(api_config);

-- 验证数据
SELECT 
    p.display_name AS package_name,
    pp.billing_cycle,
    pp.cycle_count,
    pp.original_price,
    pp.sale_price,
    pp.currency
FROM packages p
JOIN package_prices pp ON p.id = pp.package_id
WHERE p.status = 1 AND pp.status = 1
ORDER BY p.sort_order, pp.billing_cycle;

SELECT 
    method_code,
    method_name,
    provider,
    status,
    priority
FROM payment_methods
WHERE status = 1
ORDER BY priority DESC, sort_order;

-- 显示配置建议
SELECT 
    '配置建议' AS note,
    '请在数据库中更新payment_methods表的api_config字段，填入真实的虎皮椒配置信息' AS suggestion
UNION ALL
SELECT 
    '配置位置' AS note,
    '虎皮椒商户后台 -> 开发者中心 -> API信息' AS suggestion
UNION ALL
SELECT 
    '必需参数' AS note,
    'app_id: 应用ID, app_secret: 应用密钥' AS suggestion; 