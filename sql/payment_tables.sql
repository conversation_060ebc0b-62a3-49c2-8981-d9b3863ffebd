-- 支付相关数据库表
-- 基于现有的labiai.sql扩展支付功能

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for packages
-- ----------------------------
DROP TABLE IF EXISTS `packages`;
CREATE TABLE `packages` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '套餐名称(basic/plus/pro)',
    `display_name` varchar(100) NOT NULL COMMENT '显示名称',
    `description` text COMMENT '套餐描述',
    `features` json COMMENT '功能特性列表',
    `status` tinyint DEFAULT 1 COMMENT '1:启用 0:禁用',
    `sort_order` int DEFAULT 0 COMMENT '排序',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_package_name` (`name`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐表';

-- ----------------------------
-- Table structure for package_prices
-- ----------------------------
DROP TABLE IF EXISTS `package_prices`;
CREATE TABLE `package_prices` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `package_id` bigint NOT NULL,
    `billing_cycle` enum('DAY','MONTH','QUARTER','YEAR') NOT NULL,
    `cycle_count` int DEFAULT 1 COMMENT '周期数量，如3个月则为3',
    `original_price` decimal(10,2) NOT NULL COMMENT '原价',
    `sale_price` decimal(10,2) NOT NULL COMMENT '售价',
    `currency` varchar(3) DEFAULT 'CNY',
    `status` tinyint DEFAULT 1,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`package_id`) REFERENCES `packages`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `uk_package_cycle` (`package_id`, `billing_cycle`, `cycle_count`),
    KEY `idx_package_id` (`package_id`),
    KEY `idx_billing_cycle` (`billing_cycle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐价格表';

-- ----------------------------
-- Table structure for discount_codes
-- ----------------------------
DROP TABLE IF EXISTS `discount_codes`;
CREATE TABLE `discount_codes` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `code` varchar(50) UNIQUE NOT NULL,
    `name` varchar(100) NOT NULL COMMENT '优惠码名称',
    `type` enum('FIXED','PERCENTAGE') NOT NULL COMMENT '固定金额/百分比',
    `value` decimal(10,2) NOT NULL COMMENT '优惠值',
    `usage_type` enum('SINGLE','MULTIPLE') NOT NULL COMMENT '一码一用/一码多用',
    `max_usage` int DEFAULT 1 COMMENT '最大使用次数',
    `used_count` int DEFAULT 0 COMMENT '已使用次数',
    `min_amount` decimal(10,2) DEFAULT 0 COMMENT '最小使用金额',
    `max_discount` decimal(10,2) COMMENT '最大优惠金额(百分比折扣用)',
    `applicable_packages` json COMMENT '适用套餐ID数组，null表示全部',
    `start_time` timestamp NULL COMMENT '开始时间',
    `end_time` timestamp NULL COMMENT '结束时间',
    `status` tinyint DEFAULT 1,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠码表';

-- ----------------------------
-- Table structure for payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `method_code` varchar(50) UNIQUE NOT NULL COMMENT '支付方式代码',
    `method_name` varchar(100) NOT NULL COMMENT '支付方式名称',
    `method_type` enum('GATEWAY','ALIPAY','WECHAT','UNIONPAY','BANK') NOT NULL COMMENT '支付类型',
    `provider` varchar(50) NOT NULL COMMENT '支付提供商(xunhupay, alipay, wechat)',
    `icon_url` varchar(500) COMMENT '图标URL',
    `description` varchar(500) COMMENT '描述信息',
    `api_config` json NOT NULL COMMENT '支付API配置',
    `webhook_config` json COMMENT '回调配置',
    `extra_config` json COMMENT '额外配置参数',
    `min_amount` decimal(10,2) DEFAULT 0.01 COMMENT '最小支付金额',
    `max_amount` decimal(10,2) COMMENT '最大支付金额',
    `status` tinyint DEFAULT 1 COMMENT '1:启用 0:禁用',
    `priority` int DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    `sort_order` int DEFAULT 0 COMMENT '显示排序',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_method_code` (`method_code`),
    KEY `idx_provider` (`provider`),
    KEY `idx_status` (`status`),
    KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式表';

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `order_no` varchar(32) UNIQUE NOT NULL COMMENT '订单号',
    `user_id` bigint NOT NULL,
    `package_id` bigint NOT NULL,
    `package_price_id` bigint NOT NULL,
    `discount_code_id` bigint NULL COMMENT '使用的优惠码ID',
    `original_amount` decimal(10,2) NOT NULL COMMENT '原价',
    `discount_amount` decimal(10,2) DEFAULT 0 COMMENT '优惠金额',
    `final_amount` decimal(10,2) NOT NULL COMMENT '最终金额',
    `currency` varchar(3) DEFAULT 'CNY',
    `billing_cycle` enum('DAY','MONTH','QUARTER','YEAR') NOT NULL,
    `cycle_count` int NOT NULL,
    `status` enum('PENDING','PAID','CANCELLED','REFUNDED','PARTIAL_REFUNDED') DEFAULT 'PENDING',
    `order_type` enum('NEW','RENEWAL','UPGRADE') DEFAULT 'NEW',
    `metadata` json COMMENT '订单元数据',
    `expired_at` timestamp NULL COMMENT '订单过期时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`package_id`) REFERENCES `packages`(`id`),
    FOREIGN KEY (`package_price_id`) REFERENCES `package_prices`(`id`),
    FOREIGN KEY (`discount_code_id`) REFERENCES `discount_codes`(`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_order_type` (`order_type`),
    KEY `idx_expired_at` (`expired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- ----------------------------
-- Table structure for payments
-- ----------------------------
DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `payment_no` varchar(32) UNIQUE NOT NULL COMMENT '支付流水号',
    `order_id` bigint NOT NULL,
    `user_id` bigint NOT NULL,
    `payment_method_id` bigint NOT NULL COMMENT '支付方式ID',
    `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
    `currency` varchar(3) DEFAULT 'CNY',
    `trade_order_id` varchar(32) NOT NULL COMMENT '商户订单号(发给支付平台)',
    `transaction_id` varchar(64) COMMENT '支付平台交易号',
    `platform_order_id` varchar(64) COMMENT '第三方平台订单号',
    `payment_url` varchar(1000) COMMENT '支付跳转URL',
    `qr_code_url` varchar(1000) COMMENT '二维码URL',
    `status` enum('PENDING','SUCCESS','FAILED','CANCELLED','EXPIRED') DEFAULT 'PENDING',
    `failure_reason` varchar(500) COMMENT '失败原因',
    `callback_data` json COMMENT '支付回调原始数据',
    `client_ip` varchar(45) COMMENT '客户端IP',
    `user_agent` varchar(1000) COMMENT '用户代理',
    `paid_at` timestamp NULL,
    `expired_at` timestamp NULL COMMENT '支付过期时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`),
    FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods`(`id`),
    KEY `idx_trade_order_id` (`trade_order_id`),
    KEY `idx_status` (`status`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- ----------------------------
-- Table structure for user_subscriptions
-- ----------------------------
DROP TABLE IF EXISTS `user_subscriptions`;
CREATE TABLE `user_subscriptions` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `user_id` bigint NOT NULL,
    `package_id` bigint NOT NULL,
    `order_id` bigint NOT NULL COMMENT '首次订阅的订单ID',
    `status` enum('ACTIVE','EXPIRED','CANCELLED') DEFAULT 'ACTIVE',
    `start_time` timestamp NOT NULL,
    `end_time` timestamp NOT NULL,
    `auto_renewal` tinyint DEFAULT 0 COMMENT '是否自动续费',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`package_id`) REFERENCES `packages`(`id`),
    FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
    UNIQUE KEY `uk_user_subscription` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅表';

-- ----------------------------
-- Table structure for subscription_history
-- ----------------------------
DROP TABLE IF EXISTS `subscription_history`;
CREATE TABLE `subscription_history` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `user_id` bigint NOT NULL,
    `subscription_id` bigint NOT NULL,
    `order_id` bigint NOT NULL,
    `action` enum('CREATE','RENEW','UPGRADE','DOWNGRADE','CANCEL') NOT NULL,
    `from_package_id` bigint COMMENT '原套餐ID(升级降级时)',
    `to_package_id` bigint NOT NULL,
    `from_end_time` timestamp COMMENT '原订阅结束时间',
    `to_end_time` timestamp NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`subscription_id`) REFERENCES `user_subscriptions`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅历史表';

-- ----------------------------
-- Table structure for refunds
-- ----------------------------
DROP TABLE IF EXISTS `refunds`;
CREATE TABLE `refunds` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `refund_no` varchar(32) UNIQUE NOT NULL,
    `order_id` bigint NOT NULL,
    `payment_id` bigint NOT NULL,
    `user_id` bigint NOT NULL,
    `refund_amount` decimal(10,2) NOT NULL,
    `refund_reason` varchar(500),
    `refund_type` enum('FULL','PARTIAL') NOT NULL,
    `status` enum('PENDING','SUCCESS','FAILED') DEFAULT 'PENDING',
    `platform_refund_id` varchar(64) COMMENT '支付平台退款单号',
    `callback_data` json COMMENT '退款回调数据',
    `processed_at` timestamp NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_no` (`refund_no`),
    FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`payment_id`) REFERENCES `payments`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    KEY `idx_status` (`status`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- ----------------------------
-- Table structure for discount_code_usage
-- ----------------------------
DROP TABLE IF EXISTS `discount_code_usage`;
CREATE TABLE `discount_code_usage` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `discount_code_id` bigint NOT NULL,
    `user_id` bigint NOT NULL,
    `order_id` bigint NOT NULL,
    `discount_amount` decimal(10,2) NOT NULL,
    `used_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`discount_code_id`) REFERENCES `discount_codes`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
    KEY `idx_discount_code_id` (`discount_code_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠码使用记录表';

SET FOREIGN_KEY_CHECKS = 1;

-- 初始化基础数据
-- 基础套餐
INSERT INTO packages (name, display_name, description, features, status, sort_order) VALUES 
('basic', '基础版', '适合个人用户', '["基础功能", "邮件支持", "5GB存储"]', 1, 1),
('plus', '进阶版', '适合小团队', '["进阶功能", "优先支持", "50GB存储", "API访问"]', 1, 2),
('pro', '专业版', '适合企业用户', '["全部功能", "24/7支持", "500GB存储", "高级API", "自定义集成"]', 1, 3);

-- 基础版价格
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price) VALUES 
(1, 'MONTH', 1, 99.00, 89.00),
(1, 'QUARTER', 3, 297.00, 249.00),
(1, 'YEAR', 1, 1188.00, 899.00);

-- 进阶版价格
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price) VALUES 
(2, 'MONTH', 1, 199.00, 179.00),
(2, 'QUARTER', 3, 597.00, 499.00),
(2, 'YEAR', 1, 2388.00, 1799.00);

-- 专业版价格
INSERT INTO package_prices (package_id, billing_cycle, cycle_count, original_price, sale_price) VALUES 
(3, 'MONTH', 1, 399.00, 359.00),
(3, 'QUARTER', 3, 1197.00, 999.00),
(3, 'YEAR', 1, 4788.00, 3599.00);

-- 虎皮椒支付方式
INSERT INTO payment_methods (
    method_code, method_name, method_type, provider, 
    icon_url, description, api_config, webhook_config, 
    min_amount, max_amount, status, priority, sort_order
) VALUES (
    'xunhupay_auto', 
    '支付宝/微信支付', 
    'GATEWAY', 
    'xunhupay',
    'https://example.com/icons/xunhupay.png',
    '支持支付宝和微信支付，自动适配PC和手机端',
    '{"app_id": "your_xunhupay_app_id", "app_secret": "your_xunhupay_app_secret", "api_url": "https://api.xunhupay.com/payment/do.html"}',
    '{"notify_url": "/payment/notify/xunhupay", "verify_sign": true}',
    0.01, 50000.00, 1, 100, 1
);

-- 示例优惠码
INSERT INTO discount_codes (
    code, name, type, value, usage_type, max_usage, min_amount, 
    applicable_packages, start_time, end_time, status
) VALUES 
('WELCOME20', '新用户20%优惠', 'PERCENTAGE', 20.00, 'SINGLE', 1, 50.00, 
 NULL, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1),
('SAVE50', '满200减50', 'FIXED', 50.00, 'MULTIPLE', 1000, 200.00, 
 NULL, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1); 