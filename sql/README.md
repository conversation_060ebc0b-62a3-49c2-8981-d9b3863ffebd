# 安全事件系统数据库更新说明

## 更新内容

我们对安全事件系统进行了重构，使用枚举类型来规范化安全事件的分类和管理。数据库表也进行了相应的修改，主要包括：

1. 修改 `security_events` 表，使用 ENUM 类型替代原来的整数类型
2. 添加 `event_type_name` 字段，用于存储事件类型的名称
3. 更新索引，提高查询效率

## 更新文件

- `security_events_update.sql`: 用于更新现有数据库表的脚本
- `security_events_create.sql`: 用于创建新的数据库表的脚本（新环境使用）

## 执行步骤

### 对于现有环境

1. 备份数据库
   ```sql
   -- 备份原表数据
   CREATE TABLE security_events_backup AS SELECT * FROM security_events;
   ```

2. 执行更新脚本
   ```bash
   mysql -u username -p database_name < security_events_update.sql
   ```

### 对于新环境

1. 直接执行创建脚本
   ```bash
   mysql -u username -p database_name < security_events_create.sql
   ```

## 数据迁移注意事项

1. 执行更新脚本前，请确保已备份原表数据
2. 更新脚本会自动将原有的整数类型数据转换为对应的枚举值
3. 如果有自定义的事件类型，可能需要手动更新 `event_type_name` 字段

## 枚举类型对应关系

### 事件级别 (event_level)

| 原值 | 新值 | 描述 |
|------|------|------|
| 1    | INFO | 信息 |
| 2    | WARNING | 警告 |
| 3    | DANGER | 危险 |
| 4    | CRITICAL | 严重 |

### 事件状态 (status)

| 原值 | 新值 | 描述 |
|------|------|------|
| 1    | PENDING | 待处理 |
| 2    | HANDLED | 已处理 |
| 3    | IGNORED | 已忽略 |
| 4    | AUTO_RESOLVED | 自动解决 | 