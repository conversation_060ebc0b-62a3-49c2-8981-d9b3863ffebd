/*
 Navicat Premium Dump SQL

 Source Server         : chatgtp-测试服务器
 Source Server Type    : MySQL
 Source Server Version : 80405 (8.4.5)
 Source Host           : *************:3306
 Source Schema         : chatgpt

 Target Server Type    : MySQL
 Target Server Version : 80405 (8.4.5)
 File Encoding         : 65001

 Date: 18/06/2025 17:26:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for discount_code_usage
-- ----------------------------
DROP TABLE IF EXISTS `discount_code_usage`;
CREATE TABLE `discount_code_usage` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `discount_code_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `order_id` bigint NOT NULL,
  `discount_amount` decimal(10,2) NOT NULL,
  `used_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `idx_discount_code_id` (`discount_code_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_used_at` (`used_at`),
  CONSTRAINT `discount_code_usage_ibfk_1` FOREIGN KEY (`discount_code_id`) REFERENCES `discount_codes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `discount_code_usage_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `discount_code_usage_ibfk_3` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠码使用记录表';

-- ----------------------------
-- Table structure for discount_codes
-- ----------------------------
DROP TABLE IF EXISTS `discount_codes`;
CREATE TABLE `discount_codes` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠码名称',
  `type` enum('FIXED','PERCENTAGE') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '固定金额/百分比',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `usage_type` enum('SINGLE','MULTIPLE') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '一码一用/一码多用',
  `max_usage` int DEFAULT '1' COMMENT '最大使用次数',
  `used_count` int DEFAULT '0' COMMENT '已使用次数',
  `min_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最小使用金额',
  `max_discount` decimal(10,2) DEFAULT NULL COMMENT '最大优惠金额(百分比折扣用)',
  `applicable_packages` json DEFAULT NULL COMMENT '适用套餐ID数组，null表示全部',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_start_end_time` (`start_time`,`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠码表';

-- ----------------------------
-- Table structure for login_logs
-- ----------------------------
DROP TABLE IF EXISTS `login_logs`;
CREATE TABLE `login_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `location` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `isp` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网络运营商',
  `login_type` tinyint DEFAULT NULL COMMENT '1:密码登录 2:记住我登录 3:微信扫码 4:GitHub OAuth 5:Google OAuth 6:双因子登录',
  `login_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'password' COMMENT 'password/wechat/github/google/oauth',
  `third_party_provider` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方登录提供商（预留）',
  `login_result` tinyint DEFAULT NULL COMMENT '1:成功 2:密码错误 3:账号锁定 4:设备异常 5:地域异常 6:频率限制 7:第三方授权失败 8:账号未绑定',
  `failure_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `satoken_value` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Sa-Token值',
  `session_timeout` bigint DEFAULT NULL COMMENT '会话超时时间(秒)',
  `oauth_state` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'OAuth状态参数',
  `oauth_code` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'OAuth授权码',
  `third_party_user_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方平台用户ID',
  `risk_score` int DEFAULT '0' COMMENT '本次登录风险评分',
  `security_actions` json DEFAULT NULL COMMENT '触发的安全动作',
  `client_version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端版本',
  `api_version` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API版本',
  `referrer` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源页面',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_email` (`email`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_ip` (`ip_address`),
  KEY `idx_result` (`login_result`),
  KEY `idx_login_method` (`login_method`),
  KEY `idx_third_party_provider` (`third_party_provider`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_composite_user_time` (`user_id`,`created_at` DESC),
  KEY `idx_composite_security` (`login_result`,`risk_score`,`created_at`),
  CONSTRAINT `login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL,
  `package_id` bigint NOT NULL,
  `package_price_id` bigint NOT NULL,
  `discount_code_id` bigint DEFAULT NULL COMMENT '使用的优惠码ID',
  `original_amount` decimal(10,2) NOT NULL COMMENT '原价',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `final_amount` decimal(10,2) NOT NULL COMMENT '最终金额',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'CNY',
  `billing_cycle` enum('DAY','MONTH','QUARTER','YEAR') COLLATE utf8mb4_unicode_ci NOT NULL,
  `cycle_count` int NOT NULL,
  `status` enum('PENDING','PAID','CANCELLED','REFUNDED','PARTIAL_REFUNDED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `order_type` enum('NEW','RENEWAL','UPGRADE') COLLATE utf8mb4_unicode_ci DEFAULT 'NEW',
  `metadata` json DEFAULT NULL COMMENT '订单元数据',
  `expired_at` timestamp NULL DEFAULT NULL COMMENT '订单过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `package_id` (`package_id`),
  KEY `package_price_id` (`package_price_id`),
  KEY `discount_code_id` (`discount_code_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_expired_at` (`expired_at`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`),
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`package_price_id`) REFERENCES `package_prices` (`id`),
  CONSTRAINT `orders_ibfk_4` FOREIGN KEY (`discount_code_id`) REFERENCES `discount_codes` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- ----------------------------
-- Table structure for package_prices
-- ----------------------------
DROP TABLE IF EXISTS `package_prices`;
CREATE TABLE `package_prices` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `package_id` bigint NOT NULL,
  `billing_cycle` enum('DAY','MONTH','QUARTER','YEAR') COLLATE utf8mb4_unicode_ci NOT NULL,
  `cycle_count` int DEFAULT '1' COMMENT '周期数量，如3个月则为3',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `sale_price` decimal(10,2) NOT NULL COMMENT '售价',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'CNY',
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_package_cycle` (`package_id`,`billing_cycle`,`cycle_count`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_billing_cycle` (`billing_cycle`),
  CONSTRAINT `package_prices_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐价格表';

-- ----------------------------
-- Table structure for packages
-- ----------------------------
DROP TABLE IF EXISTS `packages`;
CREATE TABLE `packages` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套餐名称(basic/plus/pro)',
  `display_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '显示名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '套餐描述',
  `features` json DEFAULT NULL COMMENT '功能特性列表',
  `status` tinyint DEFAULT '1' COMMENT '1:启用 0:禁用',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_package_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐表';

-- ----------------------------
-- Table structure for payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `method_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式代码',
  `method_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式名称',
  `method_type` enum('GATEWAY','ALIPAY','WECHAT','UNIONPAY','BANK') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付类型',
  `provider` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付提供商(xunhupay, alipay, wechat)',
  `icon_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标URL',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述信息',
  `api_config` json NOT NULL COMMENT '支付API配置',
  `webhook_config` json DEFAULT NULL COMMENT '回调配置',
  `extra_config` json DEFAULT NULL COMMENT '额外配置参数',
  `min_amount` decimal(10,2) DEFAULT '0.01' COMMENT '最小支付金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '最大支付金额',
  `status` tinyint DEFAULT '1' COMMENT '1:启用 0:禁用',
  `priority` int DEFAULT '0' COMMENT '优先级，数字越大优先级越高',
  `sort_order` int DEFAULT '0' COMMENT '显示排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `method_code` (`method_code`),
  UNIQUE KEY `uk_method_code` (`method_code`),
  KEY `idx_provider` (`provider`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式表';

-- ----------------------------
-- Table structure for payments
-- ----------------------------
DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `payment_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付流水号',
  `order_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `payment_method_id` bigint NOT NULL COMMENT '支付方式ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'CNY',
  `trade_order_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商户订单号(发给支付平台)',
  `transaction_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付平台交易号',
  `platform_order_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方平台订单号',
  `payment_url` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付跳转URL',
  `qr_code_url` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二维码URL',
  `status` enum('PENDING','SUCCESS','FAILED','CANCELLED','EXPIRED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `failure_reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因',
  `callback_data` json DEFAULT NULL COMMENT '支付回调原始数据',
  `client_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `paid_at` timestamp NULL DEFAULT NULL,
  `expired_at` timestamp NULL DEFAULT NULL COMMENT '支付过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payment_no` (`payment_no`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `order_id` (`order_id`),
  KEY `payment_method_id` (`payment_method_id`),
  KEY `idx_trade_order_id` (`trade_order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_3` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- ----------------------------
-- Table structure for refunds
-- ----------------------------
DROP TABLE IF EXISTS `refunds`;
CREATE TABLE `refunds` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_id` bigint NOT NULL,
  `payment_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `refund_amount` decimal(10,2) NOT NULL,
  `refund_reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `refund_type` enum('FULL','PARTIAL') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','SUCCESS','FAILED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `platform_refund_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付平台退款单号',
  `callback_data` json DEFAULT NULL COMMENT '退款回调数据',
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `refund_no` (`refund_no`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `order_id` (`order_id`),
  KEY `payment_id` (`payment_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `refunds_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `refunds_ibfk_2` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `refunds_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- ----------------------------
-- Table structure for satoken_sessions
-- ----------------------------
DROP TABLE IF EXISTS `satoken_sessions`;
CREATE TABLE `satoken_sessions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `token_value` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Sa-Token值',
  `login_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录ID(用户ID)',
  `login_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT 'login' COMMENT '登录类型',
  `device_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备类型标识',
  `user_id` bigint NOT NULL,
  `device_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `login_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'password' COMMENT '登录方式：password/wechat/github/google',
  `login_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_activity_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `timeout_seconds` bigint DEFAULT NULL COMMENT 'Token超时时间(秒)',
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `status` tinyint DEFAULT '1' COMMENT '1:活跃 2:过期 3:手动下线 4:异常下线',
  `trust_level` int DEFAULT '0',
  `risk_score` int DEFAULT '0',
  `security_flags` json DEFAULT NULL COMMENT '安全标记位',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_value` (`token_value`),
  KEY `idx_token_value` (`token_value`),
  KEY `idx_login_id` (`login_id`),
  KEY `idx_user_device` (`user_id`,`device_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_last_activity` (`last_activity_at`),
  KEY `idx_login_method` (`login_method`),
  CONSTRAINT `satoken_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for security_events
-- ----------------------------
DROP TABLE IF EXISTS `security_events`;
CREATE TABLE `security_events` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `event_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件类型代码',
  `event_type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件类型名称',
  `event_level` enum('INFO','WARNING','DANGER','CRITICAL') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件级别: INFO(1),WARNING(2),DANGER(3),CRITICAL(4)',
  `event_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `event_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `user_id` bigint DEFAULT NULL,
  `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `session_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `login_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '触发事件的登录方式',
  `third_party_provider` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '相关第三方提供商',
  `event_data` json DEFAULT NULL COMMENT '事件详细数据',
  `triggered_rules` json DEFAULT NULL COMMENT '触发的规则',
  `status` enum('PENDING','HANDLED','IGNORED','AUTO_RESOLVED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING' COMMENT '事件状态: PENDING(1),HANDLED(2),IGNORED(3),AUTO_RESOLVED(4)',
  `handled_by` bigint DEFAULT NULL COMMENT '处理人',
  `handled_at` timestamp NULL DEFAULT NULL,
  `handle_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '处理备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_event_type_name` (`event_type_name`),
  KEY `idx_event_level` (`event_level`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_login_method` (`login_method`),
  KEY `idx_third_party_provider` (`third_party_provider`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `security_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安全事件表';

-- ----------------------------
-- Table structure for subscription_history
-- ----------------------------
DROP TABLE IF EXISTS `subscription_history`;
CREATE TABLE `subscription_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `subscription_id` bigint NOT NULL,
  `order_id` bigint NOT NULL,
  `action` enum('CREATE','RENEW','UPGRADE','DOWNGRADE','CANCEL') COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_package_id` bigint DEFAULT NULL COMMENT '原套餐ID(升级降级时)',
  `to_package_id` bigint NOT NULL,
  `from_end_time` timestamp NULL DEFAULT NULL COMMENT '原订阅结束时间',
  `to_end_time` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `subscription_id` (`subscription_id`),
  KEY `order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `subscription_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subscription_history_ibfk_2` FOREIGN KEY (`subscription_id`) REFERENCES `user_subscriptions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subscription_history_ibfk_3` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅历史表';

-- ----------------------------
-- Table structure for user_devices
-- ----------------------------
DROP TABLE IF EXISTS `user_devices`;
CREATE TABLE `user_devices` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `device_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'visitorId from FingerprintJS',
  `device_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SHA-256 of device components',
  `device_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户自定义设备名称',
  `device_type` enum('desktop','mobile','tablet','unknown') COLLATE utf8mb4_unicode_ci DEFAULT 'unknown',
  `platform` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `browser` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器',
  `screen_resolution` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `timezone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_trusted` tinyint(1) DEFAULT '0' COMMENT '是否为受信任设备',
  `trust_level` int DEFAULT '0' COMMENT '信任度评分: 0-100',
  `trust_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '信任原因',
  `first_login_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `login_count` int DEFAULT '0',
  `last_location` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录地理位置',
  `location_history` json DEFAULT NULL COMMENT '位置历史记录',
  `risk_score` int DEFAULT '0' COMMENT '风险评分: 0-100',
  `risk_factors` json DEFAULT NULL COMMENT '风险因素列表',
  `status` tinyint DEFAULT '1' COMMENT '1:正常 2:异常 3:锁定 4:删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_device` (`user_id`,`device_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_trusted` (`is_trusted`),
  KEY `idx_last_login` (`last_login_at`),
  KEY `idx_trust_level` (`trust_level`),
  KEY `idx_status` (`status`),
  CONSTRAINT `user_devices_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for user_subscriptions
-- ----------------------------
DROP TABLE IF EXISTS `user_subscriptions`;
CREATE TABLE `user_subscriptions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `package_id` bigint NOT NULL,
  `order_id` bigint NOT NULL COMMENT '首次订阅的订单ID',
  `status` enum('ACTIVE','EXPIRED','CANCELLED') COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE',
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NOT NULL,
  `auto_renewal` tinyint DEFAULT '0' COMMENT '是否自动续费',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_subscription` (`user_id`),
  KEY `package_id` (`package_id`),
  KEY `order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_end_time` (`end_time`),
  CONSTRAINT `user_subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_subscriptions_ibfk_2` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`),
  CONSTRAINT `user_subscriptions_ibfk_3` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅表';

-- ----------------------------
-- Table structure for user_third_party_accounts
-- ----------------------------
DROP TABLE IF EXISTS `user_third_party_accounts`;
CREATE TABLE `user_third_party_accounts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '关联用户ID',
  `provider` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '第三方平台：wechat/github/google/qq等',
  `provider_user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '第三方平台用户ID',
  `provider_username` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方平台用户名',
  `provider_email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方平台邮箱',
  `provider_avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方平台头像',
  `bind_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后使用该方式登录时间',
  `login_count` int DEFAULT '0' COMMENT '使用该方式登录次数',
  `provider_profile` json DEFAULT NULL COMMENT '第三方平台完整用户信息',
  `status` tinyint DEFAULT '1' COMMENT '1:正常 2:解绑 3:异常',
  `is_primary` tinyint(1) DEFAULT '0' COMMENT '是否为主要登录方式',
  `access_token` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '刷新令牌',
  `token_expires_at` timestamp NULL DEFAULT NULL COMMENT '令牌过期时间',
  `scope` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_user` (`provider`,`provider_user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_provider_user_id` (`provider_user_id`),
  KEY `idx_provider_email` (`provider_email`),
  KEY `idx_bind_time` (`bind_time`),
  KEY `idx_last_login_time` (`last_login_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `user_third_party_accounts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱，第三方登录用户可能为空',
  `nickname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `real_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码哈希，第三方登录用户可为空',
  `user_type` tinyint DEFAULT '1' COMMENT '1:普通注册 2:第三方登录 3:混合账号',
  `primary_login_type` enum('password','wechat','github','google','oauth') COLLATE utf8mb4_unicode_ci DEFAULT 'password' COMMENT '主要登录方式',
  `account_source` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'registration' COMMENT '账号来源：registration/wechat/github/google等',
  `status` tinyint DEFAULT '1' COMMENT '1:正常 2:锁定 3:禁用 4:注销',
  `email_verified` tinyint(1) DEFAULT '0' COMMENT '邮箱是否已验证',
  `phone_verified` tinyint(1) DEFAULT '0' COMMENT '手机是否已验证',
  `locked_until` timestamp NULL DEFAULT NULL COMMENT '锁定截止时间',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_login_device_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_login_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'password' COMMENT '最后登录方式：password/wechat/github/google',
  `security_level` tinyint DEFAULT '1' COMMENT '1:普通 2:敏感 3:高危',
  `two_factor_enabled` tinyint(1) DEFAULT '0',
  `login_notification_enabled` tinyint(1) DEFAULT '1' COMMENT '登录通知开关',
  `profile_public` tinyint(1) DEFAULT '0' COMMENT '个人资料是否公开',
  `allow_third_party_bind` tinyint(1) DEFAULT '1' COMMENT '是否允许绑定第三方账号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` bigint DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_primary_login_type` (`primary_login_type`),
  KEY `idx_last_login` (`last_login_at`),
  KEY `idx_security_level` (`security_level`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SET FOREIGN_KEY_CHECKS = 1;
