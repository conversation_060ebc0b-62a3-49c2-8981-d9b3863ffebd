-- 修改security_events表以支持枚举类型
-- 1. 备份原表数据
CREATE TABLE security_events_backup AS SELECT * FROM security_events;

-- 2. 修改event_level字段类型，使用ENUM类型
ALTER TABLE security_events 
MODIFY COLUMN event_level ENUM('INFO', 'WARNING', 'DANGER', 'CRITICAL') NOT NULL COMMENT '事件级别: INFO(1),WARNING(2),DANGER(3),CRITICAL(4)';

-- 3. 修改status字段类型，使用ENUM类型
ALTER TABLE security_events 
MODIFY COLUMN status ENUM('PENDING', 'HANDLED', 'IGNORED', 'AUTO_RESOLVED') NOT NULL DEFAULT 'PENDING' COMMENT '事件状态: PENDING(1),HANDLED(2),IGNORED(3),AUTO_RESOLVED(4)';

-- 4. 添加event_type_name字段，用于存储事件类型的名称
ALTER TABLE security_events 
ADD COLUMN event_type_name VARCHAR(50) AFTER event_type COMMENT '事件类型名称';

-- 5. 添加索引
ALTER TABLE security_events 
ADD INDEX idx_event_type_name (event_type_name);

-- 6. 更新现有数据的event_level (如果有数据)
UPDATE security_events SET 
  event_level = CASE 
    WHEN event_level = 1 THEN 'INFO'
    WHEN event_level = 2 THEN 'WARNING'
    WHEN event_level = 3 THEN 'DANGER'
    WHEN event_level = 4 THEN 'CRITICAL'
    ELSE 'INFO'
  END;

-- 7. 更新现有数据的status (如果有数据)
UPDATE security_events SET 
  status = CASE 
    WHEN status = 1 THEN 'PENDING'
    WHEN status = 2 THEN 'HANDLED'
    WHEN status = 3 THEN 'IGNORED'
    ELSE 'PENDING'
  END;

-- 8. 更新现有数据的event_type_name (根据event_type填充)
-- 这里只是示例，实际需要根据你的SecurityEventType枚举中定义的映射关系来更新
UPDATE security_events SET 
  event_type_name = CASE 
    WHEN event_type = 'DEVICE_ID_MISMATCH' THEN '设备ID不匹配'
    WHEN event_type = 'DEVICE_HASH_MISMATCH' THEN '设备指纹变化'
    WHEN event_type = 'DEVICE_SECURITY_VIOLATION' THEN '设备安全验证失败'
    WHEN event_type = 'BLACKLISTED_DEVICE_ACCESS' THEN '拉黑设备访问'
    WHEN event_type = 'DEVICE_ANOMALY' THEN '设备异常'
    WHEN event_type = 'IP_CHANGE_TRUSTED_DEVICE' THEN 'IP变化(信任设备)'
    WHEN event_type = 'IP_CHANGE_UNTRUSTED_DEVICE' THEN 'IP变化(非信任设备)'
    WHEN event_type = 'SUSPICIOUS_IP_ACCESS' THEN '可疑IP访问'
    WHEN event_type = 'LOGIN_FAILED' THEN '登录失败'
    WHEN event_type = 'MULTIPLE_LOGIN_FAILURES' THEN '多次登录失败'
    WHEN event_type = 'ACCOUNT_LOCKED' THEN '账户锁定'
    WHEN event_type = 'ACCOUNT_DISABLED' THEN '账户禁用'
    WHEN event_type = 'PASSWORD_RESET' THEN '密码重置'
    WHEN event_type = 'SENSITIVE_OPERATION' THEN '敏感操作'
    WHEN event_type = 'UNAUTHORIZED_ACCESS' THEN '越权访问'
    WHEN event_type = 'PERMISSION_CHANGE' THEN '权限变更'
    WHEN event_type = 'API_ABUSE' THEN 'API滥用'
    WHEN event_type = 'SYSTEM_ANOMALY' THEN '系统异常'
    ELSE '其他安全事件'
  END;

-- 9. 添加注释
COMMENT ON TABLE security_events IS '安全事件表'; 