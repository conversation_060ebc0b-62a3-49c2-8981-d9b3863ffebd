-- 创建新的security_events表，支持枚举类型
DROP TABLE IF EXISTS `security_events`;
CREATE TABLE `security_events` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `event_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件类型代码',
  `event_type_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件类型名称',
  `event_level` ENUM('INFO', 'WARNING', 'DANGER', 'CRITICAL') NOT NULL COMMENT '事件级别: INFO(1),WARNING(2),DANGER(3),CRITICAL(4)',
  `event_title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `event_description` text COLLATE utf8mb4_unicode_ci,
  `user_id` bigint DEFAULT NULL,
  `device_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `session_token` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `login_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '触发事件的登录方式',
  `third_party_provider` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '相关第三方提供商',
  `event_data` json DEFAULT NULL COMMENT '事件详细数据',
  `triggered_rules` json DEFAULT NULL COMMENT '触发的规则',
  `status` ENUM('PENDING', 'HANDLED', 'IGNORED', 'AUTO_RESOLVED') NOT NULL DEFAULT 'PENDING' COMMENT '事件状态: PENDING(1),HANDLED(2),IGNORED(3),AUTO_RESOLVED(4)',
  `handled_by` bigint DEFAULT NULL COMMENT '处理人',
  `handled_at` timestamp NULL DEFAULT NULL,
  `handle_notes` text COLLATE utf8mb4_unicode_ci COMMENT '处理备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_event_type_name` (`event_type_name`),
  KEY `idx_event_level` (`event_level`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_login_method` (`login_method`),
  KEY `idx_third_party_provider` (`third_party_provider`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `security_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安全事件表'; 