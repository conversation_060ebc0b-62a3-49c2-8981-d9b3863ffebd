# Maven构建产物
target/
.mvn/
mvnw
mvnw.cmd

# IDE相关文件
.idea/
*.iml
.vscode/
.settings/
.classpath
.project
.factorypath
.apt_generated
.springBeans
.sts4-cache

# 编译文件
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# 日志文件
*.log
logs/
log/
*.log.*

# 临时文件
*.tmp
*.temp
.tmp/
temp/

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 缓存文件
*.cache
.gradle/
build/

# 数据库文件
*.db
*.sqlite
*.sqlite3
*.h2.db

# 配置文件中的敏感信息
application-prod.yml
application-prod.yaml
application-local.yml
application-local.yaml
*.env
.env.*

# Node.js相关（如果有前端资源）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 测试覆盖率报告
coverage/
*.lcov
.nyc_output

# 文档生成文件
*.pdf
*.docx
site/
_site/

# 操作系统生成的文件
*.pid
*.seed
*.pid.lock

# 备份文件
*.bak
*.backup
*.orig
*.swp
*.swo
*~

# 压缩文件
*.gz
*.bz2
*.xz
*.lzma
*.cab
*.msi
*.msm
*.msp

# JetBrains IDE
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf
.idea/**/contentModel.xml
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Spring Boot相关
spring.log
.attach_pid*

# Docker相关
Dockerfile.prod
docker-compose.prod.yml
.dockerignore

# 其他
*.pid
*.seed
*.log
.gradle
/build/ 